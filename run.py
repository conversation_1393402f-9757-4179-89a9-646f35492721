#!/usr/bin/env python3
"""
新航发航空数据采集器启动脚本
支持开发和生产环境
"""
import os
import sys
import uvicorn
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.config import get_settings

def main():
    """主启动函数"""
    settings = get_settings()
    
    print("=" * 60)
    print("🚀 新航发航空数据采集器")
    print("=" * 60)
    print(f"📦 版本: {settings.APP_VERSION}")
    print(f"🌐 地址: http://{settings.HOST}:{settings.PORT}")
    print(f"📚 API文档: http://{settings.HOST}:{settings.PORT}/docs")
    print(f"🔧 调试模式: {'开启' if settings.DEBUG else '关闭'}")
    print("=" * 60)
    
    # 确保日志目录存在
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # 启动服务器
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=True,
        log_level=settings.LOG_LEVEL.lower(),
        access_log=True,
    )

if __name__ == "__main__":
    main()
