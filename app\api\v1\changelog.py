"""
更新日志API路由
"""

from fastapi import APIRouter, HTTPException
from typing import Dict, Any, List
import os
from pathlib import Path
from app.core.logging import logger

router = APIRouter()


@router.get("/latest-version")
async def get_latest_version() -> Dict[str, Any]:
    """
    获取最新版本信息

    返回最新版本号和更新内容，用于版本更新通知
    """
    try:
        # 获取项目根目录下的changelog.txt文件
        changelog_path = Path("changelog.txt")

        if not changelog_path.exists():
            logger.warning("changelog.txt 文件不存在")
            raise HTTPException(
                status_code=404,
                detail="更新日志文件不存在"
            )

        # 读取文件内容
        with open(changelog_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 解析更新日志内容
        changelog_data = parse_changelog_content(content)

        if not changelog_data:
            raise HTTPException(
                status_code=404,
                detail="未找到版本信息"
            )

        # 获取最新版本（第一个版本）
        latest_version = changelog_data[0]

        logger.info(f"成功获取最新版本信息: {latest_version['version']}")

        return {
            "success": True,
            "data": {
                "version": latest_version["version"],
                "changes": latest_version["changes"]
            },
            "message": "最新版本信息获取成功"
        }

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"获取最新版本信息异常: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取最新版本信息失败: {str(e)}"
        )


@router.get("/changelog")
async def get_changelog() -> Dict[str, Any]:
    """
    获取更新日志内容
    
    返回解析后的更新日志数据，包含版本号和更新条目
    """
    try:
        # 获取项目根目录下的changelog.txt文件
        changelog_path = Path("changelog.txt")
        
        if not changelog_path.exists():
            logger.warning("changelog.txt 文件不存在")
            raise HTTPException(
                status_code=404,
                detail="更新日志文件不存在"
            )
        
        # 读取文件内容
        with open(changelog_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 解析更新日志内容
        changelog_data = parse_changelog_content(content)
        
        logger.info("成功获取更新日志内容")
        
        return {
            "success": True,
            "data": changelog_data,
            "message": "更新日志获取成功"
        }
        
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"获取更新日志异常: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取更新日志失败: {str(e)}"
        )


def parse_changelog_content(content: str) -> List[Dict[str, Any]]:
    """
    解析更新日志内容
    
    Args:
        content: 原始文件内容
        
    Returns:
        解析后的版本数据列表
    """
    versions = []
    current_version = None
    
    lines = content.strip().split('\n')
    
    for line in lines:
        line = line.strip()
        
        # 跳过空行
        if not line:
            continue
            
        # 检查是否是版本号行（以 ## 开头）
        if line.startswith('## '):
            # 如果有当前版本，先保存
            if current_version:
                versions.append(current_version)
            
            # 开始新版本
            version_number = line[3:].strip()  # 移除 "## "
            current_version = {
                "version": version_number,
                "changes": []
            }
            
        # 检查是否是更新条目（以 - 开头）
        elif line.startswith('- ') and current_version:
            change_text = line[2:].strip()  # 移除 "- "
            
            # 解析更新类型和内容
            change_type = "Other"
            change_content = change_text
            
            if change_text.startswith('Added:'):
                change_type = "Added"
                change_content = change_text[6:].strip()
            elif change_text.startswith('Fixed:'):
                change_type = "Fixed"
                change_content = change_text[6:].strip()
            elif change_text.startswith('Changed:'):
                change_type = "Changed"
                change_content = change_text[8:].strip()
            elif change_text.startswith('Improved:'):
                change_type = "Improved"
                change_content = change_text[9:].strip()
            elif change_text.startswith('Removed:'):
                change_type = "Removed"
                change_content = change_text[8:].strip()
            
            current_version["changes"].append({
                "type": change_type,
                "content": change_content
            })
    
    # 添加最后一个版本
    if current_version:
        versions.append(current_version)
    
    return versions
