/**
 * 友商数据采集模块样式
 * 基于Tabler.io框架的扩展样式
 */





/* 左右布局样式 */
.scraper-layout {
    min-height: 600px;
    /* 确保左右两列顶部对齐 */
    align-items: flex-start;
}

/* 查询卡片样式 - 参考需求凭证模块 */
.query-card {
    /* 移除默认的margin-bottom，确保与右侧卡片顶部对齐 */
    margin-bottom: 0;
}

.query-card .card-body {
    padding: 1rem 1.5rem;
}

.query-card .form-label {
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.query-card .form-control,
.query-card .form-select {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

.query-card .form-text {
    font-size: 0.75rem;
    line-height: 1.2;
    margin-top: 0.25rem;
}

/* 平台选择器样式（固定下拉菜单） */
.platform-selector .dropdown-toggle {
    font-weight: 500;
    text-align: left;
    position: relative;
    transition: all 0.2s ease;
}

.platform-selector .dropdown-toggle::after {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
}

.platform-selector .dropdown-toggle:hover {
    background-color: #e7f1ff;
    border-color: #0d6efd;
    color: #0d6efd;
}

.platform-selector .dropdown-menu {
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid #dee2e6;
    max-height: 300px;
    overflow-y: auto;
}

.platform-selector .dropdown-item {
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    cursor: pointer;
}

.platform-selector .dropdown-item:hover {
    background-color: #f8f9fa;
    color: #0d6efd;
}

.platform-selector .dropdown-item.active {
    background-color: #0d6efd;
    color: white;
}

.platform-selector .dropdown-item.active .ti-circle {
    color: white !important;
}

.platform-selector .dropdown-item:not(.active) .ti-circle-filled {
    display: none;
}

.platform-selector .dropdown-item.active .ti-circle {
    display: none;
}

.platform-selector .dropdown-item.active .ti-circle-filled {
    display: inline-block;
    color: white;
}

/* 图标样式优化 */
.platform-selector .dropdown-item .ti {
    font-size: 0.875rem;
    flex-shrink: 0;
}

/* 结果卡片样式 - 确保与左侧查询卡片顶部对齐 */
.results-card {
    /* 移除margin-top，确保与左侧卡片顶部对齐 */
    margin-top: 0;
    margin-bottom: 0;
}

/* 优化操作按钮样式 - 参考需求凭证模块 */
.btn-list .btn {
    min-width: 32px;
    min-height: 32px;
    padding: 0.375rem 0.5rem;
}

.btn-list .btn .icon {
    margin: 0;
}









/* 加载状态样式（已移除全局覆盖层，保留行级加载状态） */

/* 表格行选择样式 */
.table tbody tr {
    transition: background-color 0.15s ease;
}

.table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.table tbody tr.table-active {
    background-color: rgba(13, 110, 253, 0.1);
}

.table tbody tr.table-active:hover {
    background-color: rgba(13, 110, 253, 0.15);
}

/* 平台选择器在不同屏幕尺寸下的优化 */
@media (max-width: 576px) {
    .platform-selector .dropdown-toggle {
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }

    .platform-selector .dropdown-item {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }
}

/* 响应式设计 - 参考需求凭证模块 */
@media (max-width: 768px) {
    /* 移动端恢复垂直布局，确保卡片间距一致 */
    .scraper-layout {
        flex-direction: column;
    }

    .query-card {
        margin-bottom: 1rem;
    }

    .results-card {
        margin-top: 0;
    }

    .query-card .card-body {
        padding: 0.75rem 1rem;
    }

    .btn-list .btn {
        min-width: 44px;
        min-height: 44px;
    }

    /* 加载状态响应式调整 */
    .loading-placeholder {
        padding: 6px 8px;
        gap: 4px;
    }

    .loading-spinner {
        font-size: 12px;
    }

    .loading-text {
        font-size: 12px;
    }

    /* 移动端表格优化 */
    .table-responsive {
        font-size: 0.875rem;
    }

    .table th,
    .table td {
        padding: 0.5rem 0.25rem;
        vertical-align: middle;
    }

    /* 移动端隐藏部分列以节省空间 */
    .table th:nth-child(4),
    .table td:nth-child(4),
    .table th:nth-child(5),
    .table td:nth-child(5) {
        display: none;
    }

    /* 移动端按钮优化 */
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    /* 移动端分页优化 */
    .pagination-container {
        padding: 0.75rem;
    }

    .pagination-status {
        font-size: 0.75rem;
    }

    .pagination-size-selector {
        font-size: 0.75rem;
    }
}

/* 中等屏幕尺寸优化 */
@media (min-width: 769px) and (max-width: 991px) {
    .scraper-layout {
        align-items: flex-start;
    }

    .query-card,
    .results-card {
        margin-top: 0;
        margin-bottom: 0;
    }
}

/* 大屏幕优化 */
@media (min-width: 992px) {
    .scraper-layout {
        align-items: flex-start;
    }

    .query-card,
    .results-card {
        margin-top: 0;
        margin-bottom: 0;
    }
}

/* 简洁优雅的加载状态样式 */
.loading-row {
    background: rgba(var(--tblr-primary-rgb), 0.02);
    transition: all 0.3s ease-out;
}

.sku-cell {
    background: white;
    font-weight: 500;
    position: relative;
    z-index: 2;
}

.loading-cell {
    min-height: 40px;
    vertical-align: middle;
    background: rgba(248, 249, 250, 0.5);
    position: relative;
}

.loading-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    padding: 8px 12px;
    min-height: 32px;
}

.loading-spinner {
    animation: spin 1s linear infinite;
    font-size: 14px;
    color: var(--tblr-primary);
    opacity: 0.7;
}

.loading-text {
    font-size: 13px;
    color: var(--tblr-muted);
    font-weight: 400;
}

/* 加载完成后的样式 */
.updated-row {
    background: white;
    animation: rowUpdateFade 0.4s ease-out;
}

.updated-cell {
    background: white;
    animation: cellUpdateFade 0.3s ease-out;
}

/* 旋转动画 */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 动画效果 */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes rowUpdateFade {
    from {
        background: rgba(var(--tblr-success-rgb), 0.1);
        transform: translateY(-2px);
    }
    to {
        background: white;
        transform: translateY(0);
    }
}

@keyframes cellUpdateFade {
    from {
        background: rgba(var(--tblr-success-rgb), 0.05);
        opacity: 0.8;
        transform: scale(0.98);
    }
    to {
        background: white;
        opacity: 1;
        transform: scale(1);
    }
}

/* 表格行过渡效果 */
.table tbody tr {
    transition: all 0.2s ease-in-out;
}

.table tbody tr td {
    transition: all 0.2s ease-in-out;
}

/* 错误状态样式 */
.error-row {
    background: rgba(var(--tblr-danger-rgb), 0.02);
}

.error-cell {
    background: rgba(var(--tblr-danger-rgb), 0.05);
}

/* 成功状态样式 */
.success-row {
    background: rgba(var(--tblr-success-rgb), 0.02);
}

.success-cell {
    background: rgba(var(--tblr-success-rgb), 0.03);
}

/* ========== UI状态控制样式 ========== */

/* 禁用状态的表单控件样式 */
.form-control-disabled {
    background-color: #f8f9fa !important;
    border-color: #dee2e6 !important;
    color: #6c757d !important;
    cursor: not-allowed !important;
    opacity: 0.7;
    transition: all 0.2s ease;
}

.form-control-disabled:focus {
    box-shadow: none !important;
    border-color: #dee2e6 !important;
}

/* 禁用状态的按钮样式 */
.btn.disabled,
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
    transition: all 0.2s ease;
}

/* 加载状态的按钮样式 */
.btn-loading {
    position: relative;
    color: transparent !important;
    pointer-events: none;
    transition: all 0.2s ease;
}

.btn-loading .spinner-border {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 1rem;
    height: 1rem;
    border-width: 0.125rem;
    color: currentColor;
    opacity: 1;
}

/* 禁用状态的下拉菜单样式 */
.dropdown-toggle.disabled,
.dropdown-toggle:disabled {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    color: #6c757d;
    cursor: not-allowed;
    opacity: 0.6;
    transition: all 0.2s ease;
}

.dropdown-item.disabled {
    color: #6c757d;
    pointer-events: none;
    background-color: transparent;
    opacity: 0.6;
    cursor: not-allowed;
}

/* 平台选择器禁用状态 */
.platform-selector .dropdown-toggle.disabled:hover {
    background-color: #f8f9fa !important;
    border-color: #dee2e6 !important;
    color: #6c757d !important;
}

/* 清空按钮禁用状态 */
.btn-outline-secondary.disabled,
.btn-outline-secondary:disabled {
    color: #6c757d;
    background-color: transparent;
    border-color: #dee2e6;
    opacity: 0.6;
}

/* 加载状态的视觉反馈 */
.loading-state {
    position: relative;
    overflow: hidden;
}

.loading-state::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    animation: shimmer 1.5s infinite;
    z-index: 1;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* 禁用状态的文本域样式 */
textarea.form-control-disabled {
    resize: none;
    background-image: none;
}

/* 禁用状态的图标样式 */
.disabled .ti,
.disabled .icon {
    opacity: 0.5;
}

/* 响应式禁用状态 */
@media (max-width: 768px) {
    .form-control-disabled {
        font-size: 0.875rem;
    }

    .btn.disabled,
    .btn:disabled {
        min-height: 44px;
    }

    .dropdown-toggle.disabled {
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }
}

/* 加载状态的表格样式增强 */
.table.loading-state tbody tr {
    pointer-events: none;
    opacity: 0.7;
}

.table.loading-state tbody tr td {
    background-color: #f8f9fa;
    color: #6c757d;
}

/* 禁用状态的工具提示 */
.disabled[title]:hover::after,
.disabled[data-bs-title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 1000;
}

/* 分页容器样式 */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background-color: #f8fafc;
    border-top: 1px solid #e2e8f0;
    border-radius: 0 0 0.5rem 0.5rem;
}

.pagination-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.pagination-status {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

.pagination-size-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.pagination-size-selector .form-select {
    width: auto;
    min-width: 80px;
}

.pagination-controls {
    display: flex;
    align-items: center;
}

/* 响应式分页样式 */
@media (max-width: 768px) {
    .pagination-container {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .pagination-info {
        justify-content: center;
        flex-wrap: wrap;
    }

    .pagination-controls {
        justify-content: center;
    }
}


