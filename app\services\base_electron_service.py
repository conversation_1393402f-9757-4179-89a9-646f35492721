"""
BaseElectronService - 统一的ELECTRON平台服务基类
整合重复的认证逻辑、HTTP请求模式、错误处理机制，并支持IP地址注入功能
"""

import aiohttp
import asyncio
from typing import Dict, Any, Optional
from abc import ABC
from app.core.logging import logger


class BaseElectronService(ABC):
    """
    ELECTRON平台服务基类
    
    提供统一的：
    - 请求头管理
    - HTTP请求方法
    - 认证处理
    - IP地址注入
    - 错误处理
    """
    
    def __init__(self):
        """初始化基础服务"""
        # 基础请求头配置
        self.base_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Content-Type': 'application/json;charset=UTF-8',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        }
    
    def _build_request_headers(
        self, 
        access_token: str, 
        client_ip: Optional[str] = None,
        additional_headers: Optional[Dict[str, str]] = None
    ) -> Dict[str, str]:
        """
        构建请求头
        
        Args:
            access_token: ELECTRON平台访问令牌
            client_ip: 客户端IP地址
            additional_headers: 额外的请求头
            
        Returns:
            完整的请求头字典
        """
        # 复制基础请求头
        headers = self.base_headers.copy()
        
        # 添加认证头
        headers['Authorization'] = f'Bearer {access_token}'
        
        # 添加IP相关请求头
        if client_ip and client_ip != "unknown":
            headers['X-Forwarded-For'] = client_ip
            headers['X-Real-IP'] = client_ip
            logger.debug(f"添加IP请求头: X-Forwarded-For={client_ip}, X-Real-IP={client_ip}")
        
        # 添加额外请求头
        if additional_headers:
            headers.update(additional_headers)
        
        return headers
    
    async def _make_request(
        self,
        method: str,
        url: str,
        access_token: str,
        client_ip: Optional[str] = None,
        json_data: Optional[Dict[str, Any]] = None,
        additional_headers: Optional[Dict[str, str]] = None,
        timeout: int = 30
    ) -> Dict[str, Any]:
        """
        统一的HTTP请求方法
        
        Args:
            method: HTTP方法 (GET, POST, PUT, DELETE)
            url: 请求URL
            access_token: ELECTRON平台访问令牌
            client_ip: 客户端IP地址
            json_data: JSON请求体数据
            additional_headers: 额外的请求头
            timeout: 请求超时时间（秒）
            
        Returns:
            API响应数据字典
            
        Raises:
            Exception: 请求失败时抛出异常
        """
        try:
            # 构建请求头
            headers = self._build_request_headers(
                access_token=access_token,
                client_ip=client_ip,
                additional_headers=additional_headers
            )
            
            logger.debug(f"发送{method}请求到: {url}")
            if client_ip:
                logger.debug(f"客户端IP: {client_ip}")
            
            # 发送异步HTTP请求
            timeout_config = aiohttp.ClientTimeout(total=timeout)
            async with aiohttp.ClientSession(timeout=timeout_config) as session:
                async with session.request(
                    method=method,
                    url=url,
                    json=json_data,
                    headers=headers,
                    ssl=False
                ) as response:
                    
                    # 记录响应状态
                    logger.debug(f"API响应状态: {response.status}")
                    
                    # 处理HTTP状态码
                    if response.status == 200:
                        response_data = await response.json()
                        logger.debug(f"API响应成功")
                        return response_data
                    elif response.status == 401:
                        error_text = await response.text()
                        logger.error(f"认证失败 - HTTP {response.status}: {error_text}")
                        raise Exception('认证失败，请重新登录')
                    elif response.status == 403:
                        error_text = await response.text()
                        logger.error(f"权限不足 - HTTP {response.status}: {error_text}")
                        raise Exception('没有访问权限')
                    else:
                        error_text = await response.text()
                        logger.error(f"API请求失败 - HTTP {response.status}: {error_text}")
                        raise Exception(f'服务器错误 (HTTP {response.status})')
                        
        except asyncio.TimeoutError:
            logger.error(f"请求超时 - URL: {url}")
            raise Exception('请求超时，请稍后重试')
        except aiohttp.ClientError as e:
            logger.error(f"网络错误 - URL: {url}, 错误: {str(e)}")
            raise Exception(f'网络连接错误: {str(e)}')
        except Exception as e:
            logger.error(f"请求异常 - URL: {url}, 错误: {str(e)}", exc_info=True)
            raise
    
    async def _post_request(
        self,
        url: str,
        access_token: str,
        client_ip: Optional[str] = None,
        json_data: Optional[Dict[str, Any]] = None,
        additional_headers: Optional[Dict[str, str]] = None,
        timeout: int = 30
    ) -> Dict[str, Any]:
        """POST请求的便捷方法"""
        return await self._make_request(
            method="POST",
            url=url,
            access_token=access_token,
            client_ip=client_ip,
            json_data=json_data,
            additional_headers=additional_headers,
            timeout=timeout
        )
    
    async def _get_request(
        self,
        url: str,
        access_token: str,
        client_ip: Optional[str] = None,
        additional_headers: Optional[Dict[str, str]] = None,
        timeout: int = 30
    ) -> Dict[str, Any]:
        """GET请求的便捷方法"""
        return await self._make_request(
            method="GET",
            url=url,
            access_token=access_token,
            client_ip=client_ip,
            additional_headers=additional_headers,
            timeout=timeout
        )

    async def _upload_file_request(
        self,
        url: str,
        access_token: str,
        form_data: Any,  # aiohttp.FormData
        client_ip: Optional[str] = None,
        timeout: int = 60
    ) -> Dict[str, Any]:
        """
        文件上传请求的便捷方法

        Args:
            url: 请求URL
            access_token: ELECTRON平台访问令牌
            form_data: multipart/form-data表单数据
            client_ip: 客户端IP地址
            timeout: 请求超时时间（秒）

        Returns:
            API响应数据字典

        Raises:
            Exception: 请求失败时抛出异常
        """
        try:
            # 构建请求头（不包含Content-Type，让aiohttp自动设置multipart/form-data）
            headers = self._build_request_headers(
                access_token=access_token,
                client_ip=client_ip
            )
            # 移除Content-Type，让aiohttp自动处理multipart/form-data
            if 'Content-Type' in headers:
                del headers['Content-Type']

            logger.debug(f"发送文件上传请求到: {url}")
            if client_ip:
                logger.debug(f"客户端IP: {client_ip}")

            # 发送异步HTTP请求
            timeout_config = aiohttp.ClientTimeout(total=timeout)
            async with aiohttp.ClientSession(timeout=timeout_config) as session:
                async with session.post(
                    url=url,
                    data=form_data,
                    headers=headers,
                    ssl=False
                ) as response:

                    # 记录响应状态
                    logger.debug(f"文件上传API响应状态: {response.status}")

                    # 处理HTTP状态码
                    if response.status == 200:
                        response_data = await response.json()
                        logger.debug(f"文件上传API响应成功")
                        return response_data
                    elif response.status == 401:
                        error_text = await response.text()
                        logger.error(f"认证失败 - HTTP {response.status}: {error_text}")
                        raise Exception('认证失败，请重新登录')
                    elif response.status == 403:
                        error_text = await response.text()
                        logger.error(f"权限不足 - HTTP {response.status}: {error_text}")
                        raise Exception('没有文件上传权限')
                    else:
                        error_text = await response.text()
                        logger.error(f"文件上传请求失败 - HTTP {response.status}: {error_text}")
                        raise Exception(f'服务器错误 (HTTP {response.status})')

        except asyncio.TimeoutError:
            logger.error(f"文件上传请求超时 - URL: {url}")
            raise Exception('文件上传请求超时，请稍后重试')
        except aiohttp.ClientError as e:
            logger.error(f"文件上传网络错误 - URL: {url}, 错误: {str(e)}")
            raise Exception(f'网络连接错误: {str(e)}')
        except Exception as e:
            logger.error(f"文件上传请求异常 - URL: {url}, 错误: {str(e)}", exc_info=True)
            raise

    async def _download_file_request(
        self,
        url: str,
        access_token: str,
        json_data: Optional[Dict[str, Any]] = None,
        client_ip: Optional[str] = None,
        timeout: int = 60
    ) -> bytes:
        """
        文件下载请求的便捷方法

        Args:
            url: 请求URL
            access_token: ELECTRON平台访问令牌
            json_data: JSON请求体数据
            client_ip: 客户端IP地址
            timeout: 请求超时时间（秒）

        Returns:
            文件的字节内容

        Raises:
            Exception: 请求失败时抛出异常
        """
        try:
            # 构建请求头
            headers = self._build_request_headers(
                access_token=access_token,
                client_ip=client_ip
            )

            logger.debug(f"发送文件下载请求到: {url}")
            if client_ip:
                logger.debug(f"客户端IP: {client_ip}")

            # 发送异步HTTP请求
            timeout_config = aiohttp.ClientTimeout(total=timeout)
            async with aiohttp.ClientSession(timeout=timeout_config) as session:
                async with session.post(
                    url=url,
                    json=json_data,
                    headers=headers,
                    ssl=False
                ) as response:

                    # 记录响应状态
                    logger.debug(f"文件下载API响应状态: {response.status}")

                    # 处理HTTP状态码
                    if response.status == 200:
                        # 检查响应类型
                        content_type = response.headers.get('content-type', '')
                        logger.debug(f"响应Content-Type: {content_type}")

                        if 'application/json' in content_type:
                            # 如果返回JSON，可能是错误信息
                            response_data = await response.json()
                            logger.error(f"下载失败，API返回JSON: {response_data}")
                            raise Exception(f"下载失败: {response_data.get('msg', '未知错误')}")

                        # 读取文件内容
                        file_content = await response.read()
                        logger.debug(f"成功下载文件，文件大小: {len(file_content)} 字节")
                        return file_content

                    elif response.status == 401:
                        error_text = await response.text()
                        logger.error(f"认证失败 - HTTP {response.status}: {error_text}")
                        raise Exception('认证失败，请重新登录')
                    elif response.status == 403:
                        error_text = await response.text()
                        logger.error(f"权限不足 - HTTP {response.status}: {error_text}")
                        raise Exception('没有文件下载权限')
                    else:
                        # 尝试解析错误响应
                        try:
                            error_data = await response.json()
                            error_msg = error_data.get('msg', f'HTTP {response.status}')
                            logger.error(f"下载API请求失败: HTTP {response.status}, 错误: {error_data}")
                            raise Exception(f'下载请求失败: {error_msg}')
                        except:
                            # 如果不是JSON响应，读取文本
                            error_text = await response.text()
                            logger.error(f"下载API请求失败: HTTP {response.status}, 响应: {error_text}")
                            raise Exception(f'下载请求失败: HTTP {response.status} - {error_text}')

        except asyncio.TimeoutError:
            logger.error(f"文件下载请求超时 - URL: {url}")
            raise Exception('文件下载请求超时，请稍后重试')
        except aiohttp.ClientError as e:
            logger.error(f"文件下载网络错误 - URL: {url}, 错误: {str(e)}")
            raise Exception(f'网络连接错误: {str(e)}')
        except Exception as e:
            logger.error(f"文件下载请求异常 - URL: {url}, 错误: {str(e)}", exc_info=True)
            raise
    
    def _process_api_response(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理API响应的通用方法
        
        Args:
            response_data: API原始响应数据
            
        Returns:
            标准化的响应数据
        """
        try:
            # 检查响应状态
            if response_data.get('success') is False:
                error_message = response_data.get('message', '未知错误')
                logger.error(f"API返回错误: {error_message}")
                return {
                    'success': False,
                    'message': error_message,
                    'data': []
                }
            
            # 检查响应代码
            response_code = response_data.get('code')
            if response_code and response_code != 200:
                error_message = response_data.get('message', f'API错误代码: {response_code}')
                logger.error(f"API响应代码异常: {response_code}, 消息: {error_message}")
                return {
                    'success': False,
                    'message': error_message,
                    'data': []
                }
            
            # 成功响应
            return {
                'success': True,
                'message': response_data.get('message', '操作成功'),
                'data': response_data.get('data', [])
            }
            
        except Exception as e:
            logger.error(f"处理API响应异常: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': f'数据处理错误: {str(e)}',
                'data': []
            }
