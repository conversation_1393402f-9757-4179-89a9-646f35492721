{% extends "base.html" %}

{% block title %}{{ app_name }} - 品牌库采集{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="/static/css/modules/brand.css?ver={{ app_version }}">
<!-- Cropper.js CSS - 使用多个备用CDN -->
<link rel="stylesheet" href="https://unpkg.com/cropperjs@1.6.1/dist/cropper.min.css" />
{% endblock %}

{% block content %}
<div class="page-header d-print-none">
    <div class="container-xl">
        <div class="row g-2 align-items-center">
            <div class="col">
                <!-- 面包屑导航 -->
                <div class="page-pretitle">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="/">控制台</a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">品牌库</li>
                        </ol>
                    </nav>
                </div>
                <h2 class="page-title">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler me-2 text-success" width="32" height="32" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M12 3l8 4.5l0 9l-8 4.5l-8 -4.5l0 -9l8 -4.5"></path>
                        <path d="M12 12l8 -4.5"></path>
                        <path d="M12 12l0 9"></path>
                        <path d="M12 12l-8 -4.5"></path>
                    </svg>
                    品牌库
                </h2>
                <div class="text-muted mt-1">
                    查询和管理品牌库数据，支持品牌编号、名称搜索和状态筛选
                </div>
            </div>
            <div class="col-auto ms-auto d-print-none">
                <div class="btn-list">
                    <a href="/" class="btn btn-outline-secondary">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M5 12l14 0"></path>
                            <path d="M5 12l6 6"></path>
                            <path d="M5 12l6 -6"></path>
                        </svg>
                        返回控制台
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="page-body">
    <div class="container-xl">
        <!-- 登录提示 -->
        <div class="row" id="login-prompt">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <h3>需要登录</h3>
                        <p class="text-muted">请先登录到电子超市以使用品牌库采集功能</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#login-modal">
                            立即登录
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- ELECTRON权限不足提示 -->
        <div class="row" id="electron-access-denied" style="display: none;">
            <div class="col-12">
                <div class="card border-warning">
                    <div class="card-header bg-warning-lt">
                        <h3 class="card-title text-warning">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M12 9v2m0 4v.01"></path>
                                <path d="M5 19h14a2 2 0 0 0 1.84 -2.75l-7.1 -12.25a2 2 0 0 0 -3.5 0l-7.1 12.25a2 2 0 0 0 1.75 2.75"></path>
                            </svg>
                            电子超市平台权限不足
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <h4>无法访问品牌库采集</h4>
                                <p class="text-muted mb-3">
                                    您已成功登录到商城，但电子超市系统登录失败。
                                    品牌库采集功能需要电子超市平台权限才能正常使用。
                                </p>
                                <div class="alert alert-info">
                                    <h5 class="alert-title">可能的原因：</h5>
                                    <ul class="mb-0">
                                        <li>您的账号没有电子超市平台访问权限</li>
                                        <li>电子超市系统暂时不可用</li>
                                        <li>网络连接问题导致电子超市登录失败</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-auto">
                                <div class="avatar avatar-lg bg-warning-lt text-warning">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="32" height="32" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"></path>
                                        <path d="M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6"></path>
                                        <path d="M3 3l18 18"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4">
                            <button class="btn btn-warning me-2" data-bs-toggle="modal" data-bs-target="#login-modal">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"></path>
                                    <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"></path>
                                </svg>
                                重新登录
                            </button>
                            <a href="/" class="btn btn-outline-secondary">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M5 12l14 0"></path>
                                    <path d="M5 12l6 6"></path>
                                    <path d="M5 12l6 -6"></path>
                                </svg>
                                返回控制台
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 模块内容 -->
        <div id="main-content" style="display: none;">
            <!-- 查询输入卡片 -->
            <div class="card mb-3 query-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"></path>
                            <path d="M21 21l-6 -6"></path>
                        </svg>
                        <span id="query-card-title">品牌库查询条件</span>
                    </h5>
                    <div class="card-actions">
                        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#add-brand-modal" id="add-brand-btn">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M12 5l0 14"></path>
                                <path d="M5 12l14 0"></path>
                            </svg>
                            新增品牌
                        </button>
                    </div>
                </div>
                <div class="card-body">
                        <!-- 查询类型选择 -->
                        <div class="mb-4">
                            <label class="form-label">查询类型</label>
                            <div class="btn-group w-100" role="group" aria-label="查询类型选择">
                                <input type="radio" class="btn-check" name="query_type" id="query-type-status" value="status" checked>
                                <label class="btn btn-outline-primary" for="query-type-status">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M7 4v16l13 0v-16"></path>
                                        <path d="M7 4l4 0l0 4"></path>
                                        <path d="M11 4l4 0"></path>
                                        <path d="M15 4l0 4l-4 0"></path>
                                        <path d="M7 8l13 0"></path>
                                        <path d="M7 12l13 0"></path>
                                        <path d="M7 16l13 0"></path>
                                    </svg>
                                    品牌查询
                                </label>

                                <input type="radio" class="btn-check" name="query_type" id="query-type-approval" value="approval">
                                <label class="btn btn-outline-warning" for="query-type-approval">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2"></path>
                                        <path d="M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z"></path>
                                        <path d="M9 12l2 2l4 -4"></path>
                                    </svg>
                                    审核查询
                                </label>
                            </div>
                        </div>

                    <form id="brand-form" class="compact-form">
                        <!-- 品牌查询模式字段 -->
                        <div id="brand-mode-fields">
                            <div class="row">
                                <div class="col-md-4 mb-2">
                                    <label class="form-label">品牌编号</label>
                                    <input type="text" class="form-control" name="code"
                                        placeholder="如：026865">
                                    <div class="form-text">模糊匹配</div>
                                </div>
                                <div class="col-md-4 mb-2">
                                    <label class="form-label">品牌名称</label>
                                    <input type="text" class="form-control" name="name"
                                        placeholder="如：重庆煌奕">
                                    <div class="form-text">模糊搜索</div>
                                </div>
                                <div class="col-md-4 mb-2">
                                    <label class="form-label">启用状态</label>
                                    <select class="form-select" name="status">
                                        <option value="">请选择状态</option>
                                        <option value="ENABLE">启用</option>
                                        <option value="DISABLE">禁用</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 审核查询模式字段 -->
                        <div id="approval-mode-fields" style="display: none;">
                            <div class="row">
                                <div class="col-md-6 mb-2">
                                    <label class="form-label">品牌名称</label>
                                    <input type="text" class="form-control" name="approval_name"
                                        placeholder="如：重庆煌奕">
                                    <div class="form-text">模糊搜索</div>
                                </div>
                                <div class="col-md-6 mb-2">
                                    <label class="form-label">审核状态</label>
                                    <select class="form-select" name="approval_status">
                                        <option value="">全部状态</option>
                                        <option value="APPROVE">待审核</option>
                                        <option value="REJECT">驳回</option>
                                        <option value="APPROVED">审核通过</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 按钮区域 -->
                        <div class="d-flex gap-2 mt-3">
                            <button type="submit" class="btn btn-primary" id="brand-submit-btn">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"></path>
                                    <path d="M21 21l-6 -6"></path>
                                </svg>
                                <span id="submit-btn-text">查询品牌库</span>
                            </button>

                        </div>
                    </form>
                </div>
            </div>

            <!-- 筛选器面板 -->
            <div id="data-filter-container"></div>

            <!-- 数据结果卡片 -->
            <div class="card results-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M3 3m0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z"></path>
                            <path d="M7 7h10"></path>
                            <path d="M7 12h10"></path>
                            <path d="M7 17h10"></path>
                        </svg>
                        品牌库查询结果
                    </h5>
                    <div class="card-actions">
                        <span class="badge bg-secondary" id="brand-count">0 条记录</span>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div id="brand-results"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 新增品牌模态框 -->
<div class="modal modal-blur fade" id="add-brand-modal" tabindex="-1" role="dialog" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M12 5l0 14"></path>
                        <path d="M5 12l14 0"></path>
                    </svg>
                    新增品牌
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- 表单完成进度指示器 -->
                <div class="progress-indicator mb-3" id="brand-form-progress">
                    <div class="progress-steps">
                        <div class="progress-step" data-step="basic" id="progress-step-basic">
                            <div class="step-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="14" height="14" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M9 7m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0"></path>
                                    <path d="M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2"></path>
                                    <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                                    <path d="M21 21v-2a4 4 0 0 0 -3 -3.85"></path>
                                </svg>
                            </div>
                            <div class="step-label">基本信息</div>
                        </div>
                        <div class="progress-line" id="progress-line-1"></div>
                        <div class="progress-step" data-step="logo" id="progress-step-logo">
                            <div class="step-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="14" height="14" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M15 8h.01"></path>
                                    <path d="M3 6a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3v-12z"></path>
                                    <path d="M3 16l5 -5c.928 -.893 2.072 -.893 3 0l5 5"></path>
                                    <path d="M14 14l1 -1c.928 -.893 2.072 -.893 3 0l3 3"></path>
                                </svg>
                            </div>
                            <div class="step-label">品牌LOGO</div>
                        </div>
                        <div class="progress-line" id="progress-line-2"></div>
                        <div class="progress-step" data-step="trademark" id="progress-step-trademark">
                            <div class="step-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="14" height="14" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M14 3v4a1 1 0 0 0 1 1h4"></path>
                                    <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"></path>
                                    <path d="M9 9l1 0"></path>
                                    <path d="M9 13l6 0"></path>
                                    <path d="M9 17l6 0"></path>
                                </svg>
                            </div>
                            <div class="step-label">商标截图</div>
                        </div>
                    </div>
                </div>

                <form id="add-brand-form" novalidate>
                    <div class="row">
                        <!-- 基础信息 -->
                        <div class="col-12">
                            <h6 class="mb-3 text-muted">基础信息</h6>
                        </div>

                        <!-- 第一行：3列布局 -->
                        <!-- 品牌中文名称 -->
                        <div class="col-lg-4 col-md-6 mb-3">
                            <label class="form-label">品牌中文名称</label>
                            <input type="text" class="form-control" id="brand-name-cn" name="brand_name_cn" placeholder="请输入品牌中文名称">
                            <div class="invalid-feedback"></div>
                        </div>

                        <!-- 品牌英文名称 -->
                        <div class="col-lg-4 col-md-6 mb-3">
                            <label class="form-label">品牌英文名称</label>
                            <input type="text" class="form-control" id="brand-name-en" name="brand_name_en" placeholder="请输入品牌英文名称">
                            <div class="invalid-feedback"></div>
                        </div>

                        <!-- 品牌原产地 -->
                        <div class="col-lg-4 col-md-12 mb-3">
                            <label class="form-label required">品牌原产地</label>
                            <div class="btn-group w-100" role="group" aria-label="品牌原产地选择">
                                <input type="radio" class="btn-check" name="brand_origin" id="brand-origin-domestic" value="DOMESTIC" autocomplete="off" required>
                                <label class="btn btn-outline-primary" for="brand-origin-domestic">国产</label>

                                <input type="radio" class="btn-check" name="brand_origin" id="brand-origin-import" value="CINCINNATI" autocomplete="off" required>
                                <label class="btn btn-outline-primary" for="brand-origin-import">进口</label>
                            </div>
                            <div class="invalid-feedback"></div>
                        </div>

                        <!-- 第二行：2列布局 -->
                        <!-- 商标持有人 -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label required">商标持有人</label>
                            <input type="text" class="form-control" id="trademark-holder" name="trademark_holder" placeholder="请输入商标持有人" required>
                            <div class="invalid-feedback"></div>
                        </div>

                        <!-- 国际分类 -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">国际分类</label>
                            <div class="international-classification-container">
                                <div class="classification-dropdown" id="classification-dropdown">
                                    <div class="classification-input-container">
                                        <input type="text" class="form-control classification-search" id="classification-search" placeholder="搜索分类或点击选择..." readonly>
                                        <div class="classification-actions">
                                            <button type="button" class="btn btn-sm btn-outline-secondary" id="classification-clear" title="清除选择">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="14" height="14" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                    <path d="M18 6l-12 12"></path>
                                                    <path d="M6 6l12 12"></path>
                                                </svg>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-primary" id="classification-toggle" title="展开/收起">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="14" height="14" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                    <path d="M6 9l6 6l6 -6"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="classification-dropdown-menu" id="classification-dropdown-menu" style="display: none;">
                                        <div class="classification-dropdown-header">
                                            <input type="text" class="form-control form-control-sm" id="classification-filter" placeholder="输入关键词搜索...">
                                            <div class="classification-actions-header">
                                                <button type="button" class="btn btn-sm btn-outline-primary" id="classification-select-all">全选</button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary" id="classification-clear-all">清空</button>
                                            </div>
                                        </div>
                                        <div class="classification-dropdown-list" id="classification-dropdown-list">
                                            <!-- 分类选项将通过JavaScript动态生成 -->
                                        </div>
                                    </div>
                                </div>
                                <select class="form-select d-none" id="international-classification" name="international_classification" multiple>
                                    <!-- 隐藏的原始select，用于表单提交 -->
                                </select>
                            </div>
                            <div class="invalid-feedback"></div>
                        </div>

                        <!-- 品牌LOGO上传 -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">品牌LOGO</label>
                            <div class="logo-upload-container" id="logo-upload-container">
                                <!-- 上传界面 -->
                                <div class="logo-upload-area" id="logo-upload-area">
                                    <div class="logo-upload-content">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-lg text-muted mb-2" width="48" height="48" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                            <path d="M15 8h.01"></path>
                                            <path d="M3 6a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3v-12z"></path>
                                            <path d="M3 16l5 -5c.928 -.893 2.072 -.893 3 0l5 5"></path>
                                            <path d="M14 14l1 -1c.928 -.893 2.072 -.893 3 0l3 3"></path>
                                        </svg>
                                        <p class="mb-1">拖拽图片到此处或点击上传</p>
                                        <p class="text-muted small">支持 JPG、PNG 格式，将自动进入裁剪模式</p>
                                    </div>
                                    <input type="file" id="logo-file-input" name="logo_file" accept="image/jpeg,image/png" style="display: none;">
                                </div>

                                <!-- 裁剪界面 -->
                                <div class="logo-crop-area" id="logo-crop-area" style="display: none;">
                                    <div class="logo-crop-container">
                                        <img id="logo-crop-image" src="" alt="待裁剪图片">
                                    </div>
                                    <div class="logo-crop-toolbar">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="btn-group btn-group-sm" role="group">
                                                <button type="button" class="btn btn-outline-secondary" id="logo-zoom-in" title="放大">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"></path>
                                                        <path d="M7 10l6 0"></path>
                                                        <path d="M10 7l0 6"></path>
                                                        <path d="M21 21l-6 -6"></path>
                                                    </svg>
                                                </button>
                                                <button type="button" class="btn btn-outline-secondary" id="logo-zoom-out" title="缩小">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"></path>
                                                        <path d="M7 10l6 0"></path>
                                                        <path d="M21 21l-6 -6"></path>
                                                    </svg>
                                                </button>
                                                <button type="button" class="btn btn-outline-secondary" id="logo-reset" title="重置">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"></path>
                                                        <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"></path>
                                                    </svg>
                                                </button>
                                            </div>
                                            <div class="logo-crop-info">
                                                <small class="text-muted">比例: 102:36</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="logo-crop-actions mt-2">
                                        <button type="button" class="btn btn-sm btn-secondary" id="logo-crop-cancel">取消</button>
                                        <button type="button" class="btn btn-sm btn-primary" id="logo-crop-confirm">确认裁剪</button>
                                    </div>
                                </div>

                                <!-- 预览界面 -->
                                <div class="logo-preview-area" id="logo-preview-area" style="display: none;">
                                    <div class="logo-preview-container">
                                        <img id="logo-preview-img" src="" alt="LOGO预览">
                                    </div>
                                    <div class="logo-preview-actions">
                                        <button type="button" class="btn btn-sm btn-outline-danger" id="logo-remove-btn">删除</button>
                                        <button type="button" class="btn btn-sm btn-outline-primary" id="logo-recrop-btn">重新裁剪</button>
                                        <button type="button" class="btn btn-sm btn-primary" id="logo-upload-btn">
                                            <span class="btn-text">上传LOGO</span>
                                            <span class="btn-spinner spinner-border spinner-border-sm" style="display: none;" role="status"></span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 商标网截图上传 -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">商标网截图</label>
                            <div class="trademark-upload-container" id="trademark-upload-container">
                                <!-- 上传界面 -->
                                <div class="trademark-upload-area" id="trademark-upload-area">
                                    <div class="trademark-upload-content">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-lg text-muted mb-2" width="48" height="48" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                            <path d="M15 8h.01"></path>
                                            <path d="M3 6a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3v-12z"></path>
                                            <path d="M3 16l5 -5c.928 -.893 2.072 -.893 3 0l5 5"></path>
                                            <path d="M14 14l1 -1c.928 -.893 2.072 -.893 3 0l3 3"></path>
                                        </svg>
                                        <p class="mb-1">拖拽图片到此处或点击上传</p>
                                        <p class="text-muted small">支持 JPG、PNG 格式</p>
                                    </div>
                                    <input type="file" id="trademark-file-input" name="trademark_file" accept="image/jpeg,image/png" style="display: none;">
                                </div>

                                <!-- 预览界面 -->
                                <div class="trademark-preview-area" id="trademark-preview-area" style="display: none;">
                                    <div class="trademark-preview-container">
                                        <img id="trademark-preview-img" src="" alt="商标网截图预览">
                                    </div>
                                    <div class="trademark-preview-actions">
                                        <button type="button" class="btn btn-sm btn-outline-danger" id="trademark-remove-btn">删除</button>
                                        <button type="button" class="btn btn-sm btn-primary" id="trademark-upload-btn">
                                            <span class="btn-text">上传截图</span>
                                            <span class="btn-spinner spinner-border spinner-border-sm" style="display: none;" role="status"></span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>




                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="add-brand-submit-btn" disabled>
                    <span class="btn-text">提交</span>
                    <span class="btn-spinner spinner-border spinner-border-sm" style="display: none;" role="status"></span>
                </button>
            </div>
        </div>
    </div>
</div>



{% endblock %}

{% block extra_js %}
<!-- Cropper.js JavaScript - 使用多个备用CDN -->
<script>
// 动态加载Cropper.js，支持多个备用CDN
(function() {
    const cropperCDNs = [
        'https://unpkg.com/cropperjs@1.6.1/dist/cropper.min.js',
        'https://cdn.jsdelivr.net/npm/cropperjs@1.6.1/dist/cropper.min.js',
        'https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.6.1/cropper.min.js'
    ];

    let currentCDN = 0;

    function loadCropper() {
        if (currentCDN >= cropperCDNs.length) {
            console.error('所有Cropper.js CDN都加载失败');
            return;
        }

        const script = document.createElement('script');
        script.src = cropperCDNs[currentCDN];
        script.onload = function() {
            console.log('Cropper.js 加载成功:', cropperCDNs[currentCDN]);
            // 触发自定义事件通知Cropper.js已加载
            window.dispatchEvent(new Event('cropperLoaded'));
        };
        script.onerror = function() {
            console.warn('Cropper.js CDN加载失败:', cropperCDNs[currentCDN]);
            currentCDN++;
            loadCropper();
        };
        document.head.appendChild(script);
    }

    loadCropper();
})();
</script>
<script src="/static/js/button-manager.js?ver={{ app_version }}"></script>
<script src="/static/js/pagination-manager.js?ver={{ app_version }}"></script>
<script src="/static/js/data-filter.js?ver={{ app_version }}"></script>
<script src="/static/js/modules/brand.js?ver={{ app_version }}"></script>
{% endblock %}
