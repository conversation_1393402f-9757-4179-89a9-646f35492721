"""
新航发航空数据采集器 - 主应用入口
FastAPI异步Web应用，支持跨平台运行
"""
import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
import uvicorn

from app.core.config import get_settings
from app.core.logging import setup_logging, get_logger
from app.api.v1 import api_router

# 初始化配置和日志
settings = get_settings()
setup_logging()
logger = get_logger(__name__)


class ProxyHeadersMiddleware(BaseHTTPMiddleware):
    """
    反向代理头部处理中间件
    处理X-Forwarded-Proto、X-Forwarded-Host等头部信息
    确保FastAPI在HTTPS反向代理环境下正确生成URL
    """

    async def dispatch(self, request: Request, call_next):
        # 处理X-Forwarded-Proto头部
        forwarded_proto = request.headers.get("X-Forwarded-Proto")
        if forwarded_proto:
            # 更新请求的scheme
            request.scope["scheme"] = forwarded_proto

        # 处理X-Forwarded-Host头部
        forwarded_host = request.headers.get("X-Forwarded-Host")
        if forwarded_host:
            # 更新请求的host
            request.scope["server"] = (forwarded_host, None)
            # 更新headers中的host
            request.scope["headers"] = [
                (name, value) if name != b"host" else (name, forwarded_host.encode())
                for name, value in request.scope["headers"]
            ]

        # 处理X-Forwarded-For头部（真实客户端IP）
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            # 取第一个IP作为真实客户端IP
            real_ip = forwarded_for.split(",")[0].strip()
            request.scope["client"] = (real_ip, 0)

        response = await call_next(request)
        return response


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("🚀 启动新航发航空数据采集器...")
    logger.info(f"🌐 服务地址: http://{settings.HOST}:{settings.PORT}")
    logger.info(f"📚 API文档: http://{settings.HOST}:{settings.PORT}/docs")
    logger.info("✅ 应用启动完成")
    
    yield
    
    # 关闭时执行
    logger.info("🛑 正在关闭应用...")
    logger.info("✅ 应用关闭完成")


# 创建FastAPI应用实例
app = FastAPI(
    title=settings.APP_NAME,
    description="基于FastAPI的航空数据采集Web应用，支持异步处理和多平台登录",
    version=settings.APP_VERSION,
    debug=settings.DEBUG,
    lifespan=lifespan,
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
)

# 添加反向代理头部处理中间件（必须在其他中间件之前添加）
app.add_middleware(ProxyHeadersMiddleware)

# 添加受信任主机中间件（可选，用于生产环境安全）
if not settings.DEBUG and hasattr(settings, 'TRUSTED_HOSTS'):
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=settings.TRUSTED_HOSTS
    )

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"] if settings.DEBUG else ["http://localhost:8000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件
app.mount("/static", StaticFiles(directory="app/static"), name="static")

# 设置模板
templates = Jinja2Templates(directory="app/templates")

# 包含API路由
app.include_router(api_router, prefix="/api/v1")


@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理器"""
    logger.error(f"HTTP异常: {exc.status_code} - {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "message": exc.detail,
            "status_code": exc.status_code
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    logger.error(f"未处理的异常: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "服务器内部错误",
            "status_code": 500
        }
    )


@app.get("/")
async def root(request: Request):
    """首页 - 重定向到需求凭证模块"""
    return templates.TemplateResponse(
        "dashboard.html",
        {
            "request": request,
            "app_name": settings.APP_NAME,
            "app_version": settings.APP_VERSION,
        }
    )

@app.get("/modules/xqpz")
async def xqpz_module(request: Request):
    """需求凭证查询模块页面"""
    return templates.TemplateResponse(
        "modules/xqpz.html",
        {
            "request": request,
            "app_name": settings.APP_NAME,
            "app_version": settings.APP_VERSION,
            "module_name": "需求凭证查询",
            "module_key": "xqpz",
            "current_module": "xqpz"
        }
    )

@app.get("/modules/xqd")
async def xqd_module(request: Request):
    """需求单查询模块页面"""
    return templates.TemplateResponse(
        "modules/xqd.html",
        {
            "request": request,
            "app_name": settings.APP_NAME,
            "app_version": settings.APP_VERSION,
            "module_name": "需求单查询",
            "module_key": "xqd",
            "current_module": "xqd"
        }
    )

@app.get("/modules/xqd/detail")
async def xqd_detail_page(request: Request, askSheetCode: str = "", askSheetId: str = ""):
    """需求单详情页面"""
    return templates.TemplateResponse(
        "modules/xqd-detail.html",
        {
            "request": request,
            "app_name": settings.APP_NAME,
            "app_version": settings.APP_VERSION,
            "module_name": "需求单详情",
            "module_key": "xqd-detail",
            "current_module": "xqd",
            "askSheetCode": askSheetCode,
            "askSheetId": askSheetId
        }
    )

@app.get("/modules/brand")
async def brand_module(request: Request):
    """品牌库采集模块页面"""
    return templates.TemplateResponse(
        "modules/brand.html",
        {
            "request": request,
            "app_name": settings.APP_NAME,
            "app_version": settings.APP_VERSION,
            "module_name": "品牌库采集",
            "module_key": "brand",
            "current_module": "brand"
        }
    )



@app.get("/modules/product")
async def product_status_module(request: Request):
    """商品状态查询模块页面"""
    return templates.TemplateResponse(
        "modules/product.html",
        {
            "request": request,
            "app_name": settings.APP_NAME,
            "app_version": settings.APP_VERSION,
            "module_name": "商品状态查询",
            "module_key": "product",
            "current_module": "product"
        }
    )

@app.get("/modules/product/detail")
async def product_detail_page(request: Request, productId: str = "", sku: str = ""):
    """商品详情页面"""
    return templates.TemplateResponse(
        "modules/product-detail.html",
        {
            "request": request,
            "app_name": settings.APP_NAME,
            "app_version": settings.APP_VERSION,
            "module_name": "商品详情",
            "module_key": "product-detail",
            "current_module": "product",
            "productId": productId,
            "sku": sku
        }
    )

@app.get("/scraper")
async def scraper_module(request: Request):
    """友商数据采集模块页面"""
    return templates.TemplateResponse(
        "scraper/scraper.html",
        {
            "request": request,
            "app_name": settings.APP_NAME,
            "app_version": settings.APP_VERSION,
            "module_name": "友商数据采集",
            "module_key": "scraper",
            "current_module": "scraper"
        }
    )


@app.get("/analytics")
async def analytics_module(request: Request):
    """数据分析模块页面"""
    return templates.TemplateResponse(
        "analytics.html",
        {
            "request": request,
            "app_name": settings.APP_NAME,
            "app_version": settings.APP_VERSION,
            "module_name": "数据分析",
            "module_key": "analytics",
            "current_module": "analytics"
        }
    )


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "app_name": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "message": "新航发航空数据采集器运行正常"
    }


@app.get("/debug/headers")
async def debug_headers(request: Request):
    """调试端点 - 显示请求头部信息（仅在调试模式下可用）"""
    if not settings.DEBUG:
        raise HTTPException(status_code=404, detail="Not found")

    return {
        "scheme": request.url.scheme,
        "host": request.url.hostname,
        "port": request.url.port,
        "path": request.url.path,
        "full_url": str(request.url),
        "headers": dict(request.headers),
        "client": request.client,
        "method": request.method,
        "proxy_info": {
            "x_forwarded_proto": request.headers.get("X-Forwarded-Proto"),
            "x_forwarded_host": request.headers.get("X-Forwarded-Host"),
            "x_forwarded_for": request.headers.get("X-Forwarded-For"),
            "x_forwarded_port": request.headers.get("X-Forwarded-Port"),
        },
        "static_url_example": request.url_for("static", path="css/custom.css")
    }


if __name__ == "__main__":
    # 直接运行时的配置
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower(),
    )
