"""
领先未来平台数据采集服务
参考现有平台实现，为领先未来平台(http://www.66123123.com/)提供完整的数据采集功能
使用统一Playwright管理器模块
"""
import asyncio
import random
from typing import Dict, List, Any
import requests
import aiohttp
from lxml import html
from app.core.logging import logger
from app.core.config import get_settings
from .playwright_manager import PlaywrightManager


class LxwlScraperService:
    """领先未来平台数据采集服务类"""
    
    def __init__(self):
        # 获取配置实例
        self.settings = get_settings()

        # 基础配置 - 领先未来平台URL模板
        self.base_url = "http://www.66123123.com/Goods/GoodsDetail?id={sku}"
        
        # XPath选择器配置
        self.xpath_product_name = '//div[@class="p-name"]/text()'
        self.xpath_product_unit = '//div[@class="p-price"]/span[4]/text()'
        self.xpath_product_price = '//div[@class="p-price"]/span[@class="price"]/text()'
        
        # 请求配置
        self.min_delay = 2.0
        self.max_delay = 3.0
        self.request_timeout = 30
        self.max_retries = 1  # 移除自动重试，仅保留手动重试功能
        self.concurrent_threads = 5  # 领先未来平台优化：5个并发线程
        
        # User-Agent配置
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ]
        
        # 初始化session
        self.session = requests.Session()
        self.semaphore = None

        # 初始化统一Playwright管理器
        self.playwright_manager = PlaywrightManager(
            platform_name="领先未来",
            base_domain=".66123123.com"
        )

        # 设置基础请求头
        self._setup_base_headers()
        
    def _setup_base_headers(self):
        """设置基础请求头"""
        self.session.headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        })
    
    def get_random_delay(self):
        """获取随机延迟时间"""
        return random.uniform(self.min_delay, self.max_delay)
    
    def get_random_user_agent(self):
        """获取随机User-Agent"""
        return random.choice(self.user_agents)

    def set_client_ip_headers(self, client_ip: str):
        """
        设置客户端IP相关请求头

        Args:
            client_ip: 客户端IP地址
        """
        if client_ip and client_ip != "unknown":
            self.session.headers.update({
                'X-Forwarded-For': client_ip,
                'X-Real-IP': client_ip
            })
            logger.debug(f"领先未来平台设置IP请求头: X-Forwarded-For={client_ip}, X-Real-IP={client_ip}")
    
    def _clean_field_value(self, value: str, field_type: str) -> str:
        """
        清理字段值，移除不必要的前缀文本
        Args:
            value: 原始字段值
            field_type: 字段类型 ('brand' 或 'model')
        Returns:
            清理后的字段值
        """
        if not value or value.strip() == '':
            return value
        
        cleaned_value = value.strip()
        
        # 清理品牌字段的"品牌："前缀
        if field_type == 'brand':
            prefixes_to_remove = ['品牌：', '品牌:', '品牌 :', '品牌 ：']
            for prefix in prefixes_to_remove:
                if cleaned_value.startswith(prefix):
                    cleaned_value = cleaned_value[len(prefix):].strip()
                    break
        
        # 清理规格参数字段的"商品型号："前缀
        elif field_type == 'model':
            prefixes_to_remove = ['商品型号：', '商品型号:', '商品型号 :', '商品型号 ：', '型号：', '型号:', '型号 :', '型号 ：']
            for prefix in prefixes_to_remove:
                if cleaned_value.startswith(prefix):
                    cleaned_value = cleaned_value[len(prefix):].strip()
                    break
        
        return cleaned_value if cleaned_value else value

    def _is_pure_number(self, text: str) -> bool:
        """
        检查文本是否为纯数字（包括整数和小数）
        Args:
            text: 要检查的文本
        Returns:
            如果是纯数字返回True，否则返回False
        """
        if not text or text.strip() == '':
            return False

        text = text.strip()

        # 检查是否为纯数字（整数）
        if text.isdigit():
            logger.debug(f"'{text}' 是纯整数")
            return True

        # 检查是否为小数
        try:
            float(text)
            logger.debug(f"'{text}' 是数字（包含小数）")
            return True
        except ValueError:
            logger.debug(f"'{text}' 不是数字")
            return False

    def _extract_brand_from_name(self, product_name: str) -> str:
        """
        从产品名称中提取品牌（智能过滤数字前缀）
        Args:
            product_name: 产品名称
        Returns:
            提取的品牌名称
        """
        if not product_name or product_name.strip() == '':
            logger.debug("产品名称为空，使用默认品牌：领先未来")
            return '领先未来'

        # 按空格分割产品名称
        parts = product_name.strip().split()
        if len(parts) == 0:
            logger.debug("产品名称分割后为空，使用默认品牌：领先未来")
            return '领先未来'

        # 检查第一个部分是否为纯数字
        first_part = parts[0].strip()

        if self._is_pure_number(first_part):
            logger.debug(f"检测到数字前缀：'{first_part}'，将其过滤")
            # 如果第一个部分是纯数字，使用第二个部分作为品牌
            if len(parts) >= 2:
                brand = parts[1].strip()
                logger.debug(f"过滤数字前缀后提取品牌：'{brand}'")
            else:
                # 如果只有一个数字部分，使用默认品牌
                brand = '领先未来'
                logger.debug("过滤数字前缀后无其他内容，使用默认品牌：领先未来")
        else:
            # 第一个部分不是纯数字，直接使用作为品牌
            brand = first_part
            logger.debug(f"第一个部分非数字，直接提取品牌：'{brand}'")

        # 应用字段清理
        cleaned_brand = self._clean_field_value(brand, 'brand')
        logger.debug(f"品牌字段清理：'{brand}' -> '{cleaned_brand}'")

        return cleaned_brand
    
    def _extract_model_from_name(self, product_name: str) -> str:
        """
        从产品名称中提取型号规格（智能过滤数字前缀）
        Args:
            product_name: 产品名称
        Returns:
            提取的型号规格
        """
        if not product_name or product_name.strip() == '':
            logger.debug("产品名称为空，返回默认型号：-")
            return '-'

        # 按空格分割产品名称
        parts = product_name.strip().split()
        if len(parts) == 0:
            logger.debug("产品名称分割后为空，返回默认型号：-")
            return '-'

        # 检查第一个部分是否为纯数字
        first_part = parts[0].strip()

        if self._is_pure_number(first_part):
            logger.debug(f"检测到数字前缀：'{first_part}'，调整型号提取索引")
            # 如果第一个部分是纯数字，过滤后重新分析
            # 原来的"第二个空格和第三个空格之间"变为"第一个空格和第二个空格之间"
            if len(parts) >= 3:
                # 过滤数字前缀后：parts[1]是品牌，parts[2]是型号
                model = parts[2].strip()
                logger.debug(f"过滤数字前缀后提取型号：'{model}'")
            elif len(parts) == 2:
                # 只有数字前缀和品牌，没有型号
                model = '-'
                logger.debug("过滤数字前缀后只有品牌，无型号信息")
            else:
                # 只有数字前缀，无其他信息
                model = '-'
                logger.debug("过滤数字前缀后无其他内容，返回默认型号")
        else:
            # 第一个部分不是纯数字，使用原有逻辑
            # 第一个部分是品牌，第二个部分是型号
            if len(parts) >= 2:
                model = parts[1].strip()
                logger.debug(f"无数字前缀，直接提取型号：'{model}'")
            else:
                # 只有一个部分（品牌），没有型号
                model = '-'
                logger.debug("只有品牌信息，无型号信息")

        # 应用字段清理
        cleaned_model = self._clean_field_value(model, 'model')
        logger.debug(f"型号字段清理：'{model}' -> '{cleaned_model}'")

        return cleaned_model
    
    def _parse_query_content(self, query_content: str) -> List[str]:
        """解析查询内容，提取SKU列表（保持用户输入顺序）"""
        if not query_content:
            return []

        # 支持多种分隔符：空格、逗号、换行符
        import re
        skus = re.split(r'[,\s\n]+', query_content.strip())
        # 过滤空字符串并去重，但保持输入顺序
        seen = set()
        unique_skus = []
        for sku in skus:
            sku = sku.strip()
            if sku and sku not in seen:
                seen.add(sku)
                unique_skus.append(sku)

        logger.debug(f"解析查询内容，提取到 {len(unique_skus)} 个SKU（保持输入顺序）: {unique_skus}")
        return unique_skus
    
    async def scrape_data(self, query_content: str, playwright_mode: bool = None) -> Dict[str, Any]:
        """
        采集领先未来平台数据
        Args:
            query_content: 查询内容（SKU列表）
            playwright_mode: 是否使用Playwright模式（已废弃，由配置文件控制）
        """
        try:
            # 从配置文件读取Playwright模式设置
            use_playwright = self.settings.LXWL_USE_PLAYWRIGHT
            logger.info(f"开始采集领先未来平台数据 - Playwright模式: {use_playwright}")

            skus = self._parse_query_content(query_content)

            if not skus:
                return {
                    "success": False,
                    "message": "未找到有效的SKU编码",
                    "data": [],
                    "total": 0,
                    "platform": "领先未来",
                    "query_content": query_content
                }

            # 根据配置选择采集方式
            if use_playwright:
                data = await self._scrape_with_playwright_mode(skus)
            else:
                data = await self._scrape_with_direct_mode(skus)

            success_count = len([item for item in data if item.get('status') == 'success'])
            mode_text = "Playwright模式" if use_playwright else "直接模式"
            
            result = {
                "success": True,
                "message": f"采集完成，成功 {success_count}/{len(data)} 条记录（{mode_text}）",
                "data": data,
                "total": len(data),
                "platform": "领先未来",
                "query_content": query_content,
                "mode": mode_text,
                "success_count": success_count,
                "total_count": len(data)
            }
            
            logger.info(f"✅ 领先未来平台数据采集完成 - 成功: {success_count}/{len(data)} 条记录")
            return result
            
        except Exception as e:
            logger.error(f"❌ 领先未来平台数据采集失败: {str(e)}")
            return {
                "success": False,
                "message": f"领先未来平台数据采集失败: {str(e)}",
                "data": [],
                "total": 0,
                "platform": "领先未来",
                "query_content": query_content
            }
    
    async def _scrape_with_playwright_mode(self, skus: List[str]) -> List[Dict]:
        """
        Playwright模式：使用统一Playwright管理器
        - Playwright仅用于获取Cookie（一次性初始化）
        - 后续所有数据采集使用Requests + Cookie
        - 支持Cookie失效自动刷新
        """
        logger.info("使用Playwright模式进行数据采集（统一管理器模式）...")

        # 智能Cookie管理：检查并在需要时刷新Cookie
        target_url = "http://www.66123123.com"
        success = await self.playwright_manager.refresh_cookies_if_needed(
            target_url=target_url,
            session_obj=self.session,
            test_url=target_url
        )
        if not success:
            logger.error("Cookie初始化/刷新失败，返回错误结果")
            return [self._create_error_result(sku, '无法获取或刷新cookies') for sku in skus]

        # 使用Requests进行批量采集（带Cookie）
        logger.info("Cookie已就绪，使用Requests进行数据采集...")
        results = await self._scrape_multiple_with_requests(skus, use_cookies=True)

        # 检查结果中是否有大量失败，可能是Cookie失效
        failed_count = sum(1 for r in results if r.get('status') == 'error')
        if failed_count > len(skus) * 0.5:  # 如果失败率超过50%
            logger.warning(f"检测到高失败率({failed_count}/{len(skus)})，可能是Cookie失效，尝试刷新Cookie...")

            # 强制刷新Cookie
            self.playwright_manager.reset_cookies()
            refresh_success = await self.playwright_manager.initialize_cookies(
                target_url=target_url,
                session_obj=self.session
            )

            if refresh_success:
                logger.info("Cookie刷新成功，重新采集失败的SKU...")
                # 重新采集失败的SKU
                failed_skus = [r['sku_number'] for r in results if r.get('status') == 'error']
                if failed_skus:
                    retry_results = await self._scrape_multiple_with_requests(failed_skus, use_cookies=True)
                    # 更新结果
                    for i, result in enumerate(results):
                        if result.get('status') == 'error':
                            sku = result['sku_number']
                            retry_result = next((r for r in retry_results if r['sku_number'] == sku), None)
                            if retry_result and retry_result.get('status') == 'success':
                                results[i] = retry_result
                                logger.info(f"SKU {sku} 重新采集成功")

        return results
    
    async def _scrape_with_direct_mode(self, skus: List[str]) -> List[Dict]:
        """直接模式：跳过cookie获取，直接用Requests进行数据采集"""
        return await self._scrape_multiple_with_requests(skus, use_cookies=False)



    async def _scrape_multiple_with_requests(self, skus: List[str], use_cookies: bool = True) -> List[Dict]:
        """
        批量爬取多个商品信息 - 并发优化版本（保持输入顺序）
        """
        total = len(skus)

        # 创建并发控制信号量
        self.semaphore = asyncio.Semaphore(self.concurrent_threads)

        mode_text = "Playwright模式" if use_cookies else "直接模式"
        logger.info(f"开始并发处理 {total} 个SKU，模式: {mode_text}，并发数: {self.concurrent_threads}")

        # 创建结果数组，保持输入顺序
        results = [None] * total

        # 定义单个SKU处理任务
        async def process_single_sku(index: int, sku: str):
            async with self.semaphore:  # 控制并发数量
                try:
                    # 处理单个SKU
                    result = await self._scrape_single_product(sku, use_cookies)
                    result['index'] = index  # 添加索引信息
                    result['original_order'] = index  # 保存原始顺序

                    # 将结果放入正确的位置
                    results[index] = result

                    logger.info(f"SKU {sku} 处理完成 (位置 {index + 1}/{total}): {result['status']}")

                except Exception as e:
                    error_result = self._create_error_result(sku, f'处理异常: {str(e)}')
                    error_result['index'] = index
                    error_result['original_order'] = index
                    results[index] = error_result

                    logger.error(f"SKU {sku} 处理异常 (位置 {index + 1}/{total}): {str(e)}")

        # 创建所有任务
        tasks = [process_single_sku(i, sku) for i, sku in enumerate(skus)]

        # 并发执行所有任务
        await asyncio.gather(*tasks)

        # 确保所有结果都已填充
        for i, result in enumerate(results):
            if result is None:
                results[i] = self._create_error_result(skus[i], '未知错误')
                results[i]['index'] = i
                results[i]['original_order'] = i

        success_count = sum(1 for r in results if r['status'] == 'success')
        logger.info(f"并发处理完成，成功: {success_count}/{total}")

        return results

    async def _scrape_single_product(self, sku: str, use_cookies: bool = True) -> Dict:
        """
        爬取单个商品信息 - 使用XPath选择器直接解析
        """
        url = self.base_url.format(sku=sku)
        mode_text = "Playwright模式" if use_cookies else "直接模式"

        logger.info(f"SKU {sku} - 开始数据采集 ({mode_text})")

        try:
            # 智能延迟控制
            delay = self.get_random_delay()
            logger.debug(f"SKU {sku} - 请求, 延迟 {delay:.2f}秒 ({mode_text})")
            await asyncio.sleep(delay)

            # 动态设置请求头
            request_headers = self.session.headers.copy()
            request_headers['User-Agent'] = self.get_random_user_agent()

            # 使用异步aiohttp进行请求
            timeout = aiohttp.ClientTimeout(total=self.request_timeout)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                # 根据模式选择cookies策略
                cookies = None
                if use_cookies and hasattr(self.session, 'cookies'):
                    # 将requests cookies转换为aiohttp格式
                    cookies = {cookie.name: cookie.value for cookie in self.session.cookies}

                async with session.get(
                    url,
                    headers=request_headers,
                    cookies=cookies,
                    allow_redirects=True
                ) as response:
                    response_status = response.status
                    response_content = await response.read()

            if response_status == 200:
                # 解析HTML内容
                tree = html.fromstring(response_content)

                # 使用XPath选择器提取数据
                product_data = self._extract_product_data(tree, sku, url)

                if product_data['product_name'] != '获取失败':
                    logger.debug(f"SKU {sku} - 数据采集成功: {product_data['product_name']}")
                    return product_data
                else:
                    logger.warning(f"SKU {sku} - 未找到商品信息")
                    return self._create_error_result(sku, '未找到商品信息')

            elif response_status == 404:
                return self._create_error_result(sku, '商品不存在')
            else:
                logger.warning(f"SKU {sku} - HTTP错误: {response_status}")
                return self._create_error_result(sku, f'HTTP错误: {response_status}')

        except Exception as e:
            logger.warning(f"SKU {sku} - 采集异常: {str(e)}")
            return self._create_error_result(sku, f'采集异常: {str(e)}')

    def _extract_product_data(self, tree, sku: str, url: str) -> Dict:
        """
        使用XPath选择器提取商品数据，并进行特殊的字符串处理
        """
        try:
            # 提取产品名称
            product_name_elements = tree.xpath(self.xpath_product_name)
            product_name = product_name_elements[0].strip() if product_name_elements else '获取失败'

            logger.debug(f"SKU {sku} - 原始产品名称: '{product_name}'")

            # 从产品名称中提取品牌（智能过滤数字前缀）
            product_ventor = self._extract_brand_from_name(product_name)
            logger.debug(f"SKU {sku} - 提取品牌: '{product_ventor}'")

            # 提取计量单位
            product_unit_elements = tree.xpath(self.xpath_product_unit)
            product_unit = product_unit_elements[0].strip() if product_unit_elements else '个'
            logger.debug(f"SKU {sku} - 计量单位: '{product_unit}'")

            # 从产品名称中提取型号规格（智能过滤数字前缀）
            product_model = self._extract_model_from_name(product_name)
            logger.debug(f"SKU {sku} - 提取型号: '{product_model}'")

            # 提取售价
            product_price_elements = tree.xpath(self.xpath_product_price)
            if product_price_elements:
                raw_price = product_price_elements[0].strip()
                product_price = f"{raw_price}" if raw_price else '暂无价格信息'
            else:
                product_price = '暂无价格信息'
            logger.debug(f"SKU {sku} - 售价: '{product_price}'")

            # 返回完整的9字段结构
            result = {
                'sku_number': sku,
                'product_name': product_name,
                'product_ventor': product_ventor,
                'url': url,
                'product_unit': product_unit,
                'product_model': product_model,
                'Null1': '-',
                'Null2': '-',
                'product_price': product_price,
                'platform': '领先未来',
                'status': 'success'
            }

            logger.debug(f"SKU {sku} - 数据提取成功，品牌: '{product_ventor}', 型号: '{product_model}'")
            return result

        except Exception as e:
            logger.error(f"SKU {sku} - 数据提取异常: {str(e)}")
            return self._create_error_result(sku, f'数据提取异常: {str(e)}')

    def _create_error_result(self, sku: str, error_message: str) -> Dict:
        """创建错误结果 - 9字段结构"""
        return {
            'sku_number': sku,
            'product_name': '获取失败',
            'product_ventor': '获取失败',
            'url': self.base_url.format(sku=sku),
            'product_unit': '获取失败',
            'product_model': '获取失败',
            'Null1': '-',
            'Null2': '-',
            'product_price': '获取失败',
            'platform': '领先未来',
            'status': 'error',
            'error': error_message
        }

    async def scrape_data_stream(self, query_content: str, playwright_mode: bool = None):
        """
        流式数据采集 - 实时返回每个SKU的采集结果
        """
        try:
            # 从配置文件读取Playwright模式设置
            use_playwright = self.settings.LXWL_USE_PLAYWRIGHT
            logger.info(f"开始流式采集领先未来平台数据 - Playwright模式: {use_playwright}")

            skus = self._parse_query_content(query_content)

            if not skus:
                yield {
                    "type": "error",
                    "message": "未找到有效的SKU编码"
                }
                return

            total = len(skus)

            # 根据配置选择采集方式
            if use_playwright:
                # 确保cookies已初始化
                if not self.playwright_manager.cookies_initialized:
                    yield {
                        "type": "progress",
                        "message": "正在初始化Playwright cookies...",
                        "current": 0,
                        "total": total
                    }

                    success = await self.playwright_manager.initialize_cookies(
                        target_url="http://www.66123123.com",
                        session_obj=self.session
                    )
                    if not success:
                        yield {
                            "type": "error",
                            "message": "无法获取初始cookies"
                        }
                        return

            # 创建并发控制信号量
            self.semaphore = asyncio.Semaphore(self.concurrent_threads)

            # 结果收集器，保持输入顺序
            results = [None] * total
            completed_count = 0

            # 定义单个SKU处理任务（简化版本）
            async def process_single_sku_simple(index: int, sku: str):
                nonlocal completed_count

                async with self.semaphore:
                    try:
                        # 处理单个SKU
                        result = await self._scrape_single_product(sku, use_playwright)
                        result['index'] = index
                        result['original_order'] = index

                        # 保存结果
                        results[index] = result
                        completed_count += 1

                        logger.info(f"流式处理完成 SKU {sku} (位置 {index + 1}/{total}): {result['status']}")

                        return {
                            "type": "result",
                            "data": result,
                            "current": completed_count,
                            "total": total,
                            "index": index
                        }

                    except Exception as e:
                        error_result = self._create_error_result(sku, f'处理异常: {str(e)}')
                        error_result['index'] = index
                        error_result['original_order'] = index
                        results[index] = error_result
                        completed_count += 1

                        logger.error(f"流式处理异常 SKU {sku} (位置 {index + 1}/{total}): {str(e)}")

                        return {
                            "type": "result",
                            "data": error_result,
                            "current": completed_count,
                            "total": total,
                            "index": index
                        }

            # 创建所有任务
            tasks = [asyncio.create_task(process_single_sku_simple(i, sku)) for i, sku in enumerate(skus)]

            # 实时流式处理：使用队列确保按完成顺序实时发送，但保持索引正确性
            pending_tasks = set(tasks)

            while pending_tasks:
                # 等待任何一个任务完成
                done, pending_tasks = await asyncio.wait(pending_tasks, return_when=asyncio.FIRST_COMPLETED)

                for task in done:
                    try:
                        task_result = await task
                        # 立即发送结果，保持索引与SKU的正确对应关系
                        yield task_result
                        logger.debug(f"✅ 流式发送结果 - 索引: {task_result['index']}, SKU: {task_result['data']['sku_number']}")
                    except Exception as e:
                        logger.error(f"流式任务执行异常: {str(e)}")
                        # 继续处理其他任务

            # 发送完成消息
            success_count = sum(1 for r in results if r and r['status'] == 'success')
            yield {
                "type": "complete",
                "message": f"流式采集完成，成功 {success_count}/{total} 条记录",
                "success_count": success_count,
                "total_count": total,
                "results": results
            }

        except Exception as e:
            logger.error(f"流式数据采集失败: {str(e)}")
            yield {
                "type": "error",
                "message": f"流式数据采集失败: {str(e)}"
            }

    async def scrape_single_sku(self, sku: str, playwright_mode: bool = None) -> Dict:
        """
        采集单个SKU数据（用于重试功能）
        """
        try:
            # 从配置文件读取Playwright模式设置
            use_playwright = self.settings.LXWL_USE_PLAYWRIGHT
            logger.info(f"开始采集单个SKU: {sku}, Playwright模式: {use_playwright} (配置驱动)")

            # 根据配置选择采集方式
            if use_playwright:
                # 确保cookies已初始化
                if not self.playwright_manager.cookies_initialized:
                    logger.info("单个SKU采集需要初始化cookies...")
                    success = await self.playwright_manager.initialize_cookies(
                        target_url="http://www.66123123.com",
                        session_obj=self.session
                    )
                    if not success:
                        return self._create_error_result(sku, '无法获取初始cookies')

                # 使用Playwright模式采集
                result = await self._scrape_single_product(sku, use_cookies=True)
            else:
                # 使用直接模式采集
                result = await self._scrape_single_product(sku, use_cookies=False)

            logger.info(f"单个SKU采集完成: {sku}, 状态: {result['status']}")
            return result

        except Exception as e:
            logger.error(f"单个SKU采集失败: {sku}, 错误: {str(e)}")
            return self._create_error_result(sku, f'采集异常: {str(e)}')

    def get_session_info(self) -> Dict:
        """获取当前session信息"""
        return {
            'cookies_initialized': self.playwright_manager.cookies_initialized,
            'cookies_count': len(self.session.cookies),
            'session_info': self.playwright_manager.get_session_info()
        }


# 创建全局实例
lxwl_service = LxwlScraperService()
