/**
 * 数据分析页面样式
 * 支持明暗主题切换和响应式设计
 */

/* 健康状态徽章 */
.health-badge {
    font-size: 0.875rem;
    font-weight: 600;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
}

.health-excellent {
    background-color: var(--tblr-success);
    color: white;
}

.health-good {
    background-color: var(--tblr-info);
    color: white;
}

.health-warning {
    background-color: var(--tblr-warning);
    color: white;
}

.health-critical {
    background-color: var(--tblr-danger);
    color: white;
}

/* 统计卡片 */
.stats-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.2;
    color: var(--tblr-primary);
}

.stats-label {
    font-size: 0.875rem;
    color: var(--tblr-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stats-change {
    font-size: 0.875rem;
    font-weight: 600;
}

.stats-change.positive {
    color: var(--tblr-success);
}

.stats-change.negative {
    color: var(--tblr-danger);
}

.stats-change.neutral {
    color: var(--tblr-muted);
}

/* 图表容器 */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

.chart-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: var(--tblr-muted);
}

.chart-error {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: var(--tblr-danger);
    flex-direction: column;
}

.chart-error-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* 模块统计项 */
.module-stat-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--tblr-border-color);
    transition: background-color 0.2s ease-in-out;
}

.module-stat-item:last-child {
    border-bottom: none;
}

.module-stat-item:hover {
    background-color: var(--tblr-bg-surface-secondary);
    border-radius: 0.375rem;
    margin: 0 -0.5rem;
    padding-left: 1.25rem;
    padding-right: 1.25rem;
}

.module-stat-name {
    font-weight: 600;
    color: var(--tblr-body-color);
}

.module-stat-count {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--tblr-primary);
}

.module-stat-change {
    font-size: 0.875rem;
    font-weight: 500;
}

.module-stat-success-rate {
    font-size: 0.875rem;
    color: var(--tblr-muted);
}

/* 详细数据表格 */
.details-table {
    font-size: 0.875rem;
}

.details-table th {
    font-weight: 600;
    color: var(--tblr-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.75rem;
    padding: 0.75rem 1rem;
    border-bottom: 2px solid var(--tblr-border-color);
}

.details-table td {
    padding: 1rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--tblr-border-color-translucent);
}

.details-table tbody tr:hover {
    background-color: var(--tblr-bg-surface-secondary);
}

/* 状态指示器 */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
}

.status-healthy .status-dot {
    background-color: var(--tblr-success);
}

.status-warning .status-dot {
    background-color: var(--tblr-warning);
}

.status-error .status-dot {
    background-color: var(--tblr-danger);
}

.status-inactive .status-dot {
    background-color: var(--tblr-muted);
}

/* 搜索框 */
.search-input {
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.search-input:focus {
    border-color: var(--tblr-primary);
    box-shadow: 0 0 0 0.125rem rgba(var(--tblr-primary-rgb), 0.25);
}

/* 加载状态 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    border-radius: 0.375rem;
}

.dark .loading-overlay {
    background-color: rgba(0, 0, 0, 0.8);
}

.loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 2px solid var(--tblr-border-color);
    border-top: 2px solid var(--tblr-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stats-number {
        font-size: 2rem;
    }
    
    .chart-container {
        height: 250px;
    }
    
    .module-stat-item {
        padding: 0.5rem 0;
    }
    
    .details-table {
        font-size: 0.8rem;
    }
    
    .details-table th,
    .details-table td {
        padding: 0.5rem;
    }
}

@media (max-width: 576px) {
    .stats-number {
        font-size: 1.75rem;
    }
    
    .chart-container {
        height: 200px;
    }
    
    .card-header {
        padding: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* 暗色主题适配 */
.dark .stats-card:hover {
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
}

.dark .chart-loading,
.dark .chart-error {
    color: var(--tblr-muted);
}

.dark .module-stat-item:hover {
    background-color: var(--tblr-bg-surface-secondary);
}

.dark .details-table tbody tr:hover {
    background-color: var(--tblr-bg-surface-secondary);
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.3s ease-in-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 工具提示 */
.tooltip-custom {
    font-size: 0.875rem;
    background-color: var(--tblr-dark);
    color: white;
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dark .tooltip-custom {
    background-color: var(--tblr-light);
    color: var(--tblr-dark);
}

/* 模块详情模态框样式 */
.modal-full-width {
    max-width: 95%;
    width: 95%;
    /* 确保与Bootstrap的modal-dialog-centered兼容 */
    margin: 1.75rem auto;
}

@media (min-width: 576px) {
    .modal-full-width {
        max-width: 90%;
        width: 90%;
        margin: 1.75rem auto;
    }
}

@media (min-width: 992px) {
    .modal-full-width {
        max-width: 85%;
        width: 85%;
        margin: 1.75rem auto;
    }
}

@media (min-width: 1200px) {
    .modal-full-width {
        max-width: 1140px;
        width: 1140px;
        margin: 1.75rem auto;
    }
}

/* 确保在垂直居中时有足够的上下边距 */
.modal-dialog-centered.modal-full-width {
    min-height: calc(100vh - 3.5rem);
}

.modal-full-width .modal-content {
    border-radius: 0.5rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    max-height: calc(100vh - 3.5rem);
    overflow-y: auto;
}

.dark .modal-full-width .modal-content {
    box-shadow: 0 10px 40px rgba(255, 255, 255, 0.1);
}

/* 详情表格样式 */
.detail-logs-table {
    font-size: 0.875rem;
}

.detail-logs-table th {
    font-weight: 600;
    color: var(--tblr-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.75rem;
    padding: 0.75rem 0.5rem;
    border-bottom: 2px solid var(--tblr-border-color);
    white-space: nowrap;
}

.detail-logs-table td {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--tblr-border-color-translucent);
}

.detail-logs-table tbody tr:hover {
    background-color: var(--tblr-bg-surface-secondary);
}

/* 筛选区域样式 */
.detail-filters {
    background-color: var(--tblr-bg-surface);
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid var(--tblr-border-color);
}

.dark .detail-filters {
    background-color: var(--tblr-bg-surface-secondary);
}

/* 分页样式优化 */
.detail-pagination .page-link {
    color: var(--tblr-primary);
    border-color: var(--tblr-border-color);
    padding: 0.375rem 0.75rem;
}

.detail-pagination .page-link:hover {
    color: var(--tblr-primary);
    background-color: var(--tblr-bg-surface-secondary);
    border-color: var(--tblr-primary);
}

.detail-pagination .page-item.active .page-link {
    background-color: var(--tblr-primary);
    border-color: var(--tblr-primary);
    color: white;
}

/* 状态徽章样式 */
.status-badge {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

.status-badge.success {
    background-color: var(--tblr-success);
    color: white;
}

.status-badge.failed {
    background-color: var(--tblr-danger);
    color: white;
}

/* 响应时间颜色 */
.response-time-fast {
    color: var(--tblr-success);
    font-weight: 600;
}

.response-time-medium {
    color: var(--tblr-warning);
    font-weight: 600;
}

.response-time-slow {
    color: var(--tblr-danger);
    font-weight: 600;
}

/* 查看详情按钮样式 */
.btn-view-detail {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease-in-out;
}

.btn-view-detail:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dark .btn-view-detail:hover {
    box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
}

/* 移动端适配 */
@media (max-width: 768px) {
    .modal-full-width {
        max-width: 98%;
        width: 98%;
        /* 移除margin，让Bootstrap的居中机制正常工作 */
        margin: 1rem auto;
    }

    /* 在小屏幕上减少垂直边距 */
    .modal-dialog-centered.modal-full-width {
        min-height: calc(100vh - 2rem);
    }

    .modal-full-width .modal-content {
        max-height: calc(100vh - 2rem);
    }
}

/* 修复可能的居中问题 */
.modal.show .modal-dialog.modal-full-width {
    transform: none;
}

/* 确保模态框背景遮罩正确显示 */
.modal-backdrop {
    z-index: 1040;
}

.modal.show {
    z-index: 1050;
}

/* 修复在某些情况下的滚动问题 */
.modal-open {
    overflow: hidden;
}

.modal-open .modal {
    overflow-x: hidden;
    overflow-y: auto;
}

    .detail-logs-table {
        font-size: 0.8rem;
    }

    .detail-logs-table th,
    .detail-logs-table td {
        padding: 0.5rem 0.25rem;
    }

    .detail-filters {
        padding: 0.75rem;
    }

    .detail-filters .row > div {
        margin-bottom: 0.75rem;
    }

    .detail-filters .row > div:last-child {
        margin-bottom: 0;
    }
}
