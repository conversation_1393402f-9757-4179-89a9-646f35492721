[project]
name = "project-nhf"
version = "1.8.2"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "aiofiles>=23.2.1",
    "aiohttp>=3.9.0",
    "asyncio>=3.4.3",
    "beautifulsoup4>=4.12.0",
    "black>=23.11.0",
    "cssselect>=1.2.0",
    "fastapi>=0.115.1",
    "isort>=5.12.0",
    "jinja2>=3.1.2",
    "loguru>=0.7.2",
    "lxml>=4.9.0",
    "passlib[bcrypt]>=1.7.4",
    "playwright>=1.40.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "python-dotenv>=1.0.0",
    "python-jose[cryptography]>=3.3.0",
    "python-multipart>=0.0.6",
    "requests>=2.31.0",
    "uvicorn[standard]>=0.24.0",
]
