/* 商品详情页面样式 */

/* 详情卡片样式 */
.detail-card {
    margin-bottom: 1.5rem;
}

.detail-field {
    margin-bottom: 0.75rem;
}

.detail-field .label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.25rem;
}

.detail-field .value {
    color: #212529;
}

.status-badge {
    font-size: 0.875rem;
}

/* 加载覆盖层样式 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

/* 商品信息表格样式 */
.product-info-table {
    font-size: 0.875rem;
}

.product-info-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

.product-info-table td {
    vertical-align: middle;
}

.text-truncate-custom {
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* ========== 表格文本省略号统一样式 ========== */

/* 表格单元格文本省略号样式 - 单行显示+省略号 */
.table-cell-ellipsis {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

/* 文本截断样式（保留兼容性） */
.text-truncate-custom {
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 详情字段样式 */
.detail-field {
    margin-bottom: 1rem;
}

.detail-field .label {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
    font-weight: 500;
}

.detail-field .value {
    font-size: 0.9rem;
    color: #495057;
    word-wrap: break-word;
}

/* 商品详情特有的字段样式 */
.detail-field .value code {
    background-color: #f8f9fa;
    color: #e83e8c;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

.detail-field .value .badge {
    font-size: 0.75rem;
}

/* 状态徽章样式 */
.status-badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

/* 商品价格样式 */
.product-price {
    font-size: 1.1rem;
    font-weight: 600;
    color: #28a745;
}

/* 商品名称样式 */
.product-name {
    font-size: 1rem;
    font-weight: 500;
    color: #495057;
    line-height: 1.4;
}

/* 商品品目样式 */
.product-category {
    font-size: 0.9rem;
    color: #6c757d;
}

/* 加载和错误状态样式 */
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    flex-direction: column;
}

.loading-container .spinner-border {
    margin-bottom: 1rem;
}

/* 商品详情页面特有的响应式样式 */
@media (max-width: 768px) {
    .detail-field {
        margin-bottom: 0.75rem;
    }
    
    .detail-field .label {
        font-size: 0.8rem;
    }
    
    .detail-field .value {
        font-size: 0.85rem;
    }
    
    .product-price {
        font-size: 1rem;
    }
    
    .product-name {
        font-size: 0.9rem;
    }
}

/* 商品详情卡片特殊样式 */
.product-detail-card .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.product-detail-card .card-header .card-title {
    color: #495057;
    font-weight: 600;
}

/* 商品规格参数样式 */
.spec-field {
    margin-bottom: 0.75rem;
    padding: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
}

.spec-field .label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
    font-weight: 500;
}

.spec-field .value {
    font-size: 0.875rem;
    color: #495057;
    word-wrap: break-word;
    white-space: pre-wrap;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
}

.empty-state .icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state .title {
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.empty-state .subtitle {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* 图片预览样式 */
.image-preview img,
.image-list img {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border-radius: 0.375rem;
}

.image-preview img:hover,
.image-list img:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.image-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

/* 图片占位符样式 */
.img-placeholder {
    background-color: #f8f9fa;
    border: 2px dashed #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 0.875rem;
    border-radius: 0.375rem;
}

/* 图片加载错误样式 */
.img-error {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 0.75rem;
    border-radius: 0.375rem;
}

/* 图片模态框样式 */
.modal-body img {
    border-radius: 0.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 重试按钮样式 */
.retry-button {
    padding: 1rem;
    border-top: 1px solid #dee2e6;
    background-color: #f8f9fa;
    border-radius: 0 0 0.375rem 0.375rem;
}

.retry-button .btn {
    transition: all 0.2s ease;
}

.retry-button .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 空状态改进样式 */
.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: #6c757d;
}

.empty-state .empty-icon {
    margin-bottom: 1rem;
    opacity: 0.6;
}

.empty-state .empty-title {
    font-size: 1rem;
    font-weight: 500;
    margin: 0;
    color: #495057;
}

/* 加载状态改进 */
.loading-overlay {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(2px);
}

.loading-overlay .spinner-border {
    width: 2rem;
    height: 2rem;
}

/* 错误状态改进 */
.alert-danger {
    border-left: 4px solid #dc3545;
}

.alert-danger h4 {
    color: #721c24;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

/* 审核历史样式 */
.approval-record {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: #fff;
    transition: box-shadow 0.2s ease;
}

.approval-record:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.approval-record.latest {
    border-color: #0d6efd;
    background-color: #f8f9ff;
}

.approval-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.75rem;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.approval-phase .badge {
    font-size: 0.75rem;
}

.approval-status .badge {
    font-size: 0.75rem;
}

.approval-time {
    font-size: 0.875rem;
}

.approval-body {
    border-top: 1px solid #e9ecef;
    padding-top: 0.75rem;
}

.approval-content,
.approval-user {
    margin-bottom: 0.5rem;
}

.approval-content:last-child,
.approval-user:last-child {
    margin-bottom: 0;
}

.approval-text {
    display: block;
    margin-top: 0.25rem;
    padding: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 0.25rem;
    border-left: 3px solid #6c757d;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.user-name {
    color: #0d6efd;
    font-weight: 500;
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
    .approval-record {
        background-color: #2d3748;
        border-color: #4a5568;
    }

    .approval-record.latest {
        background-color: #2a2f3a;
        border-color: #4299e1;
    }

    .approval-text {
        background-color: #4a5568;
        color: #e2e8f0;
    }
}

/* 响应式图片样式 */
@media (max-width: 768px) {
    .image-preview img {
        max-width: 80px !important;
        max-height: 80px !important;
    }

    .image-list img {
        max-width: 50px !important;
        max-height: 50px !important;
    }

    .empty-state {
        padding: 2rem 1rem;
    }

    .retry-button {
        padding: 0.75rem;
    }

    /* 审核历史移动端优化 */
    .approval-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .approval-record {
        padding: 0.75rem;
        margin-bottom: 0.75rem;
    }

    .approval-text {
        padding: 0.375rem;
        font-size: 0.875rem;
    }
}
