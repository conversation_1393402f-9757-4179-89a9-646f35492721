/**
 * 账号管理模块
 * 处理账号密码的本地存储、管理和安全加密
 */

class AccountManager {
    constructor() {
        this.storageKey = 'newhf_saved_accounts';
        this.encryptionKey = 'newhf_encryption_key_2024';
        this.accounts = this.loadAccounts();
        this.initializeEventListeners();
    }

    /**
     * 简单的字符串加密（Base64 + 简单偏移）
     */
    encrypt(text) {
        try {
            // 简单的字符偏移加密
            let encrypted = '';
            for (let i = 0; i < text.length; i++) {
                encrypted += String.fromCharCode(text.charCodeAt(i) + 3);
            }
            // Base64编码
            return btoa(encrypted);
        } catch (error) {
            console.error('加密失败:', error);
            return text;
        }
    }

    /**
     * 简单的字符串解密
     */
    decrypt(encryptedText) {
        try {
            // Base64解码
            const decoded = atob(encryptedText);
            // 字符偏移解密
            let decrypted = '';
            for (let i = 0; i < decoded.length; i++) {
                decrypted += String.fromCharCode(decoded.charCodeAt(i) - 3);
            }
            return decrypted;
        } catch (error) {
            console.error('解密失败:', error);
            return encryptedText;
        }
    }

    /**
     * 从本地存储加载账号
     */
    loadAccounts() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            if (stored) {
                const accounts = JSON.parse(stored);
                // 解密密码
                Object.keys(accounts).forEach(platform => {
                    accounts[platform].forEach(account => {
                        if (account.password) {
                            account.password = this.decrypt(account.password);
                        }
                    });
                });
                return accounts;
            }
        } catch (error) {
            console.error('加载账号失败:', error);
        }
        return { aviation: [], engine: [] };
    }

    /**
     * 保存账号到本地存储
     */
    saveAccounts() {
        try {
            // 加密密码后保存
            const accountsToSave = JSON.parse(JSON.stringify(this.accounts));
            Object.keys(accountsToSave).forEach(platform => {
                accountsToSave[platform].forEach(account => {
                    if (account.password) {
                        account.password = this.encrypt(account.password);
                    }
                });
            });
            localStorage.setItem(this.storageKey, JSON.stringify(accountsToSave));
        } catch (error) {
            console.error('保存账号失败:', error);
        }
    }

    /**
     * 添加或更新账号
     */
    saveAccount(platform, username, password, displayName = null) {
        if (!this.accounts[platform]) {
            this.accounts[platform] = [];
        }

        // 检查是否已存在相同用户名的账号
        const existingIndex = this.accounts[platform].findIndex(
            account => account.username === username
        );

        const accountData = {
            username: username,
            password: password,
            displayName: displayName || username,
            savedAt: new Date().toISOString(),
            lastUsed: new Date().toISOString()
        };

        if (existingIndex >= 0) {
            // 更新现有账号
            this.accounts[platform][existingIndex] = accountData;
        } else {
            // 添加新账号
            this.accounts[platform].push(accountData);
        }

        this.saveAccounts();
        this.updateUI();
        
        return true;
    }

    /**
     * 删除账号
     */
    deleteAccount(platform, username) {
        if (this.accounts[platform]) {
            this.accounts[platform] = this.accounts[platform].filter(
                account => account.username !== username
            );
            this.saveAccounts();
            this.updateUI();
            return true;
        }
        return false;
    }

    /**
     * 获取指定平台的所有账号
     */
    getAccounts(platform) {
        return this.accounts[platform] || [];
    }

    /**
     * 获取指定账号
     */
    getAccount(platform, username) {
        if (this.accounts[platform]) {
            return this.accounts[platform].find(
                account => account.username === username
            );
        }
        return null;
    }

    /**
     * 更新账号最后使用时间
     */
    updateLastUsed(platform, username) {
        const account = this.getAccount(platform, username);
        if (account) {
            account.lastUsed = new Date().toISOString();
            this.saveAccounts();
        }
    }

    /**
     * 清空所有保存的账号
     */
    clearAllAccounts() {
        this.accounts = { aviation: [], engine: [] };
        localStorage.removeItem(this.storageKey);
        this.updateUI();
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        // 监听保存账号选择变化
        document.addEventListener('DOMContentLoaded', () => {
            const savedAccountsSelect = document.getElementById('saved-accounts-select');
            if (savedAccountsSelect) {
                savedAccountsSelect.addEventListener('change', (e) => {
                    this.handleSavedAccountSelection(e.target.value);
                });
            }

            // 监听密码显示/隐藏按钮
            const togglePasswordBtn = document.getElementById('toggle-password');
            if (togglePasswordBtn) {
                togglePasswordBtn.addEventListener('click', () => {
                    this.togglePasswordVisibility();
                });
            }

            // 监听平台按钮组变化
            const platformRadios = document.querySelectorAll('input[name="platform"]');
            platformRadios.forEach(radio => {
                radio.addEventListener('change', () => {
                    this.updateSavedAccountsDropdown();
                });
            });

            // 监听用户名输入变化
            const usernameInput = document.querySelector('input[name="username"]');
            if (usernameInput) {
                usernameInput.addEventListener('input', () => {
                    this.handleUsernameInput();
                });
            }

            // 监听快速切换点击事件
            document.addEventListener('click', (e) => {
                if (e.target.closest('.quick-switch-item')) {
                    e.preventDefault();
                    const item = e.target.closest('.quick-switch-item');
                    const platform = item.dataset.platform;
                    const username = item.dataset.username;

                    if (platform && username) {
                        this.handleQuickSwitch(platform, username);
                    }
                }
            });

            // 监听快速切换下拉菜单显示事件
            const quickSwitchBtn = document.getElementById('quick-switch-btn');
            if (quickSwitchBtn) {
                quickSwitchBtn.addEventListener('show.bs.dropdown', () => {
                    this.updateQuickSwitchDropdown();
                });
            }

            // 监听键盘导航
            document.addEventListener('keydown', (e) => {
                // Alt + Q 快速打开快速切换菜单
                if (e.altKey && e.key.toLowerCase() === 'q') {
                    e.preventDefault();
                    const quickSwitchBtn = document.getElementById('quick-switch-btn');
                    if (quickSwitchBtn) {
                        quickSwitchBtn.click();
                    }
                }

                // ESC键取消快速切换
                if (e.key === 'Escape') {
                    const overlay = document.getElementById('quick-switch-overlay');
                    if (overlay && overlay.classList.contains('show')) {
                        e.preventDefault();
                        this.hideQuickSwitchOverlay();
                        if (typeof authManager !== 'undefined') {
                            authManager.showNotification('已取消账号切换', 'info');
                        }
                    }
                }
            });

            // 初始化UI
            this.updateUI();
        });
    }

    /**
     * 处理快速切换点击
     */
    async handleQuickSwitch(platform, username) {
        const account = this.getAccount(platform, username);
        if (!account) {
            if (typeof authManager !== 'undefined') {
                authManager.showNotification('账号信息不存在', 'error');
            }
            return;
        }

        try {
            // 显示全屏遮罩层
            this.showQuickSwitchOverlay(platform, username, account.displayName);

            // 关闭下拉菜单
            const dropdown = document.querySelector('.dropdown-toggle[data-bs-toggle="dropdown"]');
            if (dropdown) {
                const bsDropdown = bootstrap.Dropdown.getInstance(dropdown);
                if (bsDropdown) {
                    bsDropdown.hide();
                }
            }

            // 执行快速切换登录
            const success = await this.quickSwitchLogin(platform, username);

            if (!success) {
                // 如果切换失败，显示错误信息
                if (typeof authManager !== 'undefined') {
                    authManager.showNotification('快速切换失败，请重试', 'error');
                }
            }
        } catch (error) {
            console.error('快速切换失败:', error);
            if (typeof authManager !== 'undefined') {
                authManager.showNotification('快速切换失败，请重试', 'error');
            }
        } finally {
            // 隐藏遮罩层
            this.hideQuickSwitchOverlay();
        }
    }

    /**
     * 显示快速切换遮罩层
     */
    showQuickSwitchOverlay(platform, username, displayName) {
        const overlay = document.getElementById('quick-switch-overlay');
        const title = document.getElementById('quick-switch-overlay-title');
        const description = document.getElementById('quick-switch-overlay-description');
        const avatar = document.getElementById('quick-switch-overlay-avatar');
        const accountName = document.getElementById('quick-switch-overlay-account-name');
        const accountPlatform = document.getElementById('quick-switch-overlay-account-platform');

        if (!overlay) return;

        // 设置平台信息
        const platformInfo = {
            aviation: { name: '航空平台', color: 'primary', text: '航' },
            engine: { name: '航发平台', color: 'success', text: '航发' }
        };

        const info = platformInfo[platform] || platformInfo.aviation;

        // 更新内容
        if (title) {
            title.textContent = `正在切换到${info.name}`;
        }

        if (description) {
            description.textContent = `请稍候，正在为您切换到 ${displayName} 账号...`;
        }

        if (avatar) {
            avatar.className = `avatar bg-${info.color}-lt`;
            avatar.innerHTML = `<span class="text-${info.color}">${info.text}</span>`;
        }

        if (accountName) {
            accountName.textContent = displayName;
        }

        if (accountPlatform) {
            accountPlatform.textContent = `${info.name} • ${username}`;
        }

        // 禁用页面滚动
        document.body.classList.add('quick-switch-loading');

        // 显示遮罩层
        overlay.classList.add('show');

        // 设置取消按钮事件
        this.setupOverlayCancelButton();
    }

    /**
     * 隐藏快速切换遮罩层
     */
    hideQuickSwitchOverlay() {
        const overlay = document.getElementById('quick-switch-overlay');
        if (overlay) {
            overlay.classList.remove('show');

            // 恢复页面滚动
            setTimeout(() => {
                document.body.classList.remove('quick-switch-loading');
            }, 300); // 等待动画完成
        }
    }

    /**
     * 设置遮罩层取消按钮
     */
    setupOverlayCancelButton() {
        const cancelBtn = document.getElementById('quick-switch-overlay-cancel');
        if (cancelBtn) {
            // 移除之前的事件监听器
            cancelBtn.replaceWith(cancelBtn.cloneNode(true));
            const newCancelBtn = document.getElementById('quick-switch-overlay-cancel');

            // 添加新的事件监听器
            newCancelBtn.addEventListener('click', () => {
                this.hideQuickSwitchOverlay();
                if (typeof authManager !== 'undefined') {
                    authManager.showNotification('已取消账号切换', 'info');
                }
            });
        }
    }

    /**
     * 处理已保存账号的选择
     */
    handleSavedAccountSelection(accountKey) {
        if (!accountKey) return;

        const [platform, username] = accountKey.split('|');
        const account = this.getAccount(platform, username);
        
        if (account) {
            // 自动填充表单
            const form = document.getElementById('login-form');
            if (form) {
                // 选中对应的平台按钮
                const platformRadio = form.querySelector(`input[name="platform"][value="${platform}"]`);
                if (platformRadio) {
                    platformRadio.checked = true;
                }

                form.querySelector('input[name="username"]').value = account.username;
                form.querySelector('input[name="password"]').value = account.password;
                form.querySelector('input[name="remember_password"]').checked = true;
            }
        }
    }

    /**
     * 切换密码可见性
     */
    togglePasswordVisibility() {
        const passwordInput = document.querySelector('input[name="password"]');
        const toggleBtn = document.getElementById('toggle-password');
        
        if (passwordInput && toggleBtn) {
            const isPassword = passwordInput.type === 'password';
            passwordInput.type = isPassword ? 'text' : 'password';
            
            // 更新按钮图标
            const icon = toggleBtn.querySelector('svg');
            if (icon) {
                if (isPassword) {
                    // 显示"隐藏"图标
                    icon.innerHTML = `
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M10.585 10.587a2 2 0 0 0 2.829 2.828"></path>
                        <path d="M16.681 16.673a8.717 8.717 0 0 1 -4.681 1.327c-3.6 0 -6.6 -2 -9 -6c1.272 -2.12 2.712 -3.678 4.32 -4.674m2.86 -1.146a9.055 9.055 0 0 1 1.82 -.18c3.6 0 6.6 2 9 6c-.666 1.11 -1.379 2.067 -2.138 2.87"></path>
                        <path d="M3 3l18 18"></path>
                    `;
                } else {
                    // 显示"显示"图标
                    icon.innerHTML = `
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"></path>
                        <path d="M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6"></path>
                    `;
                }
            }
        }
    }

    /**
     * 获取当前选中的平台
     */
    getSelectedPlatform() {
        const checkedRadio = document.querySelector('input[name="platform"]:checked');
        return checkedRadio ? checkedRadio.value : '';
    }

    /**
     * 处理用户名输入变化
     */
    handleUsernameInput() {
        const savedAccountsSelect = document.getElementById('saved-accounts-select');
        if (savedAccountsSelect) {
            savedAccountsSelect.value = ''; // 清空账号选择
        }
    }

    /**
     * 更新已保存账号下拉列表
     */
    updateSavedAccountsDropdown() {
        const savedAccountsSelect = document.getElementById('saved-accounts-select');
        const savedAccountsSection = document.getElementById('saved-accounts-section');

        if (!savedAccountsSelect || !savedAccountsSection) return;

        const selectedPlatform = this.getSelectedPlatform();
        const accounts = this.getAccounts(selectedPlatform);

        // 清空现有选项
        savedAccountsSelect.innerHTML = '<option value="">选择已保存的账号...</option>';

        if (accounts.length > 0) {
            accounts.forEach(account => {
                const option = document.createElement('option');
                option.value = `${selectedPlatform}|${account.username}`;
                option.textContent = `${account.displayName} (${account.username})`;
                savedAccountsSelect.appendChild(option);
            });
            savedAccountsSection.style.display = 'block';
        } else {
            savedAccountsSection.style.display = 'none';
        }
    }

    /**
     * 更新UI显示
     */
    updateUI() {
        this.updateSavedAccountsDropdown();
        this.updateAccountManagerModal();
        this.updateQuickSwitchDropdown();
    }

    /**
     * 获取所有已保存账号的格式化列表（用于快速切换）
     */
    getFormattedAccountsForQuickSwitch() {
        const formattedAccounts = [];

        Object.keys(this.accounts).forEach(platform => {
            this.accounts[platform].forEach(account => {
                formattedAccounts.push({
                    platform: platform,
                    username: account.username,
                    displayName: account.displayName,
                    lastUsed: account.lastUsed,
                    platformName: platform === 'aviation' ? '航空平台' : '航发平台',
                    platformColor: platform === 'aviation' ? 'primary' : 'success',
                    platformIcon: platform === 'aviation' ? 'plane' : 'engine',
                    lastUsedFormatted: this.formatLastUsedTime(account.lastUsed)
                });
            });
        });

        // 按最后使用时间排序
        formattedAccounts.sort((a, b) => new Date(b.lastUsed) - new Date(a.lastUsed));

        return formattedAccounts;
    }

    /**
     * 格式化最后使用时间
     */
    formatLastUsedTime(lastUsedISO) {
        const lastUsed = new Date(lastUsedISO);
        const now = new Date();
        const diffMs = now - lastUsed;
        const diffMins = Math.floor(diffMs / (1000 * 60));
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (diffMins < 1) {
            return '刚刚';
        } else if (diffMins < 60) {
            return `${diffMins}分钟前`;
        } else if (diffHours < 24) {
            return `${diffHours}小时前`;
        } else if (diffDays < 7) {
            return `${diffDays}天前`;
        } else {
            return lastUsed.toLocaleDateString('zh-CN');
        }
    }

    /**
     * 触发一键快速切换登录
     */
    async quickSwitchLogin(platform, username) {
        const account = this.getAccount(platform, username);
        if (!account) {
            if (typeof authManager !== 'undefined') {
                authManager.showNotification('账号信息不存在', 'error');
            }
            return false;
        }

        // 更新最后使用时间
        this.updateLastUsed(platform, username);

        // 调用AuthManager的自动登录方法
        if (typeof authManager !== 'undefined') {
            return await authManager.autoLogin({
                platform: platform,
                username: account.username,
                password: account.password
            });
        }

        return false;
    }

    /**
     * 更新快速切换下拉菜单
     */
    updateQuickSwitchDropdown() {
        const quickSwitchDropdown = document.getElementById('quick-switch-dropdown');
        if (!quickSwitchDropdown) return;

        const accounts = this.getFormattedAccountsForQuickSwitch();

        if (accounts.length === 0) {
            quickSwitchDropdown.innerHTML = `
                <li>
                    <span class="dropdown-item-text text-muted">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M8.18 8.189a4.01 4.01 0 0 0 2.616 2.627m3.507 -.545a4 4 0 0 0 -5.59 -5.552"></path>
                            <path d="M6 21v-2a4 4 0 0 1 4 -4h4c.412 0 .81 .062 1.183 .178m2.633 2.618c.12 .38 .184 .785 .184 1.204v2"></path>
                            <path d="M3 3l18 18"></path>
                        </svg>
                        暂无保存的账号
                    </span>
                </li>
            `;
            return;
        }

        let html = '';
        let currentPlatform = '';

        accounts.forEach((account) => {
            // 添加平台分组标题
            if (account.platform !== currentPlatform) {
                if (currentPlatform !== '') {
                    html += '<li><hr class="dropdown-divider"></li>';
                }
                html += `
                    <li>
                        <h6 class="dropdown-header">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2 text-${account.platformColor}" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                ${this.getPlatformIconSVG(account.platform)}
                            </svg>
                            ${account.platformName}
                        </h6>
                    </li>
                `;
                currentPlatform = account.platform;
            }

            // 添加账号项
            html += `
                <li>
                    <a class="dropdown-item quick-switch-item" href="#" data-platform="${account.platform}" data-username="${account.username}">
                        <div class="d-flex align-items-center">
                            <div class="avatar avatar-sm bg-${account.platformColor}-lt me-3">
                                <span class="text-${account.platformColor}">${account.displayName.charAt(0).toUpperCase()}</span>
                            </div>
                            <div class="flex-fill">
                                <div class="font-weight-medium">${account.displayName}</div>
                                <div class="text-muted small">${account.username} • ${account.lastUsedFormatted}</div>
                            </div>
                            <div class="ms-2">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon text-muted" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M9 12l2 2l4 -4"></path>
                                    <path d="M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6"></path>
                                </svg>
                            </div>
                        </div>
                    </a>
                </li>
            `;
        });

        // 添加管理账号选项
        html += `
            <li><hr class="dropdown-divider"></li>
            <li>
                <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#account-manager-modal">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z"></path>
                        <path d="M9 12a3 3 0 1 0 6 0a3 3 0 0 0 -6 0"></path>
                    </svg>
                    管理保存的账号
                </a>
            </li>
        `;

        quickSwitchDropdown.innerHTML = html;
    }

    /**
     * 获取平台图标SVG
     */
    getPlatformIconSVG(platform) {
        if (platform === 'aviation') {
            return `
                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                <path d="M16 10h4a2 2 0 0 1 0 4h-4l-4 7h-3l2 -7h-4l-2 2h-3l2 -4l-2 -4h3l2 2h4l-2 -7h3z"></path>
            `;
        } else {
            return `
                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                <path d="M3 10v6"></path>
                <path d="M12 5v3"></path>
                <path d="M12 15v3"></path>
                <path d="M5 7h2a1 1 0 0 1 1 1v1a1 1 0 0 0 1 1h6a1 1 0 0 0 1 -1v-1a1 1 0 0 1 1 -1h2"></path>
                <path d="M5 17h2a1 1 0 0 0 1 -1v-1a1 1 0 0 1 1 -1h6a1 1 0 0 1 1 1v1a1 1 0 0 0 1 1h2"></path>
                <path d="M21 10v6"></path>
            `;
        }
    }

    /**
     * 更新账号管理模态框
     */
    updateAccountManagerModal() {
        const aviationList = document.getElementById('aviation-accounts-list');
        const engineList = document.getElementById('engine-accounts-list');

        if (aviationList) {
            this.renderAccountList(aviationList, 'aviation');
        }
        if (engineList) {
            this.renderAccountList(engineList, 'engine');
        }
    }

    /**
     * 渲染账号列表
     */
    renderAccountList(container, platform) {
        const accounts = this.getAccounts(platform);
        
        if (accounts.length === 0) {
            container.innerHTML = `
                <div class="empty-state text-center py-4">
                    <div class="empty-icon mb-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-user-off" width="48" height="48" viewBox="0 0 24 24" stroke-width="1" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M8.18 8.189a4.01 4.01 0 0 0 2.616 2.627m3.507 -.545a4 4 0 0 0 -5.59 -5.552"></path>
                            <path d="M6 21v-2a4 4 0 0 1 4 -4h4c.412 0 .81 .062 1.183 .178m2.633 2.618c.12 .38 .184 .785 .184 1.204v2"></path>
                            <path d="M3 3l18 18"></path>
                        </svg>
                    </div>
                    <p class="text-muted">暂无保存的${platform === 'aviation' ? '航空' : '航发'}平台账号</p>
                </div>
            `;
            return;
        }

        let html = '';
        accounts.forEach(account => {
            const lastUsed = new Date(account.lastUsed).toLocaleString('zh-CN');
            html += `
                <div class="card mb-2">
                    <div class="card-body py-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="text-muted small">${account.username}</div>
                                <div class="text-muted small">最后使用: ${lastUsed}</div>
                            </div>
                            <div>
                                <button class="btn btn-sm btn-outline-danger" onclick="accountManager.deleteAccount('${platform}', '${account.username}')">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M4 7l16 0"></path>
                                        <path d="M10 11l0 6"></path>
                                        <path d="M14 11l0 6"></path>
                                        <path d="M5 7l1 -4l4 0l1 4"></path>
                                        <path d="M9 7l6 0"></path>
                                    </svg>
                                    删除
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;
    }
}

// 创建全局账号管理器实例
const accountManager = new AccountManager();

// 全局函数
function clearAllSavedAccounts() {
    if (confirm('确定要清空所有保存的账号吗？此操作不可撤销。')) {
        accountManager.clearAllAccounts();
        authManager.showNotification('已清空所有保存的账号', 'info');
    }
}
