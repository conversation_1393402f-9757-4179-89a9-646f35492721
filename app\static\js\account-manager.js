/**
 * 账号管理模块
 * 处理账号密码的本地存储、管理和安全加密
 */

class AccountManager {
    constructor() {
        this.storageKey = 'newhf_saved_accounts';
        this.encryptionKey = 'newhf_encryption_key_2024';
        this.accounts = this.loadAccounts();
        this.initializeEventListeners();
    }

    /**
     * 简单的字符串加密（Base64 + 简单偏移）
     */
    encrypt(text) {
        try {
            // 简单的字符偏移加密
            let encrypted = '';
            for (let i = 0; i < text.length; i++) {
                encrypted += String.fromCharCode(text.charCodeAt(i) + 3);
            }
            // Base64编码
            return btoa(encrypted);
        } catch (error) {
            console.error('加密失败:', error);
            return text;
        }
    }

    /**
     * 简单的字符串解密
     */
    decrypt(encryptedText) {
        try {
            // Base64解码
            const decoded = atob(encryptedText);
            // 字符偏移解密
            let decrypted = '';
            for (let i = 0; i < decoded.length; i++) {
                decrypted += String.fromCharCode(decoded.charCodeAt(i) - 3);
            }
            return decrypted;
        } catch (error) {
            console.error('解密失败:', error);
            return encryptedText;
        }
    }

    /**
     * 从本地存储加载账号
     */
    loadAccounts() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            if (stored) {
                const accounts = JSON.parse(stored);
                // 解密密码
                Object.keys(accounts).forEach(platform => {
                    accounts[platform].forEach(account => {
                        if (account.password) {
                            account.password = this.decrypt(account.password);
                        }
                    });
                });
                return accounts;
            }
        } catch (error) {
            console.error('加载账号失败:', error);
        }
        return { aviation: [], engine: [] };
    }

    /**
     * 保存账号到本地存储
     */
    saveAccounts() {
        try {
            // 加密密码后保存
            const accountsToSave = JSON.parse(JSON.stringify(this.accounts));
            Object.keys(accountsToSave).forEach(platform => {
                accountsToSave[platform].forEach(account => {
                    if (account.password) {
                        account.password = this.encrypt(account.password);
                    }
                });
            });
            localStorage.setItem(this.storageKey, JSON.stringify(accountsToSave));
        } catch (error) {
            console.error('保存账号失败:', error);
        }
    }

    /**
     * 添加或更新账号
     */
    saveAccount(platform, username, password, displayName = null) {
        if (!this.accounts[platform]) {
            this.accounts[platform] = [];
        }

        // 检查是否已存在相同用户名的账号
        const existingIndex = this.accounts[platform].findIndex(
            account => account.username === username
        );

        const accountData = {
            username: username,
            password: password,
            displayName: displayName || username,
            savedAt: new Date().toISOString(),
            lastUsed: new Date().toISOString()
        };

        if (existingIndex >= 0) {
            // 更新现有账号
            this.accounts[platform][existingIndex] = accountData;
        } else {
            // 添加新账号
            this.accounts[platform].push(accountData);
        }

        this.saveAccounts();
        this.updateUI();
        
        return true;
    }

    /**
     * 删除账号
     */
    deleteAccount(platform, username) {
        if (this.accounts[platform]) {
            this.accounts[platform] = this.accounts[platform].filter(
                account => account.username !== username
            );
            this.saveAccounts();
            this.updateUI();
            return true;
        }
        return false;
    }

    /**
     * 获取指定平台的所有账号
     */
    getAccounts(platform) {
        return this.accounts[platform] || [];
    }

    /**
     * 获取指定账号
     */
    getAccount(platform, username) {
        if (this.accounts[platform]) {
            return this.accounts[platform].find(
                account => account.username === username
            );
        }
        return null;
    }

    /**
     * 更新账号最后使用时间
     */
    updateLastUsed(platform, username) {
        const account = this.getAccount(platform, username);
        if (account) {
            account.lastUsed = new Date().toISOString();
            this.saveAccounts();
        }
    }

    /**
     * 清空所有保存的账号
     */
    clearAllAccounts() {
        this.accounts = { aviation: [], engine: [] };
        localStorage.removeItem(this.storageKey);
        this.updateUI();
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        // 监听保存账号选择变化
        document.addEventListener('DOMContentLoaded', () => {
            const savedAccountsSelect = document.getElementById('saved-accounts-select');
            if (savedAccountsSelect) {
                savedAccountsSelect.addEventListener('change', (e) => {
                    this.handleSavedAccountSelection(e.target.value);
                });
            }

            // 监听密码显示/隐藏按钮
            const togglePasswordBtn = document.getElementById('toggle-password');
            if (togglePasswordBtn) {
                togglePasswordBtn.addEventListener('click', () => {
                    this.togglePasswordVisibility();
                });
            }

            // 监听平台按钮组变化
            const platformRadios = document.querySelectorAll('input[name="platform"]');
            platformRadios.forEach(radio => {
                radio.addEventListener('change', () => {
                    this.updateSavedAccountsDropdown();
                });
            });

            // 监听用户名输入变化
            const usernameInput = document.querySelector('input[name="username"]');
            if (usernameInput) {
                usernameInput.addEventListener('input', () => {
                    this.handleUsernameInput();
                });
            }

            // 初始化UI
            this.updateUI();
        });
    }

    /**
     * 处理已保存账号的选择
     */
    handleSavedAccountSelection(accountKey) {
        if (!accountKey) return;

        const [platform, username] = accountKey.split('|');
        const account = this.getAccount(platform, username);
        
        if (account) {
            // 自动填充表单
            const form = document.getElementById('login-form');
            if (form) {
                // 选中对应的平台按钮
                const platformRadio = form.querySelector(`input[name="platform"][value="${platform}"]`);
                if (platformRadio) {
                    platformRadio.checked = true;
                }

                form.querySelector('input[name="username"]').value = account.username;
                form.querySelector('input[name="password"]').value = account.password;
                form.querySelector('input[name="remember_password"]').checked = true;
            }
        }
    }

    /**
     * 切换密码可见性
     */
    togglePasswordVisibility() {
        const passwordInput = document.querySelector('input[name="password"]');
        const toggleBtn = document.getElementById('toggle-password');
        
        if (passwordInput && toggleBtn) {
            const isPassword = passwordInput.type === 'password';
            passwordInput.type = isPassword ? 'text' : 'password';
            
            // 更新按钮图标
            const icon = toggleBtn.querySelector('svg');
            if (icon) {
                if (isPassword) {
                    // 显示"隐藏"图标
                    icon.innerHTML = `
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M10.585 10.587a2 2 0 0 0 2.829 2.828"></path>
                        <path d="M16.681 16.673a8.717 8.717 0 0 1 -4.681 1.327c-3.6 0 -6.6 -2 -9 -6c1.272 -2.12 2.712 -3.678 4.32 -4.674m2.86 -1.146a9.055 9.055 0 0 1 1.82 -.18c3.6 0 6.6 2 9 6c-.666 1.11 -1.379 2.067 -2.138 2.87"></path>
                        <path d="M3 3l18 18"></path>
                    `;
                } else {
                    // 显示"显示"图标
                    icon.innerHTML = `
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"></path>
                        <path d="M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6"></path>
                    `;
                }
            }
        }
    }

    /**
     * 获取当前选中的平台
     */
    getSelectedPlatform() {
        const checkedRadio = document.querySelector('input[name="platform"]:checked');
        return checkedRadio ? checkedRadio.value : '';
    }

    /**
     * 处理用户名输入变化
     */
    handleUsernameInput() {
        const savedAccountsSelect = document.getElementById('saved-accounts-select');
        if (savedAccountsSelect) {
            savedAccountsSelect.value = ''; // 清空账号选择
        }
    }

    /**
     * 更新已保存账号下拉列表
     */
    updateSavedAccountsDropdown() {
        const savedAccountsSelect = document.getElementById('saved-accounts-select');
        const savedAccountsSection = document.getElementById('saved-accounts-section');

        if (!savedAccountsSelect || !savedAccountsSection) return;

        const selectedPlatform = this.getSelectedPlatform();
        const accounts = this.getAccounts(selectedPlatform);

        // 清空现有选项
        savedAccountsSelect.innerHTML = '<option value="">选择已保存的账号...</option>';

        if (accounts.length > 0) {
            accounts.forEach(account => {
                const option = document.createElement('option');
                option.value = `${selectedPlatform}|${account.username}`;
                option.textContent = `${account.displayName} (${account.username})`;
                savedAccountsSelect.appendChild(option);
            });
            savedAccountsSection.style.display = 'block';
        } else {
            savedAccountsSection.style.display = 'none';
        }
    }

    /**
     * 更新UI显示
     */
    updateUI() {
        this.updateSavedAccountsDropdown();
        this.updateAccountManagerModal();
    }

    /**
     * 更新账号管理模态框
     */
    updateAccountManagerModal() {
        const aviationList = document.getElementById('aviation-accounts-list');
        const engineList = document.getElementById('engine-accounts-list');

        if (aviationList) {
            this.renderAccountList(aviationList, 'aviation');
        }
        if (engineList) {
            this.renderAccountList(engineList, 'engine');
        }
    }

    /**
     * 渲染账号列表
     */
    renderAccountList(container, platform) {
        const accounts = this.getAccounts(platform);
        
        if (accounts.length === 0) {
            container.innerHTML = `
                <div class="empty-state text-center py-4">
                    <div class="empty-icon mb-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-user-off" width="48" height="48" viewBox="0 0 24 24" stroke-width="1" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M8.18 8.189a4.01 4.01 0 0 0 2.616 2.627m3.507 -.545a4 4 0 0 0 -5.59 -5.552"></path>
                            <path d="M6 21v-2a4 4 0 0 1 4 -4h4c.412 0 .81 .062 1.183 .178m2.633 2.618c.12 .38 .184 .785 .184 1.204v2"></path>
                            <path d="M3 3l18 18"></path>
                        </svg>
                    </div>
                    <p class="text-muted">暂无保存的${platform === 'aviation' ? '航空' : '航发'}平台账号</p>
                </div>
            `;
            return;
        }

        let html = '';
        accounts.forEach(account => {
            const lastUsed = new Date(account.lastUsed).toLocaleString('zh-CN');
            html += `
                <div class="card mb-2">
                    <div class="card-body py-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="text-muted small">${account.username}</div>
                                <div class="text-muted small">最后使用: ${lastUsed}</div>
                            </div>
                            <div>
                                <button class="btn btn-sm btn-outline-danger" onclick="accountManager.deleteAccount('${platform}', '${account.username}')">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M4 7l16 0"></path>
                                        <path d="M10 11l0 6"></path>
                                        <path d="M14 11l0 6"></path>
                                        <path d="M5 7l1 -4l4 0l1 4"></path>
                                        <path d="M9 7l6 0"></path>
                                    </svg>
                                    删除
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;
    }
}

// 创建全局账号管理器实例
const accountManager = new AccountManager();

// 全局函数
function clearAllSavedAccounts() {
    if (confirm('确定要清空所有保存的账号吗？此操作不可撤销。')) {
        accountManager.clearAllAccounts();
        authManager.showNotification('已清空所有保存的账号', 'info');
    }
}
