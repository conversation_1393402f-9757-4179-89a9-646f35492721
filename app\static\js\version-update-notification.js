/**
 * 版本更新通知管理器
 * 负责检测版本更新并显示更新日志模态框
 */
class VersionUpdateNotification {
    constructor() {
        this.storageKey = 'app_last_viewed_version';
        this.modalId = 'version-update-modal';
        this.isInitialized = false;
        
        // 绑定方法上下文
        this.checkVersionUpdate = this.checkVersionUpdate.bind(this);
        this.showUpdateModal = this.showUpdateModal.bind(this);
        this.markVersionAsViewed = this.markVersionAsViewed.bind(this);
        this.createUpdateModal = this.createUpdateModal.bind(this);
        this.renderChangelogContent = this.renderChangelogContent.bind(this);
    }

    /**
     * 初始化版本更新检测
     */
    async init() {
        if (this.isInitialized) {
            return;
        }

        try {
            await this.checkVersionUpdate();
            this.isInitialized = true;
            console.log('版本更新通知管理器已初始化');
        } catch (error) {
            console.error('版本更新检测初始化失败:', error);
        }
    }

    /**
     * 检测版本更新
     */
    async checkVersionUpdate() {
        try {
            // 获取最新版本信息
            const response = await fetch('/api/v1/system/latest-version');
            const result = await response.json();

            if (!response.ok || !result.success) {
                console.warn('获取最新版本信息失败:', result.message);
                return;
            }

            const latestVersion = result.data.version;
            const changes = result.data.changes;

            // 获取用户上次查看的版本
            const lastViewedVersion = this.getLastViewedVersion();

            // 检查是否需要显示更新通知
            if (this.shouldShowUpdateNotification(latestVersion, lastViewedVersion)) {
                console.log(`检测到新版本: ${latestVersion}，上次查看版本: ${lastViewedVersion || '无'}`);
                await this.showUpdateModal(latestVersion, changes);
            } else {
                console.log(`当前版本 ${latestVersion} 已查看，无需显示更新通知`);
            }

        } catch (error) {
            console.error('版本更新检测失败:', error);
        }
    }

    /**
     * 判断是否需要显示更新通知
     */
    shouldShowUpdateNotification(latestVersion, lastViewedVersion) {
        // 如果没有上次查看的版本记录，显示更新通知
        if (!lastViewedVersion) {
            return true;
        }

        // 如果版本号不同，显示更新通知
        return latestVersion !== lastViewedVersion;
    }

    /**
     * 获取用户上次查看的版本号
     */
    getLastViewedVersion() {
        try {
            return localStorage.getItem(this.storageKey);
        } catch (error) {
            console.warn('无法从localStorage获取上次查看的版本:', error);
            return null;
        }
    }

    /**
     * 标记版本为已查看
     */
    markVersionAsViewed(version) {
        try {
            localStorage.setItem(this.storageKey, version);
            console.log(`已标记版本 ${version} 为已查看`);
        } catch (error) {
            console.warn('无法保存版本查看记录到localStorage:', error);
        }
    }

    /**
     * 显示更新模态框
     */
    async showUpdateModal(version, changes) {
        try {
            // 创建模态框HTML
            this.createUpdateModal(version, changes);

            // 显示模态框
            const modalElement = document.getElementById(this.modalId);
            if (modalElement) {
                const modal = new bootstrap.Modal(modalElement, {
                    backdrop: 'static',
                    keyboard: false
                });
                modal.show();

                // 绑定关闭事件
                const closeBtn = modalElement.querySelector('.btn-close-update');
                if (closeBtn) {
                    closeBtn.addEventListener('click', () => {
                        this.markVersionAsViewed(version);
                        modal.hide();
                    });
                }
            }

        } catch (error) {
            console.error('显示更新模态框失败:', error);
        }
    }

    /**
     * 创建更新模态框HTML
     */
    createUpdateModal(version, changes) {
        // 检查是否已存在模态框
        let existingModal = document.getElementById(this.modalId);
        if (existingModal) {
            existingModal.remove();
        }

        // 创建模态框HTML
        const modalHtml = `
            <div class="modal modal-blur fade" id="${this.modalId}" tabindex="-1" role="dialog" aria-hidden="true">
                <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-rocket me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M4 13a8 8 0 0 1 7 7a6 6 0 0 0 3 -5a9 9 0 0 0 6 -8a3 3 0 0 0 -3 -3a9 9 0 0 0 -8 6a6 6 0 0 0 -5 3"></path>
                                    <path d="M7 14a6 6 0 0 0 3 6a6 6 0 0 0 6 -3"></path>
                                    <circle cx="15" cy="9" r="1"></circle>
                                </svg>
                                版本更新 ${this.escapeHtml(version)}
                            </h5>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <p class="text-muted mb-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-info-circle me-1" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <circle cx="12" cy="12" r="9"></circle>
                                        <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                        <polyline points="11,12 12,12 12,16 13,16"></polyline>
                                    </svg>
                                    欢迎使用新版本！以下是本次更新的主要内容：
                                </p>
                            </div>
                            <div class="changelog-content">
                                ${this.renderChangelogContent(changes)}
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-primary btn-close-update">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-check me-1" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <polyline points="9,11 12,14 20,6"></polyline>
                                    <path d="M21 12c0 4.97 -4.03 9 -9 9s-9 -4.03 -9 -9 4.03 -9 9 -9c1.51 0 2.93 .37 4.18 1.03"></path>
                                </svg>
                                我知道了
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);
    }

    /**
     * 渲染更新日志内容
     */
    renderChangelogContent(changes) {
        if (!changes || !Array.isArray(changes) || changes.length === 0) {
            return '<p class="text-muted">暂无更新记录</p>';
        }

        let html = '<ul class="list-unstyled mb-0">';

        changes.forEach(change => {
            const typeInfo = this.getChangeTypeInfo(change.type);
            html += `
                <li class="mb-2">
                    <div class="d-flex align-items-start">
                        <span class="badge ${typeInfo.badgeClass} me-2 mt-1">
                            ${typeInfo.icon}
                            ${typeInfo.label}
                        </span>
                        <span class="flex-1">${this.escapeHtml(change.content)}</span>
                    </div>
                </li>
            `;
        });

        html += '</ul>';
        return html;
    }

    /**
     * 获取更新类型信息
     */
    getChangeTypeInfo(type) {
        const typeMap = {
            'Added': {
                label: '新增',
                badgeClass: 'bg-success',
                icon: ''
            },
            'Changed': {
                label: '修改',
                badgeClass: 'bg-info',
                icon: ''
            },
            'Fixed': {
                label: '修复',
                badgeClass: 'bg-warning',
                icon: ''
            },
            'Removed': {
                label: '移除',
                badgeClass: 'bg-danger',
                icon: ''
            },
            'Security': {
                label: '安全',
                badgeClass: 'bg-dark',
                icon: ''
            },
            'Improved': {
                label: '优化',
                badgeClass: 'bg-primary',
                icon: ''
            }
        };

        return typeMap[type] || {
            label: '其他',
            badgeClass: 'bg-secondary',
            icon: ''
        };
    }

    /**
     * HTML转义
     */
    escapeHtml(text) {
        if (text === null || text === undefined) {
            return '';
        }
        const div = document.createElement('div');
        div.textContent = String(text);
        return div.innerHTML;
    }
}

// 创建全局实例
let versionUpdateNotification;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保其他组件已加载
    setTimeout(() => {
        versionUpdateNotification = new VersionUpdateNotification();
        versionUpdateNotification.init();
    }, 1000);
});
