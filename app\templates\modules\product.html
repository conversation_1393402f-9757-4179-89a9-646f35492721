{% extends "base.html" %}

{% block title %}{{ app_name }} - 商品状态查询{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="/static/css/modules/product.css?ver={{ app_version }}">
{% endblock %}

{% block content %}
<div class="page-header d-print-none">
    <div class="container-fluid">
        <div class="row g-2 align-items-center">
            <div class="col">
                <!-- 面包屑导航 -->
                <div class="page-pretitle">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="/">控制台</a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">商品状态查询</li>
                        </ol>
                    </nav>
                </div>
                <h2 class="page-title">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler me-2 text-warning" width="32" height="32" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M7 4v16l13 0v-16"></path>
                        <path d="M7 4l4 0l0 4"></path>
                        <path d="M11 4l4 0"></path>
                        <path d="M15 4l0 4l-4 0"></path>
                        <path d="M7 8l13 0"></path>
                        <path d="M7 12l13 0"></path>
                        <path d="M7 16l13 0"></path>
                    </svg>
                    商品状态查询
                </h2>
                <div class="text-muted mt-1">
                    采集和管理商品状态数据，支持库存监控和供应商管理
                </div>
            </div>
            <div class="col-auto ms-auto d-print-none">
                <div class="btn-list">
                    <a href="/" class="btn btn-outline-secondary">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M5 12l14 0"></path>
                            <path d="M5 12l6 6"></path>
                            <path d="M5 12l6 -6"></path>
                        </svg>
                        返回控制台
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="page-body">
    <div class="container-fluid">
        <!-- 登录提示 -->
        <div class="row" id="login-prompt">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <h3>需要登录</h3>
                        <p class="text-muted">请先登录到电子超市以使用商品状态查询功能</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#login-modal">
                            立即登录
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- ELECTRON权限不足提示 -->
        <div class="row" id="electron-access-denied" style="display: none;">
            <div class="col-12">
                <div class="card border-warning">
                    <div class="card-header bg-warning-lt">
                        <h3 class="card-title text-warning">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M12 9v2m0 4v.01"></path>
                                <path d="M5 19h14a2 2 0 0 0 1.84 -2.75l-7.1 -12.25a2 2 0 0 0 -3.5 0l-7.1 12.25a2 2 0 0 0 1.75 2.75"></path>
                            </svg>
                            电子超市平台权限不足
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <h4>无法访问商品状态查询</h4>
                                <p class="text-muted mb-3">
                                    您已成功登录到商城，但电子超市系统登录失败。
                                    商品状态查询功能需要电子超市平台权限才能正常使用。
                                </p>
                                <div class="alert alert-info">
                                    <h5 class="alert-title">可能的原因：</h5>
                                    <ul class="mb-0">
                                        <li>您的账号没有电子超市平台访问权限</li>
                                        <li>电子超市系统暂时不可用</li>
                                        <li>网络连接问题导致电子超市登录失败</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-auto">
                                <div class="avatar avatar-lg bg-warning-lt text-warning">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="32" height="32" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"></path>
                                        <path d="M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6"></path>
                                        <path d="M3 3l18 18"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4">
                            <button class="btn btn-warning me-2" data-bs-toggle="modal" data-bs-target="#login-modal">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"></path>
                                    <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"></path>
                                </svg>
                                重新登录
                            </button>
                            <a href="/" class="btn btn-outline-secondary">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M5 12l14 0"></path>
                                    <path d="M5 12l6 6"></path>
                                    <path d="M5 12l6 -6"></path>
                                </svg>
                                返回控制台
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 模块内容 -->
        <div id="main-content" style="display: none;">
            <!-- 查询输入卡片 -->
            <div class="card mb-4 query-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"></path>
                            <path d="M21 21l-6 -6"></path>
                        </svg>
                        商品状态查询
                    </h5>
                    <div class="card-actions">
                        <button class="btn btn-sm btn-outline-secondary" onclick="clearProductQueryCondition()">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M4 7l16 0"></path>
                                <path d="M10 11l0 6"></path>
                                <path d="M14 11l0 6"></path>
                                <path d="M5 7l1 -4l4 0l1 4"></path>
                                <path d="M9 7l6 0"></path>
                            </svg>
                            清空条件
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <form id="product-form">
                        <!-- 功能说明 -->
                        <div class="mb-4">
                            <div class="alert alert-info">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0"></path>
                                    <path d="M12 9h.01"></path>
                                    <path d="M11 12h1v4h1"></path>
                                </svg>
                                <strong>智能查询模式</strong>：系统将自动查询商品状态和审核状态信息.
                            </div>
                        </div>

                        <!-- 第一行：商品编码 (SKU) 独占一行 -->
                        <div class="row">
                            <div class="col-12 mb-2">
                                <label class="form-label">商品编码 (SKU)</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" name="product_sku" id="productSkuInput"
                                        placeholder="请输入商品SKU编码，支持多个编码（空格、换行、制表符分隔）">

                                    <!-- 多行编辑下拉菜单 -->
                                    <div class="dropdown">
                                        <button class="btn btn-outline-secondary btn-icon" type="button"
                                                data-bs-toggle="dropdown" aria-expanded="false"
                                                title="多行编辑SKU编码">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                <path d="M4 8v-2a2 2 0 0 1 2 -2h2" />
                                                <path d="M4 16v2a2 2 0 0 0 2 2h2" />
                                                <path d="M16 4h2a2 2 0 0 1 2 2v2" />
                                                <path d="M16 20h2a2 2 0 0 0 2 -2v-2" />
                                            </svg>
                                        </button>

                                        <div class="dropdown-menu dropdown-menu-end" style="min-width: 400px; padding: 1rem;" onclick="event.stopPropagation()">
                                            <div class="mb-2">
                                                <label class="form-label">多行编辑 - 商品SKU编码</label>
                                                <div class="form-text small mb-2">
                                                    每行输入一个SKU编码，或使用空格、制表符分隔<br>
                                                </div>
                                            </div>
                                            <textarea class="form-control" id="productSkuTextarea" rows="8"
                                                      placeholder="3002534&#10;43621463&#10;SKU001&#10;SKU002"></textarea>

                                            <!-- 实时统计显示 -->
                                            <div class="mt-2 mb-2">
                                                <div id="productSkuCountInfo" class="form-text d-flex align-items-center gap-2 text-muted">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M9 11l3 3l8 -8"></path>
                                                        <path d="M20 12v6a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2h9"></path>
                                                    </svg>
                                                    <span id="productSkuCountText">暂无输入</span>
                                                </div>
                                            </div>

                                            <div class="d-flex gap-2">
                                                <button type="button" class="btn btn-primary btn-sm" onclick="ProductModule.applyMultilineEdit('productSku')">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-sm" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                        <path d="M5 12l5 5l10 -10" />
                                                    </svg>
                                                    应用
                                                </button>
                                                <button type="button" class="btn btn-secondary btn-sm" onclick="ProductModule.cancelMultilineEdit('productSku')">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-sm" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                        <path d="M18 6l-12 12" />
                                                        <path d="M6 6l12 12" />
                                                    </svg>
                                                    取消
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-text">
                                    支持输入多个SKU编码，用空格或换行分隔。点击右侧按钮可打开多行编辑器
                                </div>
                            </div>
                        </div>

                        <!-- 第二行：商品名称和筛选条件（4:2:2:2比例布局） -->
                        <div class="row" id="status-filter-fields">
                            <!-- 商品名称：占4/10宽度 -->
                            <div class="col-lg-6 col-md-12 mb-2">
                                <label class="form-label">商品名称</label>
                                <input type="text" class="form-control" name="product_name"
                                    placeholder="请输入商品名称进行模糊搜索，如：齐心封箱胶带">
                                <div class="form-text">
                                    支持模糊搜索，可输入商品名称的关键词
                                </div>
                            </div>
                            <!-- 上架状态：占2/10宽度 -->
                            <div class="col-lg-2 col-md-4 col-sm-6 mb-2" id="shelf-status-field">
                                <label class="form-label">上架状态</label>
                                <select class="form-select" name="shelf_status" id="shelf-status-select">
                                    <option value="">全部</option>
                                    <option value="UP_SHELF">上架</option>
                                    <option value="DOWN_SHELF">下架</option>
                                </select>
                                <div class="form-text">
                                    选择上架状态
                                </div>
                            </div>
                            <!-- 下架类型：占2/10宽度 -->
                            <div class="col-lg-2 col-md-4 col-sm-6 mb-2" id="delist-type-field">
                                <label class="form-label">下架类型</label>
                                <select class="form-select" name="delist_type" id="delist-type-select" disabled>
                                    <option value="">全部</option>
                                    <option value="OPERATE_DELIST">运营下架</option>
                                    <option value="SYSTEM_DELIST">系统下架</option>
                                    <option value="OPERATE_DELIST_FOREVER">永久下架</option>
                                    <option value="SUPPLIER_DELIST">供应商下架</option>
                                </select>
                                <div class="form-text">
                                    仅在选择"下架"时可用
                                </div>
                            </div>
                            <!-- 审核状态：占2/10宽度 -->
                            <div class="col-lg-2 col-md-4 col-sm-6 mb-2" id="approval-status-field">
                                <label class="form-label">审核状态</label>
                                <select class="form-select" name="approval_status">
                                    <option value="">全部</option>
                                    <option value="APPROVED">审核通过</option>
                                    <option value="DRAFT">待审核</option>
                                </select>
                                <div class="form-text">
                                    选择审核状态
                                </div>
                            </div>
                        </div>
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary" id="query-submit-btn">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"></path>
                                    <path d="M21 21l-6 -6"></path>
                                </svg>
                                <span id="query-submit-text">查询商品状态</span>
                            </button>

                        </div>
                    </form>
                </div>
            </div>

            <!-- 筛选器面板 -->
            <div id="data-filter-container"></div>

            <!-- 数据结果卡片 -->
            <div class="card results-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M3 3m0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z"></path>
                            <path d="M7 7h10"></path>
                            <path d="M7 12h10"></path>
                            <path d="M7 17h10"></path>
                        </svg>
                        <span id="results-title">商品状态查询结果</span>
                    </h5>
                    <div class="card-actions">
                        <span class="badge bg-secondary" id="product-count">0 条记录</span>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div id="product-results"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="/static/js/button-manager.js?ver={{ app_version }}"></script>
<script src="/static/js/pagination-manager.js?ver={{ app_version }}"></script>
<script src="/static/js/data-filter.js?ver={{ app_version }}"></script>
<script src="/static/js/modules/product.js?ver={{ app_version }}"></script>
{% endblock %}

