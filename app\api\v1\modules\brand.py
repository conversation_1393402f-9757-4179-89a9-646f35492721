"""
品牌库采集API路由
"""

from fastapi import APIRouter, HTTPException, Depends, UploadFile, File
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
import time
from app.core.auth import get_current_user
from app.services.modules.brand_service import brand_service
from app.services.analytics_service import analytics_service
from app.core.logging import logger


router = APIRouter()


class BrandQueryRequest(BaseModel):
    """品牌库查询请求模型"""
    code: Optional[str] = Field(None, description="品牌编号")
    name: Optional[str] = Field(None, description="品牌名称")
    status: Optional[str] = Field(None, description="启用状态")
    current: int = Field(1, description="当前页码")
    limit: int = Field(20, description="每页数量")


class BrandApprovalQueryRequest(BaseModel):
    """品牌审核查询请求模型"""
    name: Optional[str] = Field(None, description="品牌名称")
    approval_status: Optional[str] = Field(None, description="审核状态")
    current: int = Field(1, description="当前页码")
    limit: int = Field(20, description="每页数量")


class BrandInfo(BaseModel):
    """品牌信息模型"""
    id: str = Field(..., description="品牌ID")
    code: Optional[str] = Field(None, description="品牌编号")
    name: str = Field(..., description="品牌名称")
    nameEn: Optional[str] = Field(None, description="英文名称")
    logoUrl: Optional[str] = Field(None, description="品牌Logo URL")
    logoOwner: Optional[str] = Field(None, description="商标持有人")
    brandOrigin: str = Field(..., description="品牌类型")
    brandOriginDisplay: str = Field(..., description="品牌类型显示")
    status: Optional[str] = Field(None, description="启用状态")
    statusDisplay: Optional[str] = Field(None, description="状态显示")
    approvalStatus: Optional[str] = Field(None, description="审核状态")
    approvalStatusDisplay: Optional[str] = Field(None, description="审核状态显示")


class BrandQueryResponse(BaseModel):
    """品牌库查询响应模型"""
    success: bool = Field(..., description="查询是否成功")
    message: str = Field(..., description="响应消息")
    data: List[BrandInfo] = Field(default_factory=list, description="品牌数据列表")
    total: Optional[int] = Field(None, description="总记录数")
    permission_error: Optional[bool] = Field(None, description="是否为权限错误")


class FileUploadResponse(BaseModel):
    """文件上传响应模型"""
    success: bool = Field(..., description="上传是否成功")
    message: str = Field(..., description="响应消息")
    file_id: Optional[str] = Field(None, description="文件ID")
    file_name: Optional[str] = Field(None, description="文件名")
    file_size: Optional[str] = Field(None, description="文件大小")
    file_suffix: Optional[str] = Field(None, description="文件后缀")
    public_url: Optional[str] = Field(None, description="公开访问URL")
    permission_error: Optional[bool] = Field(None, description="是否为权限错误")


class BrandCreateRequest(BaseModel):
    """新增品牌请求模型"""
    brand_name_cn: Optional[str] = Field(None, description="品牌中文名称")
    brand_name_en: Optional[str] = Field(None, description="品牌英文名称")
    trademark_holder: str = Field(..., description="商标持有人")
    brand_origin: str = Field(..., description="品牌原产地")
    international_classification: List[str] = Field(..., description="国际分类")
    logo_file_id: Optional[str] = Field(None, description="LOGO文件ID")
    logo_file_url: Optional[str] = Field(None, description="LOGO文件URL")
    trademark_file_id: Optional[str] = Field(None, description="商标网截图文件ID")


class BrandCreateResponse(BaseModel):
    """新增品牌响应模型"""
    success: bool = Field(..., description="新增是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Dict[str, Any]] = Field(None, description="返回数据")
    permission_error: Optional[bool] = Field(None, description="是否为权限错误")


@router.post("/brand", response_model=BrandQueryResponse)
async def query_brand_list(
    request: BrandQueryRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    查询品牌库列表

    支持按品牌编号、品牌名称、启用状态进行查询
    需要ELECTRON平台访问权限
    """
    start_time = time.time()

    try:
        logger.info(f"用户 {current_user.get('username')} 请求查询品牌库")
        
        # 检查用户是否有ELECTRON访问权限
        if not current_user.get('electron_access'):
            logger.warning(f"用户 {current_user.get('username')} 没有ELECTRON访问权限")
            return BrandQueryResponse(
                success=False,
                message="需要电子超市平台访问权限才能查询品牌库",
                permission_error=True
            )
        
        # 获取ELECTRON访问令牌
        electron_token = current_user.get('electron_token')
        if not electron_token:
            logger.warning(f"用户 {current_user.get('username')} 缺少ELECTRON访问令牌")
            return BrandQueryResponse(
                success=False,
                message="缺少电子超市平台访问令牌",
                permission_error=True
            )
        
        # 构建查询参数
        query_params = {
            'code': request.code,
            'name': request.name,
            'status': request.status,
            'current': request.current,
            'limit': request.limit
        }
        
        # 执行品牌库查询
        result = await brand_service.query_brand_list(
            access_token=electron_token,
            client_ip=current_user.get('client_ip'),
            **query_params
        )
        
        logger.info(f"品牌库查询完成 - 用户: {current_user.get('username')}, 结果: {result.get('success')}")

        # 记录查询统计（异步，不影响响应）
        try:
            # 计算响应时间
            response_time = (time.time() - start_time) * 1000

            await analytics_service.record_query_log(
                module='brand',
                user=current_user.get('username', 'unknown'),
                platform=current_user.get('platform', 'ELECTRON'),
                success=result.get('success', False),
                response_time=response_time,
                status_code=200 if result.get('success') else 500,
                error_message=result.get('message') if not result.get('success') else None,
                query_params=query_params,
                result_count=result.get('total', 0)
            )
        except Exception as e:
            logger.warning(f"记录品牌库查询统计失败: {str(e)}")

        return BrandQueryResponse(
            success=result['success'],
            message=result['message'],
            data=result['data'],
            total=result.get('total'),
            permission_error=result.get('permission_error')
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("品牌库查询失败", exc_info=True)
        return BrandQueryResponse(
            success=False,
            message="查询过程中发生错误，请稍后重试"
        )


@router.post("/brand/approval", response_model=BrandQueryResponse)
async def query_brand_approval_list(
    request: BrandApprovalQueryRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    查询品牌审核列表

    支持按品牌名称、审核状态进行查询
    需要ELECTRON平台访问权限
    """
    try:
        logger.info(f"用户 {current_user.get('username')} 请求查询品牌审核列表")

        # 检查用户是否有ELECTRON访问权限
        if not current_user.get('electron_access'):
            logger.warning(f"用户 {current_user.get('username')} 没有ELECTRON访问权限")
            return BrandQueryResponse(
                success=False,
                message="需要电子超市平台访问权限才能查询品牌审核列表",
                permission_error=True
            )

        # 获取ELECTRON访问令牌
        electron_token = current_user.get('electron_token')
        if not electron_token:
            logger.warning(f"用户 {current_user.get('username')} 缺少ELECTRON访问令牌")
            return BrandQueryResponse(
                success=False,
                message="缺少电子超市平台访问令牌",
                permission_error=True
            )

        # 构建查询参数
        query_params = {
            'name': request.name,
            'approval_status': request.approval_status,
            'current': request.current,
            'limit': request.limit
        }

        # 执行品牌审核查询
        result = await brand_service.query_brand_approval_list(
            access_token=electron_token,
            client_ip=current_user.get('client_ip'),
            **query_params
        )

        logger.info(f"品牌审核查询完成 - 用户: {current_user.get('username')}, 结果: {result.get('success')}")

        # 记录查询统计（异步，不影响响应）
        try:
            await analytics_service.record_query_log(
                module='brand',
                user=current_user.get('username', 'unknown'),
                platform=current_user.get('platform', 'ELECTRON'),
                success=result.get('success', False),
                response_time=result.get('response_time'),
                status_code=200 if result.get('success') else 500,
                error_message=result.get('message') if not result.get('success') else None,
                query_params={**query_params, 'query_type': 'approval'},
                result_count=result.get('total', 0)
            )
        except Exception as e:
            logger.warning(f"记录品牌审核查询统计失败: {str(e)}")

        return BrandQueryResponse(
            success=result['success'],
            message=result['message'],
            data=result['data'],
            total=result.get('total'),
            permission_error=result.get('permission_error')
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error("品牌审核查询失败", exc_info=True)
        return BrandQueryResponse(
            success=False,
            message="查询过程中发生错误，请稍后重试"
        )


@router.post("/brand/upload", response_model=FileUploadResponse)
async def upload_brand_file(
    file: UploadFile = File(...),
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    上传品牌相关文件（LOGO或商标网截图）

    支持JPG、PNG格式的图片文件
    需要ELECTRON平台访问权限
    """
    try:
        logger.info(f"用户 {current_user.get('username')} 请求上传品牌文件: {file.filename}")

        # 检查用户是否有ELECTRON访问权限
        if not current_user.get('electron_access'):
            logger.warning(f"用户 {current_user.get('username')} 没有ELECTRON访问权限")
            return FileUploadResponse(
                success=False,
                message="需要电子超市平台访问权限才能上传文件",
                permission_error=True
            )

        # 获取ELECTRON访问令牌
        electron_token = current_user.get('electron_token')
        if not electron_token:
            logger.warning(f"用户 {current_user.get('username')} 缺少ELECTRON访问令牌")
            return FileUploadResponse(
                success=False,
                message="缺少电子超市平台访问令牌",
                permission_error=True
            )

        # 验证文件类型
        allowed_types = ['image/jpeg', 'image/png']
        if file.content_type not in allowed_types:
            logger.warning(f"用户上传了不支持的文件类型: {file.content_type}")
            return FileUploadResponse(
                success=False,
                message="只支持JPG和PNG格式的图片文件"
            )

        # 验证文件大小（限制为3MB）
        max_size = 3 * 1024 * 1024  # 3MB
        file_content = await file.read()
        if len(file_content) > max_size:
            logger.warning(f"用户上传的文件过大: {len(file_content)} 字节")
            return FileUploadResponse(
                success=False,
                message="文件大小不能超过3MB"
            )

        # 执行文件上传
        result = await brand_service.upload_brand_file(
            access_token=electron_token,
            file_content=file_content,
            file_name=file.filename,
            file_type=file.content_type,
            client_ip=current_user.get('client_ip')
        )

        logger.info(f"品牌文件上传完成 - 用户: {current_user.get('username')}, 结果: {result.get('success')}")

        return FileUploadResponse(
            success=result['success'],
            message=result['message'],
            file_id=result.get('file_id'),
            file_name=result.get('file_name'),
            file_size=result.get('file_size'),
            file_suffix=result.get('file_suffix'),
            public_url=result.get('public_url'),
            permission_error=result.get('permission_error')
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error("品牌文件上传失败", exc_info=True)
        return FileUploadResponse(
            success=False,
            message="上传过程中发生错误，请稍后重试"
        )


@router.post("/brand/create", response_model=BrandCreateResponse)
async def create_brand(
    request: BrandCreateRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    新增品牌

    支持新增品牌信息，包括基本信息和文件上传
    需要ELECTRON平台访问权限
    """
    try:
        logger.info(f"用户 {current_user.get('username')} 请求新增品牌")

        # 检查用户是否有ELECTRON访问权限
        if not current_user.get('electron_access'):
            logger.warning(f"用户 {current_user.get('username')} 没有ELECTRON访问权限")
            return BrandCreateResponse(
                success=False,
                message="需要ELECTRON平台访问权限",
                permission_error=True
            )

        # 获取ELECTRON访问令牌
        electron_token = current_user.get('electron_token')
        if not electron_token:
            logger.warning(f"用户 {current_user.get('username')} 缺少ELECTRON访问令牌")
            return BrandCreateResponse(
                success=False,
                message="缺少ELECTRON平台访问令牌，请重新登录",
                permission_error=True
            )

        # 验证必填字段
        if not request.brand_name_cn and not request.brand_name_en:
            return BrandCreateResponse(
                success=False,
                message="品牌中文名称或英文名称至少填写一个"
            )

        if not request.trademark_holder:
            return BrandCreateResponse(
                success=False,
                message="商标持有人为必填项"
            )

        if not request.brand_origin:
            return BrandCreateResponse(
                success=False,
                message="品牌原产地为必选项"
            )

        if not request.international_classification or len(request.international_classification) == 0:
            return BrandCreateResponse(
                success=False,
                message="国际分类为必选项"
            )

        # 构建品牌数据
        brand_data = {
            'name': request.brand_name_cn or '',
            'nameEn': request.brand_name_en or '',
            'logoOwner': request.trademark_holder,
            'brandOrigin': request.brand_origin,
            'interCategory': ','.join(request.international_classification),  # 转换为字符串
            'logo': request.logo_file_id or '',
            'logoUrl': request.logo_file_url or '',
            'markUrl': request.trademark_file_id or ''
        }

        # 执行品牌新增
        result = await brand_service.create_brand(
            access_token=electron_token,
            brand_data=brand_data,
            client_ip=current_user.get('client_ip')
        )

        logger.info(f"品牌新增完成 - 用户: {current_user.get('username')}, 结果: {result.get('success')}")

        # 记录操作统计（异步，不影响响应）
        try:
            await analytics_service.record_query_log(
                module='brand',
                user=current_user.get('username', 'unknown'),
                platform=current_user.get('platform', 'ELECTRON'),
                success=result.get('success', False),
                response_time=result.get('response_time'),
                status_code=200 if result.get('success') else 500,
                error_message=result.get('message') if not result.get('success') else None,
                query_params={
                    'brand_name': request.brand_name_cn or request.brand_name_en,
                    'brand_origin': request.brand_origin,
                    'query_type': 'create'
                },
                result_count=1 if result.get('success') else 0
            )
        except Exception as e:
            logger.warning(f"记录品牌新增统计失败: {str(e)}")

        return BrandCreateResponse(
            success=result['success'],
            message=result['message'],
            data=result.get('data'),
            permission_error=result.get('permission_error')
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error("品牌新增失败", exc_info=True)
        return BrandCreateResponse(
            success=False,
            message="新增过程中发生错误，请稍后重试"
        )

