/* 品牌库采集模块样式 */

/* ========== 国际分类下拉多选组件样式 ========== */

/* 分类容器 */
.international-classification-container {
    position: relative;
}

/* 分类下拉框 */
.classification-dropdown {
    position: relative;
}

/* 输入容器 */
.classification-input-container {
    position: relative;
    display: flex;
    align-items: center;
}

/* 搜索输入框 */
.classification-search {
    cursor: pointer;
    padding-right: 80px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.classification-search:focus {
    cursor: text;
}

.classification-search.has-selections {
    color: #0d6efd;
    font-weight: 500;
}

/* 操作按钮容器 */
.classification-actions {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    gap: 4px;
}

.classification-actions .btn {
    padding: 4px 6px;
    border: none;
    background: transparent;
}

.classification-actions .btn:hover {
    background-color: #f8f9fa;
}



/* 下拉菜单 */
.classification-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1050;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    max-height: 300px;
    overflow: hidden;
    margin-top: 4px;
}

/* 下拉菜单头部 */
.classification-dropdown-header {
    padding: 12px;
    border-bottom: 1px solid #dee2e6;
    background-color: #f8f9fa;
    display: flex;
    gap: 8px;
    align-items: center;
}

.classification-dropdown-header input {
    flex: 1;
}

.classification-actions-header {
    display: flex;
    gap: 4px;
}

.classification-actions-header .btn {
    padding: 4px 8px;
    font-size: 0.75rem;
}

/* 下拉列表 */
.classification-dropdown-list {
    max-height: 200px;
    overflow-y: auto;
    padding: 4px 0;
}

/* 分类选项 */
.classification-option {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.15s ease;
    gap: 8px;
}

.classification-option:hover {
    background-color: #f8f9fa;
}

.classification-option.selected {
    background-color: #e7f3ff;
    color: #0d6efd;
}

.classification-option input[type="checkbox"] {
    margin: 0;
}

.classification-option-text {
    flex: 1;
    font-size: 0.875rem;
}

/* 无结果提示 */
.classification-no-results {
    padding: 16px;
    text-align: center;
    color: #6c757d;
    font-size: 0.875rem;
}

/* ========== 模式切换按钮样式 ========== */

/* 品牌查询按钮 - 蓝色主题 */
.btn-check:checked + .btn-outline-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: #fff;
}

/* 审核查询按钮 - 橙色主题 */
.btn-check:checked + .btn-outline-warning {
    background-color: #fd7e14;
    border-color: #fd7e14;
    color: #fff;
}

/* 按钮组间距优化 */
.btn-group .btn {
    transition: all 0.2s ease-in-out;
}

.btn-group .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* ========== 品牌原产地按钮组样式 ========== */

/* 品牌原产地按钮组容器 */
.btn-group[aria-label="品牌原产地选择"] {
    width: 100%;
}

.btn-group[aria-label="品牌原产地选择"] .btn {
    flex: 1;
    font-weight: 500;
}

/* 移动端优化 */
@media (max-width: 576px) {
    .btn-group[aria-label="品牌原产地选择"] .btn {
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }
}

/* ========== 表单进度指示器样式 ========== */

/* 进度指示器容器 */
.progress-indicator {
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    padding: 0.75rem;
    border: 1px solid #dee2e6;
}

/* 进度步骤容器 */
.progress-steps {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* 单个进度步骤 */
.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 0 0 auto;
    cursor: pointer;
    transition: all 0.3s ease;
}

.progress-step .step-icon {
    width: 1.75rem;
    height: 1.75rem;
    border-radius: 50%;
    background-color: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.25rem;
    transition: all 0.3s ease;
    border: 2px solid #e9ecef;
}

.progress-step .step-label {
    font-size: 0.6875rem;
    font-weight: 500;
    color: #6c757d;
    text-align: center;
    transition: color 0.3s ease;
}

/* 已完成状态 */
.progress-step.completed .step-icon {
    background-color: #198754;
    color: #fff;
    border-color: #198754;
}

.progress-step.completed .step-label {
    color: #198754;
    font-weight: 600;
}

/* 连接线 */
.progress-line {
    flex: 1;
    height: 2px;
    background-color: #e9ecef;
    margin: 0 0.375rem;
    position: relative;
    top: -0.75rem;
    transition: background-color 0.3s ease;
}

.progress-line.completed {
    background-color: #198754;
}



/* 移动端优化 */
@media (max-width: 768px) {
    .progress-indicator {
        padding: 0.5rem;
    }

    .progress-step .step-icon {
        width: 1.5rem;
        height: 1.5rem;
    }

    .progress-step .step-icon .icon {
        width: 12px;
        height: 12px;
    }

    .progress-step .step-label {
        font-size: 0.625rem;
    }

    .progress-line {
        margin: 0 0.25rem;
        top: -0.625rem;
    }
}

/* ========== 品牌Logo显示样式 ========== */

/* Logo容器 */
.brand-logo-container {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 102px;
    height: 36px;
    border: 1px solid #e6e7e9;
    border-radius: 4px;
    background-color: #f8f9fa;
    overflow: hidden;
    position: relative;
}

.brand-logo-container img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    transition: transform 0.2s ease-in-out;
}

.brand-logo-container:hover img {
    transform: scale(1.1);
}

/* Logo加载状态 */
.brand-logo-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: #f8f9fa;
    color: #6c757d;
    font-size: 12px;
}

/* Logo错误状态 */
.brand-logo-error {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: #f8f9fa;
    transition: opacity 0.2s ease-in-out;
}

.brand-logo-error svg {
    opacity: 0.7;
    transition: opacity 0.2s ease-in-out;
}

.brand-logo-error:hover svg {
    opacity: 1;
}

/* Logo占位符 */
.brand-logo-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: #e9ecef;
    transition: opacity 0.2s ease-in-out;
}

.brand-logo-placeholder svg {
    opacity: 0.6;
    transition: opacity 0.2s ease-in-out;
}

.brand-logo-placeholder:hover svg {
    opacity: 0.8;
}

/* ========== 状态标识样式 ========== */


/* ========== 表格样式优化 ========== */

/* 品牌表格特殊列样式 */
.brand-table .logo-column {
    width: 80px;
    text-align: center;
    vertical-align: middle;
}

.brand-table .code-column {
    width: 120px;
    font-family: 'Courier New', monospace;
    font-weight: 500;
}

.brand-table .name-column {
    min-width: 150px;
    font-weight: 500;
}

.brand-table .status-column {
    width: 100px;
    text-align: center;
}

.brand-table .origin-column {
    width: 100px;
    text-align: center;
}

.brand-table .owner-column {
    min-width: 200px;
    max-width: 300px;
}

/* 表格交互样式已移动到 custom.css 的公共表格样式 */

/* ========== 操作按钮样式 ========== */

/* 操作按钮和表格样式已移动到 custom.css 的公共样式 */

/* ========== 响应式设计 ========== */

/* 移动端按钮优化已移动到 custom.css */
@media (max-width: 768px) {

    .brand-logo-container {
        width: 50px;
        height: 35px;
    }

    .brand-table .logo-column {
        width: 60px;
    }

    .brand-table .code-column {
        width: 100px;
        font-size: 0.875rem;
    }

    .brand-table .name-column {
        min-width: 120px;
    }

    .brand-table .status-column,
    .brand-table .origin-column {
        width: 80px;
    }

    .brand-table .owner-column {
        min-width: 150px;
        max-width: 200px;
    }

    /* 移动端表格文字大小调整 */
    .brand-table {
        font-size: 0.875rem;
    }

    .brand-status-badge,
    .brand-origin-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }
}

/* 平板端优化 */
@media (min-width: 769px) and (max-width: 992px) {
    .brand-table .owner-column {
        max-width: 250px;
    }
}

/* ========== 加载状态样式 ========== */

/* 表格加载状态 */
.brand-table-loading {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
}

.brand-table-loading .spinner-border {
    margin-bottom: 1rem;
}

/* 空状态样式 */
.brand-table-empty {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.brand-table-empty .empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* ========== 工具提示样式 ========== */

/* Logo预览工具提示 */
.brand-logo-tooltip {
    max-width: 300px;
}

.brand-logo-tooltip img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
}

/* ========== 打印样式 ========== */

@media print {
    .brand-logo-container {
        border: 1px solid #000;
        background-color: #fff;
    }

    .brand-status-badge,
    .brand-origin-badge {
        border: 1px solid #000;
        background-color: #fff !important;
        color: #000 !important;
    }

    .btn-list {
        display: none;
    }
}

/* ========== 新增品牌模态框样式 ========== */

/* 模态框整体样式 */
#add-brand-modal .modal-dialog {
    max-width: 800px;
}

#add-brand-modal .modal-content {
    border-radius: 0.75rem;
    border: none;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

#add-brand-modal .modal-header {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e2e8f0;
    border-radius: 0.75rem 0.75rem 0 0;
    padding: 1.25rem 1.5rem;
}

#add-brand-modal .modal-title {
    color: #1e293b;
    font-weight: 600;
    display: flex;
    align-items: center;
    margin-bottom: 0;
}

/* 表单样式 */
#add-brand-form .form-label.required::after {
    content: " *";
    color: #dc3545;
}

#add-brand-form .form-control:focus,
#add-brand-form .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* LOGO上传组件样式 */
.logo-upload-container {
    position: relative;
    width: 100%;
    min-height: 200px;
}

/* 上传界面样式 */
.logo-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.5rem;
    padding: 2rem 1rem;
    text-align: center;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-upload-area:hover {
    border-color: #0d6efd;
    background-color: #f0f8ff;
}

.logo-upload-area.dragover {
    border-color: #0d6efd;
    background-color: #e7f3ff;
    transform: scale(1.02);
}

.logo-upload-content {
    pointer-events: none;
}

.logo-upload-content p {
    margin: 0;
    color: #6c757d;
}

.logo-upload-content .small {
    font-size: 0.875rem;
}

/* 裁剪界面样式 */
.logo-crop-area {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    background-color: #fff;
    padding: 1rem;
}

.logo-crop-container {
    position: relative;
    width: 100%;
    height: 200px;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

#logo-crop-image {
    max-width: 100%;
    max-height: 100%;
    display: block;
}

.logo-crop-toolbar {
    margin-top: 0.75rem;
    padding: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
}

.logo-crop-info {
    font-size: 0.875rem;
    color: #6c757d;
}

.logo-crop-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

/* 预览界面样式 */
.logo-preview-area {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1rem;
    background-color: #fff;
    text-align: center;
}

.logo-preview-container {
    margin-bottom: 0.75rem;
}

#logo-preview-img {
    max-width: 100%;
    max-height: 150px;
    border-radius: 0.375rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.logo-preview-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

/* 商标网截图上传组件样式 */
.trademark-upload-container {
    position: relative;
    width: 100%;
    min-height: 200px;
}

/* 商标网截图上传界面样式 */
.trademark-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.5rem;
    padding: 2rem 1rem;
    text-align: center;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.trademark-upload-area:hover {
    border-color: #0d6efd;
    background-color: #f0f8ff;
}

.trademark-upload-area.dragover {
    border-color: #0d6efd;
    background-color: #e7f3ff;
    transform: scale(1.02);
}

.trademark-upload-content {
    pointer-events: none;
}

.trademark-upload-content p {
    margin: 0;
    color: #6c757d;
}

.trademark-upload-content .small {
    font-size: 0.875rem;
}

/* 商标网截图预览界面样式 */
.trademark-preview-area {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1rem;
    background-color: #fff;
    text-align: center;
}

.trademark-preview-container {
    margin-bottom: 0.75rem;
}

#trademark-preview-img {
    max-width: 100%;
    max-height: 150px;
    border-radius: 0.375rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.trademark-preview-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

/* 上传状态显示样式 */
.logo-upload-status,
.trademark-upload-status {
    margin: 0.75rem 0;
    padding: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
}

.upload-status-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.status-text {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6c757d;
}

.status-text.status-ready {
    color: #6c757d;
}

.status-text.status-uploading {
    color: #0d6efd;
}

.status-text.status-success {
    color: #198754;
}

.status-text.status-error {
    color: #dc3545;
}

.upload-progress {
    flex: 1;
    min-width: 120px;
    max-width: 200px;
}

.upload-progress .progress {
    height: 0.5rem;
    background-color: #e9ecef;
}

.upload-progress .progress-bar {
    background-color: #0d6efd;
    transition: width 0.3s ease;
}

/* 按钮状态样式 */
.btn .btn-spinner {
    margin-left: 0.5rem;
}

.btn:disabled .btn-text {
    opacity: 0.6;
}







/* 多选下拉框样式 */
#international-classification {
    min-height: 120px;
}


/* 响应式设计 */
@media (max-width: 768px) {
    #add-brand-modal .modal-dialog {
        max-width: 95%;
        margin: 1rem auto;
    }

    #add-brand-modal .modal-header {
        padding: 1rem;
    }

    /* LOGO上传组件移动端优化 */
    .logo-upload-container {
        min-height: 180px;
    }

    .logo-upload-area {
        padding: 1.5rem 0.75rem;
        min-height: 180px;
    }

    .logo-crop-container {
        height: 180px;
    }

    .logo-crop-toolbar {
        padding: 0.5rem;
    }

    .logo-crop-actions {
        flex-direction: column;
        gap: 0.25rem;
    }

    .logo-crop-actions .btn {
        width: 100%;
    }

    .logo-preview-actions {
        flex-direction: column;
        align-items: center;
        gap: 0.25rem;
    }

    .logo-preview-actions .btn {
        width: 100%;
        max-width: 120px;
    }

    /* 商标网截图上传组件移动端优化 */
    .trademark-upload-container {
        min-height: 180px;
    }

    .trademark-upload-area {
        padding: 1.5rem 0.75rem;
        min-height: 180px;
    }

    .trademark-preview-actions {
        flex-direction: column;
        align-items: center;
        gap: 0.25rem;
    }

    .trademark-preview-actions .btn {
        width: 100%;
        max-width: 120px;
    }

    /* 上传状态移动端优化 */
    .upload-status-info {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .upload-progress {
        min-width: auto;
        max-width: none;
    }


}

/* 超小屏幕适配 */
@media (max-width: 480px) {
    .logo-upload-container {
        min-height: 160px;
    }

    .logo-upload-area {
        padding: 1rem 0.5rem;
        min-height: 160px;
    }

    .logo-crop-container {
        height: 160px;
    }

    .logo-crop-toolbar .btn-group .btn {
        padding: 0.25rem 0.375rem;
        font-size: 0.75rem;
    }

    /* 商标网截图上传超小屏幕适配 */
    .trademark-upload-container {
        min-height: 160px;
    }

    .trademark-upload-area {
        padding: 1rem 0.5rem;
        min-height: 160px;
    }
}

/* ========== 国际分类组件移动端优化 ========== */

@media (max-width: 768px) {
    .classification-dropdown-menu {
        max-height: 250px;
    }

    .classification-dropdown-list {
        max-height: 150px;
    }

    .classification-option {
        padding: 12px;
    }

    .classification-actions-header .btn {
        padding: 6px 10px;
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .classification-dropdown-header {
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
    }

    .classification-actions-header {
        justify-content: center;
    }
}

/* iOS Safari 兼容性 */
@supports (-webkit-touch-callout: none) {
    .logo-upload-area,
    .trademark-upload-area {
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        user-select: none;
    }

    .modal-dialog {
        margin-top: env(safe-area-inset-top, 1.75rem);
        margin-bottom: env(safe-area-inset-bottom, 1.75rem);
    }

    .classification-dropdown-menu {
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        user-select: none;
    }
}
