/* 商品状态查询模块样式 */

/* 查询表单响应式布局优化 */
@media (max-width: 991.98px) {
    /* 在中等屏幕以下，商品名称字段占满整行 */
    #status-filter-fields .col-lg-4 {
        margin-bottom: 0.75rem;
    }

    /* 筛选字段在中等屏幕下保持合理的宽度 */
    #status-filter-fields .col-lg-2 {
        margin-bottom: 0.75rem;
    }
}

@media (max-width: 575.98px) {
    /* 在小屏幕下，所有字段都占满整行 */
    #status-filter-fields .col-lg-2 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

/* 表单字段标签和帮助文本的紧凑样式 */
.form-text {
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

/* 确保下拉选择框在紧凑布局下的可读性 */
.form-select {
    font-size: 0.875rem;
}

/* 优化查询按钮区域的间距 */
.query-card .card-body {
    padding-bottom: 1rem;
}

/* 下架原因详情弹窗样式 - 使用公共弹窗组件样式 */
/* 弹窗样式已移动到 custom.css 的 .popover-component 类 */
/* JavaScript 中需要将 .delist-reason-popover 改为 .popover-component */

/* ========== 表格文本省略号统一样式 ========== */

/* 表格单元格文本省略号样式 - 单行显示+省略号 */
.table-cell-ellipsis {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

/* 商品名称列专用省略号样式 */
.table-cell-ellipsis.product-name {
    max-width: 300px;
}

/* 分类名称列专用省略号样式 */
.table-cell-ellipsis.category-name {
    max-width: 150px;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .table-cell-ellipsis.product-name {
        max-width: 180px;
    }

    .table-cell-ellipsis.category-name {
        max-width: 120px;
    }
}
