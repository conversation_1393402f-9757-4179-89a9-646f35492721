/**
 * 按钮状态管理器
 * 用于防止重复点击和显示加载状态
 */
class ButtonManager {
    constructor() {
        this.activeButtons = new Map(); // 存储活跃的按钮状态
        this.defaultTimeout = 30000; // 默认30秒超时
    }

    /**
     * 设置按钮为加载状态
     * @param {string|HTMLElement} buttonSelector - 按钮选择器或DOM元素
     * @param {string} loadingText - 加载时显示的文字
     * @param {number} timeout - 超时时间（毫秒）
     */
    setLoading(buttonSelector, loadingText = '查询中...', timeout = this.defaultTimeout) {
        const button = typeof buttonSelector === 'string' 
            ? document.querySelector(buttonSelector) 
            : buttonSelector;
            
        if (!button) {
            console.warn('按钮未找到:', buttonSelector);
            return;
        }

        // 保存原始状态
        const originalState = {
            disabled: button.disabled,
            innerHTML: button.innerHTML,
            className: button.className
        };

        // 生成唯一ID
        const buttonId = this.generateButtonId(button);
        
        // 存储原始状态
        this.activeButtons.set(buttonId, {
            button: button,
            originalState: originalState,
            timeoutId: null
        });

        // 设置加载状态
        button.disabled = true;
        button.innerHTML = `
            <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
            ${loadingText}
        `;

        // 设置超时自动恢复
        const timeoutId = setTimeout(() => {
            this.resetButton(buttonId);
            console.warn('按钮操作超时，自动恢复:', buttonSelector);
        }, timeout);

        // 更新超时ID
        this.activeButtons.get(buttonId).timeoutId = timeoutId;
    }

    /**
     * 重置按钮状态
     * @param {string|HTMLElement} buttonSelector - 按钮选择器或DOM元素
     */
    reset(buttonSelector) {
        const button = typeof buttonSelector === 'string' 
            ? document.querySelector(buttonSelector) 
            : buttonSelector;
            
        if (!button) {
            console.warn('按钮未找到:', buttonSelector);
            return;
        }

        const buttonId = this.generateButtonId(button);
        this.resetButton(buttonId);
    }

    /**
     * 内部方法：重置按钮状态
     * @param {string} buttonId - 按钮ID
     */
    resetButton(buttonId) {
        const buttonData = this.activeButtons.get(buttonId);
        if (!buttonData) {
            return;
        }

        const { button, originalState, timeoutId } = buttonData;

        // 清除超时
        if (timeoutId) {
            clearTimeout(timeoutId);
        }

        // 恢复原始状态
        button.disabled = originalState.disabled;
        button.innerHTML = originalState.innerHTML;
        button.className = originalState.className;

        // 移除记录
        this.activeButtons.delete(buttonId);

    }

    /**
     * 生成按钮唯一ID
     * @param {HTMLElement} button - 按钮DOM元素
     * @returns {string} 唯一ID
     */
    generateButtonId(button) {
        // 尝试使用ID
        if (button.id) {
            return button.id;
        }
        
        // 尝试使用data属性
        if (button.dataset.buttonId) {
            return button.dataset.buttonId;
        }
        
        // 使用按钮文本和位置生成ID
        const text = button.textContent.trim();
        const rect = button.getBoundingClientRect();
        return `btn_${text}_${Math.round(rect.left)}_${Math.round(rect.top)}`;
    }

    /**
     * 检查按钮是否处于加载状态
     * @param {string|HTMLElement} buttonSelector - 按钮选择器或DOM元素
     * @returns {boolean} 是否处于加载状态
     */
    isLoading(buttonSelector) {
        const button = typeof buttonSelector === 'string' 
            ? document.querySelector(buttonSelector) 
            : buttonSelector;
            
        if (!button) {
            return false;
        }

        const buttonId = this.generateButtonId(button);
        return this.activeButtons.has(buttonId);
    }

    /**
     * 重置所有按钮状态
     */
    resetAll() {
        const buttonIds = Array.from(this.activeButtons.keys());
        buttonIds.forEach(buttonId => {
            this.resetButton(buttonId);
        });
    }

    /**
     * 包装异步函数，自动管理按钮状态
     * @param {string|HTMLElement} buttonSelector - 按钮选择器或DOM元素
     * @param {Function} asyncFunction - 异步函数
     * @param {string} loadingText - 加载文字
     * @param {number} timeout - 超时时间
     * @returns {Promise} 包装后的Promise
     */
    async wrapAsync(buttonSelector, asyncFunction, loadingText = '处理中...', timeout = this.defaultTimeout) {
        this.setLoading(buttonSelector, loadingText, timeout);
        
        try {
            const result = await asyncFunction();
            this.reset(buttonSelector);
            return result;
        } catch (error) {
            this.reset(buttonSelector);
            throw error;
        }
    }
}

// 创建全局实例
const buttonManager = new ButtonManager();

// 导出到全局作用域
window.buttonManager = buttonManager;
