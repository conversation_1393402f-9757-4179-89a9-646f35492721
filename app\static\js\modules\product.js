/**
 * 商品状态查询模块 JavaScript
 * 使用模块化设计模式，提供完整的数据采集、筛选、分页、导出功能
 */

// 商品状态模块对象
const ProductStatusModule = {
    // 模块初始化
    init() {
        console.log('商品状态查询模块已加载');

        // 检查ELECTRON访问权限
        this.checkElectronAccess();

        // 绑定表单提交事件
        this.bindFormEvents();

        // 绑定上架状态变化事件
        this.bindShelfStatusEvents();

        // 初始化界面
        this.handleShelfStatusChange();

        // 加载保存的查询条件
        this.loadQueryConditionsFromStorage();

        // 初始化数据筛选器
        this.initializeDataFilter();

        // 初始化多行编辑功能
        this.initializeMultilineEdit();
    },

    // 检查ELECTRON访问权限
    checkElectronAccess() {
        if (typeof authManager !== 'undefined' && authManager.userInfo) {
            const loginPrompt = document.getElementById('login-prompt');
            const electronAccessDenied = document.getElementById('electron-access-denied');
            const mainContent = document.getElementById('main-content');

            if (authManager.token && authManager.userInfo.local_username) {
                // 用户已登录，检查ELECTRON权限
                if (authManager.userInfo.electron_access) {
                    // 有ELECTRON权限，显示主要内容
                    if (loginPrompt) loginPrompt.style.display = 'none';
                    if (electronAccessDenied) electronAccessDenied.style.display = 'none';
                    if (mainContent) mainContent.style.display = 'block';
                } else {
                    // 没有ELECTRON权限，显示权限不足提示
                    if (loginPrompt) loginPrompt.style.display = 'none';
                    if (electronAccessDenied) electronAccessDenied.style.display = 'block';
                    if (mainContent) mainContent.style.display = 'none';
                }
            } else {
                // 用户未登录，显示登录提示
                if (loginPrompt) loginPrompt.style.display = 'block';
                if (electronAccessDenied) electronAccessDenied.style.display = 'none';
                if (mainContent) mainContent.style.display = 'none';
            }
        }
    },

    // 绑定表单事件
    bindFormEvents() {
        const form = document.getElementById('product-form');
        if (form) {
            form.addEventListener('submit', this.handleFormSubmit.bind(this));
        }
    },



    // 绑定上架状态变化事件
    bindShelfStatusEvents() {
        const shelfStatusSelect = document.getElementById('shelf-status-select');
        if (shelfStatusSelect) {
            shelfStatusSelect.addEventListener('change', this.handleShelfStatusChange.bind(this));
        }
    },

    // 初始化数据筛选器
    initializeDataFilter() {
        if (!dataFilter) {
            dataFilter = new DataFilter('data-filter-container', {
                storageKey: 'product-filter',
                useTabSeparatedCopy: true, // 使用制表符分隔的复制格式，适合Excel粘贴
                onFilterChange: (filteredData) => {
                    this.displayFilteredResults(filteredData);
                }
            });
        }
    },



    // 处理上架状态变化
    handleShelfStatusChange() {
        const shelfStatusSelect = document.getElementById('shelf-status-select');
        const delistTypeSelect = document.getElementById('delist-type-select');

        if (!shelfStatusSelect || !delistTypeSelect) return;

        // 仅当选择"下架"时启用下架类型字段
        if (shelfStatusSelect.value === 'DOWN_SHELF') {
            delistTypeSelect.disabled = false;
            delistTypeSelect.parentElement.querySelector('.form-text').textContent = '选择下架的具体类型';
        } else {
            delistTypeSelect.disabled = true;
            delistTypeSelect.value = ''; // 清空选择
            delistTypeSelect.parentElement.querySelector('.form-text').textContent = '仅在选择"下架"时可用';
        }
    },

    // 处理表单提交
    async handleFormSubmit(event) {
        event.preventDefault();

        // 检查ELECTRON权限
        if (!this.checkElectronAccessForQuery()) {
            return;
        }

        const form = event.target;
        const formData = new FormData(form);
        const productName = formData.get('product_name')?.trim() || '';
        const productSku = formData.get('product_sku')?.trim() || '';
        const shelfStatus = formData.get('shelf_status') || '';
        const delistType = formData.get('delist_type') || '';
        const approvalStatus = formData.get('approval_status') || '';

        // 验证输入
        if (!productName && !productSku) {
            authManager.showNotification('请输入商品名称或SKU编码', 'warning');
            return;
        }

        // 保存查询条件到本地存储（仅保存SKU字段）
        this.saveQueryConditionsToStorage({
            product_sku: productSku
        });

        // 使用按钮管理器包装查询操作
        const queryButton = document.getElementById('query-submit-btn');

        try {
            await buttonManager.wrapAsync(queryButton, async () => {
                // 构建查询参数
                const queryParams = {
                    name: productName || null,
                    sku: productSku || null
                };

                // 添加筛选条件
                if (shelfStatus) queryParams.shelf_status = shelfStatus;
                if (delistType) queryParams.delist_type = delistType;
                if (approvalStatus) queryParams.approval_status = approvalStatus;

                // 执行智能整合查询
                await this.executeIntegratedProductQuery(queryParams);
            }, '查询商品状态中...');

        } catch (error) {
            console.error('商品状态查询错误:', error);
            authManager.showNotification('查询过程中发生错误', 'error');
        }
    },

    // 检查ELECTRON访问权限（用于查询）
    checkElectronAccessForQuery() {
        if (typeof authManager === 'undefined' || !authManager.userInfo) {
            authManager.showNotification('请先登录', 'warning');
            return false;
        }

        if (!authManager.userInfo.electron_access) {
            authManager.showNotification('需要电子超市平台权限才能进行商品状态查询', 'error');
            return false;
        }

        return true;
    },

    // 执行智能整合查询（商品状态 + 审核状态）
    async executeIntegratedProductQuery(queryParams) {
        try {
            const response = await fetch('/api/v1/data/product-integrated', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authManager.token}`
                },
                body: JSON.stringify(queryParams)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.success) {
                this.setupFilter(result.data, 'integrated');

                // 使用后端返回的详细消息
                const message = result.message || `智能整合查询成功，找到 ${result.data.length} 条商品记录`;
                authManager.showNotification(message, 'success');

                // 记录查询统计
                if (typeof dashboardStats !== 'undefined') {
                    dashboardStats.recordQuery('product', true);
                }
            } else {
                authManager.showNotification(result.message, 'error');
                this.setupFilter([], 'integrated');

                // 记录查询统计（失败）
                if (typeof dashboardStats !== 'undefined') {
                    dashboardStats.recordQuery('product', false);
                }
            }
        } catch (error) {
            console.error('智能整合查询失败:', error);
            authManager.showNotification('查询失败，请检查网络连接', 'error');
            this.setupFilter([], 'integrated');

            // 记录查询统计（异常）
            if (typeof dashboardStats !== 'undefined') {
                dashboardStats.recordQuery('product', false);
            }
        }
    },




    // 设置筛选器
    setupFilter(data, queryType) {
        if (!dataFilter) {
            this.initializeDataFilter();
        }

        // 存储当前查询类型到全局变量，供筛选器使用
        window.currentQueryType = queryType;

        // 初始化分页管理器
        if (!window.paginationManager_productStatus) {
            window.paginationManager_productStatus = new PaginationManager({
                moduleId: 'productStatus',
                pageSize: 20,
                renderCallback: () => {
                    const currentPageData = window.paginationManager_productStatus.getCurrentPageData();
                    this.displayFilteredResults(currentPageData, true);
                }
            });
        }

        // 定义列结构（智能整合查询：包含完整的11字段）
        const columns = [
            { key: 'sku', label: 'SKU编码' },
            { key: 'name', label: '商品名称' },
            { key: 'categoryFullName', label: '商品品目' },
            { key: 'price', label: '售价' },
            { key: 'shelfStatus', label: '上架状态' },
            { key: 'delistType', label: '下架类型' },
            { key: 'delistReason', label: '下架原因' },
            { key: 'approvalStatus', label: '审核状态' },
            { key: 'approvalContent', label: '驳回原因' },
            { key: 'userName', label: '操作员' },
            { key: 'updateTime', label: '更新时间' }
        ];

        // 设置数据和列
        dataFilter.setData(data, columns);

        // 设置分页数据
        window.paginationManager_productStatus.setData(data, data);

        // 显示第一页数据
        const firstPageData = window.paginationManager_productStatus.getCurrentPageData();
        this.displayFilteredResults(firstPageData, true);
    },

    // 显示筛选后的结果
    displayFilteredResults(data, isPaginated = false) {
        const resultsContainer = document.getElementById('product-results');
        const countElement = document.getElementById('product-count');

        if (!resultsContainer || !countElement) return;

        // 使用智能整合查询表格渲染器
        const customRenderer = this.renderIntegratedCustomTable.bind(this);
        let tableHTML = '';

        if (isPaginated && window.paginationManager_productStatus) {
            // 分页模式
            const paginationInfo = window.paginationManager_productStatus.getInfo();
            countElement.textContent = `第${paginationInfo.startItem}-${paginationInfo.endItem}条，共${paginationInfo.totalItems}条记录`;

            // 生成表格
            tableHTML = dataFilter.generateSelectableTable(data, customRenderer);

            // 添加分页控件
            const paginationHTML = window.paginationManager_productStatus.generatePaginationHTML();
            tableHTML += paginationHTML;
        } else {
            // 非分页模式
            countElement.textContent = `${data.length} 条记录`;
            tableHTML = dataFilter.generateSelectableTable(data, customRenderer);
        }

        resultsContainer.innerHTML = tableHTML;
    },

    // 自定义智能整合表格渲染器（智能列隐藏版）
    renderIntegratedCustomTable(data, filterInstance) {
        if (!data || data.length === 0) {
            return `
                <div class="empty">
                    <div class="empty-img">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDEyOCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik02NCA5NkM4MC41Njg1IDk2IDk0IDgyLjU2ODUgOTQgNjZDOTQgNDkuNDMxNSA4MC41Njg1IDM2IDY0IDM2QzQ3LjQzMTUgMzYgMzQgNDkuNDMxNSAzNCA2NkMzNCA4Mi41Njg1IDQ3LjQzMTUgOTYgNjQgOTZaIiBzdHJva2U9IiNEQURERTIiIHN0cm9rZS13aWR0aD0iMiIvPgo8cGF0aCBkPSJNNTcuMzMzMyA2NkM1Ny4zMzMzIDYyLjMxODEgNjAuMzE4MSA1OS4zMzMzIDY0IDU5LjMzMzNDNjcuNjgxOSA1OS4zMzMzIDcwLjY2NjcgNjIuMzE4MSA3MC42NjY3IDY2QzcwLjY2NjcgNjkuNjgxOSA2Ny42ODE5IDcyLjY2NjcgNjQgNzIuNjY2N0M2MC4zMTgxIDcyLjY2NjcgNTcuMzMzMyA2OS42ODE5IDU3LjMzMzMgNjZaIiBmaWxsPSIjREFEREUyIi8+Cjwvc3ZnPgo=" alt="暂无数据">
                    </div>
                    <p class="empty-title">暂无商品数据</p>
                    <p class="empty-subtitle text-muted">请调整查询条件或筛选条件后重试</p>
                </div>
            `;
        }

        // 获取可见列配置
        const visibleColumns = this.getVisibleColumns(data);
        const hiddenColumns = this.getHiddenColumns(visibleColumns);

        // 生成隐藏列提示信息
        let hiddenColumnsInfo = '';
        // if (hiddenColumns.length > 0) {
        //     hiddenColumnsInfo = `
        //         <div class="alert alert-info mb-3">
        //             <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
        //                 <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
        //                 <path d="M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0"></path>
        //                 <path d="M12 9h.01"></path>
        //                 <path d="M11 12h1v4h1"></path>
        //             </svg>
        //             <strong>智能优化</strong>：已隐藏无数据的列：${hiddenColumns.join('、')}
        //         </div>
        //     `;
        // }

        // 生成动态表头
        let tableHTML = `
            <div class="table-responsive">
                ${hiddenColumnsInfo}
                <table class="table table-vcenter">
                    <thead>
                        <tr>
                            ${visibleColumns.map(col => `<th>${col.label}</th>`).join('')}
                        </tr>
                    </thead>
                    <tbody>
        `;

        // 生成动态表格行
        data.forEach((item, index) => {
            const isSelected = filterInstance.selectedRows.has(index);
            const isNoDataSku = item.name === '未找到商品信息';
            const rowClass = isSelected ? 'table-active' : (isNoDataSku ? 'table-warning' : '');

            tableHTML += `
                <tr data-row-index="${index}" class="${rowClass}"
                    onclick="ProductStatusModule.handleRowClick(${index}, event)"
                    style="cursor: pointer;">
                    ${visibleColumns.map(col => this.renderTableCell(col, item, index)).join('')}
                </tr>
            `;
        });

        tableHTML += `
                    </tbody>
                </table>
            </div>
        `;

        // 更新选择状态UI
        setTimeout(() => {
            filterInstance.updateAllRowSelectionUI();
        }, 0);

        return tableHTML;
    },

    // 获取可见列配置
    getVisibleColumns(data) {
        // 定义所有可能的列
        const allColumns = [
            { key: 'sku', label: 'SKU编码', required: true },
            { key: 'name', label: '商品名称', required: true },
            { key: 'categoryFullName', label: '商品品目', required: true },
            { key: 'price', label: '售价', required: true },
            { key: 'shelfStatus', label: '上架状态', required: false },
            { key: 'delistType', label: '下架类型', required: false },
            { key: 'delistReason', label: '下架原因', required: false },
            { key: 'approvalStatus', label: '审核状态', required: false },
            { key: 'approvalContent', label: '驳回原因', required: false },
            { key: 'userName', label: '操作员', required: false },
            { key: 'updateTime', label: '更新时间', required: true },
            { key: 'actions', label: '操作', required: true }
        ];

        // 检测每列是否有有效数据
        const visibleColumns = allColumns.filter(column => {
            // 必需列始终显示
            if (column.required) return true;

            // 检查该列是否在所有行中都为空
            return data.some(item => this.hasValidData(item[column.key]));
        });

        return visibleColumns;
    },

    // 获取隐藏列名称列表
    getHiddenColumns(visibleColumns) {
        const allColumnLabels = ['SKU编码', '商品名称', '商品品目', '售价', '上架状态', '下架类型', '下架原因', '审核状态', '驳回原因', '操作员', '更新时间', '操作'];
        const visibleColumnLabels = visibleColumns.map(col => col.label);
        return allColumnLabels.filter(label => !visibleColumnLabels.includes(label));
    },

    // 检查数据是否有效（非空）
    hasValidData(value) {
        if (value === null || value === undefined) return false;
        if (typeof value === 'string') {
            const trimmed = value.trim();
            return trimmed !== '' &&
                   trimmed !== '-' &&
                   trimmed !== '暂无' &&
                   trimmed !== 'null' &&
                   trimmed !== 'undefined';
        }
        return true;
    },

    // 渲染表格单元格
    renderTableCell(column, item, index) {
        const value = item[column.key];

        switch (column.key) {
            case 'sku':
                return `<td><code>${this.escapeHtml(value || '')}</code></td>`;

            case 'name':
                return `
                    <td>
                        <div class="table-cell-ellipsis product-name" title="${this.escapeHtml(value || '')}">
                            ${this.escapeHtml(value || '')}
                        </div>
                    </td>
                `;

            case 'categoryFullName':
                return `
                    <td>
                        <div class="table-cell-ellipsis category-name" title="${this.escapeHtml(value || '')}">
                            ${this.escapeHtml(value || '')}
                        </div>
                    </td>
                `;

            case 'price':
                return `<td><span class="text-success fw-bold">${this.formatPrice(value)}</span></td>`;

            case 'shelfStatus':
                return `<td>${this.formatShelfStatus(value)}</td>`;

            case 'delistType':
                return `<td>${this.formatDelistType(value)}</td>`;

            case 'delistReason':
                return `<td>${this.formatDelistReason(value, index)}</td>`;

            case 'approvalStatus':
                return `<td>${this.formatApprovalStatus(value)}</td>`;

            case 'approvalContent':
                return `<td>${this.formatApprovalContent(value, index)}</td>`;

            case 'userName':
                return `<td><span class="text-muted">${this.escapeHtml(value || '')}</span></td>`;

            case 'updateTime':
                return `<td><span class="text-muted">${this.formatDateTime(value)}</span></td>`;

            case 'actions':
                return this.renderActionButtons(item, index);

            default:
                return `<td>${this.escapeHtml(value || '')}</td>`;
        }
    },
    // 渲染操作按钮列
    renderActionButtons(item, index) {
        const sku = item.sku || '';
        const productId = item.productId || item.id || '';

        // 转义HTML字符以防止XSS攻击
        const escapedSku = this.escapeHtml(sku);
        const escapedProductId = this.escapeHtml(productId);

        return `
            <td>
                <div class="btn-list flex-nowrap">
                    <button class="btn btn-sm btn-outline-primary"
                            onclick="event.stopPropagation(); ProductStatusModule.viewProductDetail('${escapedSku}', '${escapedProductId}')"
                            title="查看商品详情">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"></path>
                            <path d="M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6"></path>
                        </svg>
                    </button>
                </div>
            </td>
        `;
    },





    // 格式化价格
    formatPrice(price) {
        if (price === null || price === undefined) return '0.00';
        return parseFloat(price).toFixed(2);
    },

    // 格式化上架状态
    formatShelfStatus(status) {
        const statusMap = {
            'UP_SHELF': '<span class="badge bg-success">上架</span>',
            'DOWN_SHELF': '<span class="badge bg-danger">下架</span>'
        };
        return statusMap[status] || '';
    },

    // 格式化下架类型
    formatDelistType(type) {
        const typeMap = {
            'OPERATE_DELIST': '<span class="badge bg-warning">运营下架</span>',
            'SYSTEM_DELIST': '<span class="badge bg-danger">系统下架</span>',
            'OPERATE_DELIST_FOREVER': '<span class="badge bg-dark">永久下架</span>',
            'SUPPLIER_DELIST': '<span class="badge bg-info">供应商下架</span>'
        };
        return typeMap[type] || '';
    },

    // 格式化下架原因（带CSS省略号和查看详情功能）
    formatDelistReason(reason, rowIndex = null) {
        if (!reason || reason.trim() === '') {
            return '<span class="text-muted">-</span>';
        }

        const escapedReason = this.escapeHtml(reason);
        const uniqueId = rowIndex !== null ? `reason-${rowIndex}` : `reason-${Date.now()}`;

        return `
            <div class="d-flex align-items-center">
                <span class="text-muted table-cell-ellipsis me-2" style="max-width: 120px;" title="${escapedReason}">${escapedReason}</span>
                <button class="btn btn-sm btn-ghost-secondary"
                        onclick="event.stopPropagation(); showDelistReasonDetail('${uniqueId}', this)"
                        title="查看完整下架原因"
                        data-full-reason="${escapedReason}">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-sm" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"></path>
                        <path d="M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6"></path>
                    </svg>
                </button>
            </div>
        `;
    },

    // 格式化审核状态
    formatApprovalStatus(status) {
        const statusMap = {
            'APPROVE': '<span class="badge bg-warning">待审核</span>',
            'REJECT': '<span class="badge bg-danger">驳回</span>',
            'END': '<span class="badge bg-success">结束</span>'
        };
        return statusMap[status] || '';
    },

    // 格式化审核内容（驳回原因，带CSS省略号和查看详情功能）
    formatApprovalContent(content, rowIndex = null) {
        if (!content || content.trim() === '' || content === '-') {
            return '';
        }

        const escapedContent = this.escapeHtml(content);
        const uniqueId = rowIndex !== null ? `approval-${rowIndex}` : `approval-${Date.now()}`;

        return `
            <div class="d-flex align-items-center">
                <span class="text-muted table-cell-ellipsis me-2" style="max-width: 120px;" title="${escapedContent}">${escapedContent}</span>
                <button class="btn btn-sm btn-ghost-secondary"
                        onclick="event.stopPropagation(); showApprovalContentDetail('${uniqueId}', this)"
                        title="查看完整驳回原因"
                        data-full-content="${escapedContent}">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-sm" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"></path>
                        <path d="M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6"></path>
                    </svg>
                </button>
            </div>
        `;
    },

    // 格式化日期时间
    formatDateTime(dateTimeStr) {
        if (!dateTimeStr) return '';
        try {
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch (error) {
            return dateTimeStr;
        }
    },

    // HTML转义函数
    escapeHtml(text) {
        if (text === null || text === undefined) {
            return '';
        }
        const div = document.createElement('div');
        div.textContent = String(text);
        return div.innerHTML;
    },

    // 保存查询条件到本地存储（仅保存SKU字段）
    saveQueryConditionsToStorage(conditions) {
        try {
            // 只保存SKU字段到本地存储
            const skuOnlyConditions = {
                product_sku: conditions.product_sku || ''
            };
            localStorage.setItem('product-query-conditions', JSON.stringify(skuOnlyConditions));
        } catch (error) {
            console.warn('无法保存查询条件到本地存储:', error);
        }
    },

    // 从本地存储加载查询条件（仅恢复SKU字段）
    loadQueryConditionsFromStorage() {
        try {
            const stored = localStorage.getItem('product-query-conditions');
            if (stored) {
                const conditions = JSON.parse(stored);
                const form = document.getElementById('product-form');
                if (form) {
                    if (conditions.product_sku) {
                        // 修复：现在使用input而不是textarea
                        const skuField = form.querySelector('input[name="product_sku"]');
                        if (skuField) {
                            skuField.value = conditions.product_sku;

                            // 同步到多行编辑器
                            const textarea = document.getElementById('productSkuTextarea');
                            if (textarea) {
                                textarea.value = conditions.product_sku.replace(/[\s\t]+/g, '\n').replace(/\n+/g, '\n');
                            }
                        }
                    }

                    // 触发相关事件更新UI状态
                    this.handleShelfStatusChange();
                }
            }
        } catch (error) {
            console.warn('无法从本地存储加载查询条件:', error);
        }
    },

    // 处理行点击事件（智能选择）
    handleRowClick(rowIndex, event) {
        // 检查是否在文本选择模式
        const selection = window.getSelection();
        if (selection.toString().length > 0) {
            // 如果有文本被选中，不触发行选择
            return;
        }

        // 检查点击的目标元素
        const target = event.target;

        // 如果点击的是可选择的文本内容，允许文本选择
        if (target.tagName === 'CODE' ||
            target.tagName === 'SPAN' ||
            target.closest('.table-cell-ellipsis') ||
            target.closest('.badge')) {
            // 延迟检查是否真的在选择文本
            setTimeout(() => {
                const newSelection = window.getSelection();
                if (newSelection.toString().length === 0) {
                    // 没有选择文本，触发行选择
                    dataFilter.toggleRowSelection(rowIndex, event);
                }
            }, 10);
        } else {
            // 点击其他区域，直接触发行选择
            dataFilter.toggleRowSelection(rowIndex, event);
        }
    },

    // 初始化多行编辑功能
    initializeMultilineEdit() {
        // 绑定输入框和文本域的同步事件
        this.bindMultilineSync('productSku');

        // 绑定下拉菜单显示事件，自动聚焦到文本域
        this.bindDropdownFocus();

        // 初始化实时统计功能
        this.initializeRealtimeCount();
    },

    // 绑定单行输入框和多行文本域的双向同步
    bindMultilineSync(fieldType) {
        const inputId = 'productSkuInput';
        const textareaId = 'productSkuTextarea';

        const input = document.getElementById(inputId);
        const textarea = document.getElementById(textareaId);

        if (!input || !textarea) {
            console.warn(`多行编辑元素未找到: ${fieldType}`);
            return;
        }

        // 单行输入框变化时，同步到多行文本域（空格转换为换行）并保存到localStorage
        input.addEventListener('input', () => {
            // 使用正则表达式替换空格、制表符为换行符
            textarea.value = input.value.replace(/[\s\t]+/g, '\n').replace(/\n+/g, '\n');

            // 保存到localStorage
            this.saveQueryConditionsToStorage({
                product_sku: input.value
            });
        });

        // 多行文本域变化时，同步到单行输入框（换行转换为空格）
        textarea.addEventListener('input', () => {
            // 使用正则表达式替换换行符为空格，并清理多余空格
            input.value = textarea.value.replace(/\r\n|\n/g, ' ').replace(/\s+/g, ' ').trim();

            // 保存到localStorage
            this.saveQueryConditionsToStorage({
                product_sku: input.value
            });
        });
    },

    // 绑定下拉菜单显示时的自动聚焦
    bindDropdownFocus() {
        // SKU下拉菜单
        const skuDropdown = document.querySelector('#productSkuInput').closest('.input-group').querySelector('.dropdown');
        if (skuDropdown) {
            skuDropdown.addEventListener('shown.bs.dropdown', () => {
                const input = document.getElementById('productSkuInput');
                const textarea = document.getElementById('productSkuTextarea');
                if (input && textarea) {
                    // 将输入框的内容同步到多行编辑器（空格转换为换行）
                    if (input.value.trim()) {
                        textarea.value = input.value.replace(/[\s\t]+/g, '\n').replace(/\n+/g, '\n');
                    } else {
                        textarea.value = '';
                    }
                    // 更新统计信息
                    this.updateRealtimeCount(textarea.value);
                    // 自动聚焦到文本域
                    setTimeout(() => textarea.focus(), 100);
                }
            });
        }
    },

    // 应用多行编辑内容
    applyMultilineEdit(fieldType) {
        const inputId = 'productSkuInput';
        const textareaId = 'productSkuTextarea';

        const input = document.getElementById(inputId);
        const textarea = document.getElementById(textareaId);

        if (!input || !textarea) {
            console.warn(`多行编辑元素未找到: ${fieldType}`);
            return;
        }

        // 将多行文本域的内容转换为单行格式并应用到输入框
        const cleanedValue = textarea.value
            .split(/\r\n|\n/)  // 按换行符分割
            .map(line => line.trim())  // 去除每行的前后空白
            .filter(line => line.length > 0)  // 过滤空行
            .join(' ');  // 用空格连接

        input.value = cleanedValue;

        // 保存到localStorage
        this.saveQueryConditionsToStorage({
            product_sku: cleanedValue
        });

        // 关闭下拉菜单
        const dropdown = input.closest('.input-group').querySelector('.dropdown');
        if (dropdown) {
            const bsDropdown = bootstrap.Dropdown.getInstance(dropdown.querySelector('[data-bs-toggle="dropdown"]'));
            if (bsDropdown) {
                bsDropdown.hide();
            }
        }

        // 显示应用成功的提示
        const itemCount = cleanedValue ? cleanedValue.split(' ').length : 0;
        if (itemCount > 0) {
            authManager.showNotification(`SKU编码多行编辑已应用，共 ${itemCount} 个项目`, 'success');
        } else {
            authManager.showNotification('SKU编码内容已清空', 'info');
        }
    },

    // 取消多行编辑
    cancelMultilineEdit(fieldType) {
        const inputId = 'productSkuInput';
        const textareaId = 'productSkuTextarea';

        const input = document.getElementById(inputId);
        const textarea = document.getElementById(textareaId);

        if (!input || !textarea) {
            console.warn(`多行编辑元素未找到: ${fieldType}`);
            return;
        }

        // 恢复文本域内容为输入框的内容
        textarea.value = input.value.replace(/[\s\t]+/g, '\n').replace(/\n+/g, '\n');

        // 关闭下拉菜单
        const dropdown = input.closest('.input-group').querySelector('.dropdown');
        if (dropdown) {
            const bsDropdown = bootstrap.Dropdown.getInstance(dropdown.querySelector('[data-bs-toggle="dropdown"]'));
            if (bsDropdown) {
                bsDropdown.hide();
            }
        }
    },

    // 初始化实时统计功能
    initializeRealtimeCount() {
        // 为SKU多行编辑器绑定实时统计
        const productSkuTextarea = document.getElementById('productSkuTextarea');
        if (productSkuTextarea) {
            productSkuTextarea.addEventListener('input', () => {
                this.updateRealtimeCount(productSkuTextarea.value);
            });
        }
    },

    // 更新实时统计显示
    updateRealtimeCount(content) {
        const countInfo = document.getElementById('productSkuCountInfo');
        const countText = document.getElementById('productSkuCountText');

        if (!countInfo || !countText) {
            return;
        }

        // 统计有效SKU数量
        const count = this.countValidItems(content);

        if (count === 0) {
            // 没有输入
            countText.textContent = '暂无输入';
            countInfo.className = 'form-text d-flex align-items-center gap-2 text-muted';
        } else {
            // 有输入
            countText.textContent = `已输入 ${count} 个SKU编码`;
            countInfo.className = 'form-text d-flex align-items-center gap-2 text-success';
        }
    },

    // 统计有效项目数量
    countValidItems(content) {
        if (!content || !content.trim()) {
            return 0;
        }

        // 按换行符分割并过滤空行
        const lines = content.split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0);

        return lines.length;
    },

    // 查看商品详情
    viewProductDetail(sku, productId) {
        // 检查ELECTRON权限
        if (!this.checkElectronAccessForQuery()) {
            return;
        }

        if (!sku && !productId) {
            authManager.showNotification('缺少必要的参数信息', 'warning');
            return;
        }

        // 构建详情页面URL
        const detailUrl = `/modules/product/detail?productId=${encodeURIComponent(productId || sku)}&sku=${encodeURIComponent(sku)}`;

        // 在新标签页中打开详情页面
        window.open(detailUrl, '_blank');

        authManager.showNotification(`正在打开商品详情: ${sku || productId}`, 'info');
    },

};

// 全局函数，保持向后兼容性
function clearProductQueryCondition() {
    const form = document.getElementById('product-form');
    if (form) {
        form.reset();
        // 清空本地存储（包括SKU字段）
        try {
            localStorage.removeItem('product-query-conditions');
        } catch (error) {
            console.warn('无法清空本地存储:', error);
        }
        // 重置UI状态
        ProductStatusModule.handleShelfStatusChange();
        authManager.showNotification('查询条件已清空', 'info');
    }
}

// 显示下架原因详情弹窗
function showDelistReasonDetail(uniqueId, buttonElement) {
    const fullReason = buttonElement.getAttribute('data-full-reason');
    if (!fullReason) return;

    // 移除已存在的弹窗
    hideDelistReasonDetail();

    // 创建弹窗元素
    const popover = document.createElement('div');
    popover.id = 'delist-reason-popover';
    popover.className = 'delist-reason-popover';
    popover.innerHTML = `
        <div class="popover-content">
            <div class="popover-header">
                <span class="popover-title">详情</span>
                <button type="button" class="btn-close" onclick="hideDelistReasonDetail()" aria-label="关闭"></button>
            </div>
            <div class="popover-body">
                <p class="mb-0">${ProductStatusModule.escapeHtml(fullReason)}</p>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.appendChild(popover);

    // 计算位置
    const buttonRect = buttonElement.getBoundingClientRect();
    const popoverRect = popover.getBoundingClientRect();

    // 默认显示在按钮右侧
    let left = buttonRect.right + 10;
    let top = buttonRect.top + (buttonRect.height / 2) - (popoverRect.height / 2);

    // 检查是否超出视窗右边界
    if (left + popoverRect.width > window.innerWidth - 20) {
        // 显示在按钮左侧
        left = buttonRect.left - popoverRect.width - 10;
    }

    // 检查是否超出视窗上边界
    if (top < 20) {
        top = 20;
    }

    // 检查是否超出视窗下边界
    if (top + popoverRect.height > window.innerHeight - 20) {
        top = window.innerHeight - popoverRect.height - 20;
    }

    // 应用位置
    popover.style.left = left + 'px';
    popover.style.top = top + 'px';

    // 添加显示动画
    setTimeout(() => {
        popover.classList.add('show');
    }, 10);

    // 点击外部关闭弹窗
    setTimeout(() => {
        document.addEventListener('click', handleOutsideClick);
    }, 100);
}

// 隐藏下架原因详情弹窗
function hideDelistReasonDetail() {
    const popover = document.getElementById('delist-reason-popover');
    if (popover) {
        popover.classList.remove('show');
        setTimeout(() => {
            if (popover.parentNode) {
                popover.parentNode.removeChild(popover);
            }
        }, 200);
    }

    // 移除外部点击监听器（如果没有其他弹窗）
    const approvalPopover = document.getElementById('approval-content-popover');
    if (!approvalPopover) {
        document.removeEventListener('click', handleOutsideClick);
    }
}

// 显示审核内容详情弹窗
function showApprovalContentDetail(uniqueId, buttonElement) {
    const fullContent = buttonElement.getAttribute('data-full-content');
    if (!fullContent) return;

    // 移除已存在的弹窗
    hideApprovalContentDetail();

    // 创建弹窗元素
    const popover = document.createElement('div');
    popover.id = 'approval-content-popover';
    popover.className = 'delist-reason-popover'; // 复用相同的样式
    popover.innerHTML = `
        <div class="popover-content">
            <div class="popover-header">
                <span class="popover-title">驳回原因详情</span>
                <button type="button" class="btn-close" onclick="hideApprovalContentDetail()" aria-label="关闭"></button>
            </div>
            <div class="popover-body">
                <p class="mb-0">${ProductStatusModule.escapeHtml(fullContent)}</p>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.appendChild(popover);

    // 计算位置
    const buttonRect = buttonElement.getBoundingClientRect();
    const popoverRect = popover.getBoundingClientRect();

    // 默认显示在按钮右侧
    let left = buttonRect.right + 10;
    let top = buttonRect.top + (buttonRect.height / 2) - (popoverRect.height / 2);

    // 检查是否超出视窗右边界
    if (left + popoverRect.width > window.innerWidth - 20) {
        // 显示在按钮左侧
        left = buttonRect.left - popoverRect.width - 10;
    }

    // 检查是否超出视窗上边界
    if (top < 20) {
        top = 20;
    }

    // 检查是否超出视窗下边界
    if (top + popoverRect.height > window.innerHeight - 20) {
        top = window.innerHeight - popoverRect.height - 20;
    }

    // 应用位置
    popover.style.left = left + 'px';
    popover.style.top = top + 'px';

    // 添加显示动画
    setTimeout(() => {
        popover.classList.add('show');
    }, 10);

    // 点击外部关闭弹窗
    setTimeout(() => {
        document.addEventListener('click', handleOutsideClick);
    }, 100);
}

// 隐藏审核内容详情弹窗
function hideApprovalContentDetail() {
    const popover = document.getElementById('approval-content-popover');
    if (popover) {
        popover.classList.remove('show');
        setTimeout(() => {
            if (popover.parentNode) {
                popover.parentNode.removeChild(popover);
            }
        }, 200);
    }

    // 移除外部点击监听器（如果没有其他弹窗）
    const delistPopover = document.getElementById('delist-reason-popover');
    if (!delistPopover) {
        document.removeEventListener('click', handleOutsideClick);
    }
}

// 处理外部点击事件
function handleOutsideClick(event) {
    const popover = document.getElementById('delist-reason-popover');
    if (popover && !popover.contains(event.target)) {
        hideDelistReasonDetail();
    }

    const approvalPopover = document.getElementById('approval-content-popover');
    if (approvalPopover && !approvalPopover.contains(event.target)) {
        hideApprovalContentDetail();
    }
}

// 重写authManager的显示主要内容方法，添加ELECTRON权限检查
if (typeof authManager !== 'undefined') {
    const originalShowMainContent = authManager.showMainContent;
    authManager.showMainContent = function() {
        originalShowMainContent.call(this);
        // 在商品状态模块中，需要额外检查ELECTRON权限
        setTimeout(() => ProductStatusModule.checkElectronAccess(), 100);
    };

    const originalShowLoginPrompt = authManager.showLoginPrompt;
    authManager.showLoginPrompt = function() {
        originalShowLoginPrompt.call(this);
        // 在商品状态模块中，隐藏ELECTRON权限提示
        const electronAccessDenied = document.getElementById('electron-access-denied');
        if (electronAccessDenied) electronAccessDenied.style.display = 'none';
    };
}

// 创建全局ProductModule对象，用于HTML模板中的多行编辑功能调用
window.ProductModule = {
    applyMultilineEdit: function(fieldType) {
        return ProductStatusModule.applyMultilineEdit(fieldType);
    },
    cancelMultilineEdit: function(fieldType) {
        return ProductStatusModule.cancelMultilineEdit(fieldType);
    }
};

// 模块初始化
document.addEventListener('DOMContentLoaded', function() {
    ProductStatusModule.init();
});
