"""
需求凭证查询API路由
"""

from fastapi import APIRouter, HTTPException, Depends, Request
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
import time
from app.core.auth import get_current_user
from app.services.modules.xqpz_service import xqpz_service
from app.services.analytics_service import analytics_service
from app.core.logging import logger


router = APIRouter()


class XqpzQueryRequest(BaseModel):
    """需求凭证查询请求模型"""
    askSheetCode: Optional[str] = Field(None, description="比价单号")
    sku: Optional[str] = Field(None, description="商品SKU编码")


class XqpzQueryResponse(BaseModel):
    """需求凭证查询响应模型"""
    success: bool = Field(..., description="查询是否成功")
    message: str = Field(..., description="响应消息")
    data: list = Field(default_factory=list, description="查询结果数据")
    total: Optional[int] = Field(None, description="总记录数")
    permission_error: Optional[bool] = Field(None, description="是否为权限相关错误")


def _parse_query_content(request: XqpzQueryRequest) -> Dict[str, Any]:
    """
    解析查询内容，提取SKU列表等信息用于统计

    Args:
        request: 查询请求对象

    Returns:
        包含解析结果的字典
    """
    try:
        # 解析比价单号
        ask_sheet_codes = []
        if request.askSheetCode:
            ask_sheet_codes = xqpz_service._parse_input_values(request.askSheetCode)

        # 解析SKU
        skus = []
        if request.sku:
            skus = xqpz_service._parse_input_values(request.sku)

        # 合并所有查询项
        all_items = ask_sheet_codes + skus

        return {
            'sku_list': all_items,  # 统一使用sku_list字段名
            'sku_count': len(all_items),
            'ask_sheet_codes': ask_sheet_codes,
            'skus': skus,
            'query_type': 'batch' if len(all_items) > 1 else 'single'
        }
    except Exception as e:
        logger.warning(f"解析查询内容失败: {str(e)}")
        return {
            'sku_list': [],
            'sku_count': 0,
            'ask_sheet_codes': [],
            'skus': [],
            'query_type': 'unknown'
        }


@router.post("/xqpz", response_model=XqpzQueryResponse)
async def query_xqpz(
    request: XqpzQueryRequest,
    http_request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    查询需求凭证数据
    支持单个和批量查询（空格、换行符、制表符分隔）

    需要ELECTRON平台访问权限
    """
    start_time = time.time()
    try:
        logger.info(f"用户 {current_user.get('username')} 请求需求凭证查询")
        
        # 检查用户是否有ELECTRON访问权限
        if not current_user.get('electron_access'):
            logger.warning(f"用户 {current_user.get('username')} 没有ELECTRON访问权限")

            # 记录权限错误统计
            try:
                query_content = _parse_query_content(request)
                await analytics_service.record_query_log(
                    module='xqpz',
                    user=current_user.get('username', 'unknown'),
                    platform=current_user.get('platform', 'ELECTRON'),
                    success=False,
                    response_time=(time.time() - start_time) * 1000,
                    status_code=403,
                    error_message="需要电子超市平台访问权限才能查询需求凭证",
                    client_ip=current_user.get('client_ip'),
                    user_agent=http_request.headers.get('user-agent'),
                    query_params=query_content,
                    result_count=0
                )
            except Exception as stats_error:
                logger.warning(f"记录权限错误统计失败: {str(stats_error)}")

            raise HTTPException(
                status_code=403,
                detail="需要电子超市平台访问权限才能查询需求凭证"
            )
        
        # 获取ELECTRON访问令牌
        electron_token = current_user.get('electron_token')
        if not electron_token:
            logger.warning(f"用户 {current_user.get('username')} 缺少ELECTRON访问令牌")
            raise HTTPException(
                status_code=401,
                detail="缺少电子超市平台访问令牌"
            )
        
        # 验证查询参数
        if not request.askSheetCode and not request.sku:
            raise HTTPException(
                status_code=400,
                detail="请提供比价单号或商品SKU编码"
            )
        
        # 获取客户端IP
        client_ip = current_user.get('client_ip')

        # 解析查询内容用于统计
        query_content = _parse_query_content(request)

        logger.info(f"需求凭证查询 - 查询类型: {query_content['query_type']}, 项目数量: {query_content['sku_count']}")

        # 调用服务查询数据（支持批量查询）
        result = await xqpz_service.query_voucher_batch_from_input(
            access_token=electron_token,
            client_ip=client_ip,
            ask_sheet_code_input=request.askSheetCode,
            sku_input=request.sku
        )

        logger.info(f"需求凭证查询完成 - 用户: {current_user.get('username')}, 结果: {result.get('success')}")

        # 记录查询统计（异步，不影响响应）
        try:
            # 计算响应时间
            response_time = (time.time() - start_time) * 1000

            await analytics_service.record_query_log(
                module='xqpz',
                user=current_user.get('username', 'unknown'),
                platform=current_user.get('platform', 'ELECTRON'),
                success=result.get('success', False),
                response_time=response_time,
                status_code=200 if result.get('success', False) else 500,
                error_message=result.get('message') if not result.get('success', False) else None,
                client_ip=client_ip,
                user_agent=http_request.headers.get('user-agent'),
                query_params=query_content,
                result_count=len(result.get('data', []))
            )
        except Exception as e:
            logger.warning(f"记录需求凭证查询统计失败: {str(e)}")

        return XqpzQueryResponse(
            success=result['success'],
            message=result['message'],
            data=result['data'],
            total=result.get('total'),
            permission_error=result.get('permission_error')
        )
        
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"需求凭证查询API异常: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"查询过程中发生内部错误: {str(e)}"
        )
