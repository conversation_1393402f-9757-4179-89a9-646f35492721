/* 需求凭证查询模块样式 */

/* 操作按钮和表格样式已移动到 custom.css 的公共样式 */

/* 响应式优化 */
@media (max-width: 992px) {
    .table th:nth-child(8),
    .table td:nth-child(8) {
        max-width: 120px;
        min-width: 100px;
    }

    .table td:nth-child(8) .text-truncate {
        max-width: 120px !important;
    }
}

/* ========== 表格文本省略号统一样式 ========== */

/* 表格单元格文本省略号样式 - 单行显示+省略号 */
.table-cell-ellipsis {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

/* 商品名称列专用省略号样式 */
.table-cell-ellipsis.product-name {
    max-width: 300px;
}

/* 客户单位列专用省略号样式 */
.table-cell-ellipsis.customer-unit {
    max-width: 220px;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .table-cell-ellipsis.product-name {
        max-width: 180px;
    }

    .table-cell-ellipsis.customer-unit {
        max-width: 160px;
    }
}

/* 表格样式已移动到 custom.css 的公共表格样式 */
