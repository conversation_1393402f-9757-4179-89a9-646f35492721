"""
统一Playwright管理器模块
负责浏览器实例创建、<PERSON><PERSON>获取、页面访问等通用功能
解决各平台服务代码重复问题
"""
import asyncio
import logging
import sys
import concurrent.futures
from typing import Dict, List, Any, Optional, Tuple
from app.core.logging import logger


class PlaywrightManager:
    """统一Playwright管理器类"""
    
    def __init__(self, platform_name: str, base_domain: str):
        """
        初始化Playwright管理器
        Args:
            platform_name: 平台名称（用于日志）
            base_domain: 基础域名（用于Cookie设置）
        """
        self.platform_name = platform_name
        self.base_domain = base_domain
        self.cookies_initialized = False
        self.session_info = {}
        
        # 浏览器配置
        self.browser_args = [
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-blink-features=AutomationControlled',
            '--disable-extensions',
            '--disable-plugins',
            '--disable-images',
            '--disable-javascript',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor'
        ]
        
        # 浏览器上下文配置
        self.context_config = {
            'viewport': {'width': 1920, 'height': 1080},
            'ignore_https_errors': True
        }
    
    def get_random_user_agent(self) -> str:
        """获取随机User-Agent"""
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
        import random
        return random.choice(user_agents)
    
    async def initialize_cookies(self, target_url: str, session_obj: Any) -> bool:
        """
        初始化Cookies（统一入口方法）
        Args:
            target_url: 目标网站URL
            session_obj: requests.Session对象
        Returns:
            bool: 初始化是否成功
        """
        if self.cookies_initialized:
            logger.info(f"{self.platform_name}平台Cookies已初始化，跳过重复获取")
            return True

        logger.info(f"{self.platform_name}平台开始获取初始cookies和session信息（线程池模式）...")
        start_time = asyncio.get_event_loop().time()

        try:
            # 使用线程池执行Playwright以避免事件循环冲突
            loop = asyncio.get_event_loop()
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(self._run_playwright_in_thread, target_url)
                cookies_data = await loop.run_in_executor(None, future.result)

            if cookies_data:
                # 处理获取到的cookies
                cookies, session_info = cookies_data

                # 将cookies添加到requests session
                for cookie in cookies:
                    session_obj.cookies.set(
                        cookie['name'],
                        cookie['value'],
                        domain=cookie.get('domain', self.base_domain)
                    )

                self.session_info = session_info
                self.cookies_initialized = True

                elapsed_time = asyncio.get_event_loop().time() - start_time
                logger.info(f"{self.platform_name}平台Playwright任务完成: 获取 {len(cookies)} 个cookies，耗时 {elapsed_time:.2f}秒")
                logger.info(f"{self.platform_name}平台浏览器已关闭，后续请求将由Requests处理")
                return True
            else:
                logger.error(f"{self.platform_name}平台Playwright线程执行失败")
                return False

        except Exception as e:
            logger.error(f"{self.platform_name}平台Playwright获取cookies失败: {str(e)}")
            return False
    
    def _run_playwright_in_thread(self, target_url: str):
        """
        在独立线程中运行Playwright，避免事件循环冲突
        这是解决uvicorn兼容性问题的关键方法
        """
        try:
            # 在新线程中设置正确的事件循环策略
            if sys.platform == "win32":
                asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

            # 创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                # 在新事件循环中运行Playwright
                return loop.run_until_complete(self._playwright_get_cookies(target_url))
            finally:
                loop.close()

        except Exception as e:
            logger.error(f"{self.platform_name}平台Playwright线程执行异常: {str(e)}")
            return None
    
    async def _playwright_get_cookies(self, target_url: str) -> Optional[Tuple[List[Dict], Dict]]:
        """
        实际的Playwright cookie获取逻辑
        Args:
            target_url: 目标网站URL
        Returns:
            Tuple[List[Dict], Dict]: (cookies列表, session信息) 或 None
        """
        try:
            # 动态导入playwright以避免在主线程中的问题
            from playwright.async_api import async_playwright

            async with async_playwright() as p:
                # 启动浏览器
                browser = await p.chromium.launch(
                    headless=True,
                    args=self.browser_args
                )

                # 创建浏览器上下文
                context = await browser.new_context(
                    user_agent=self.get_random_user_agent(),
                    **self.context_config
                )

                page = await context.new_page()

                # 访问目标页面获取cookies和session信息
                logger.info(f"正在访问{self.platform_name}平台主页获取认证信息...")
                await page.goto(target_url,
                              wait_until="networkidle",
                              timeout=30000)

                # 等待页面完全加载
                await asyncio.sleep(3)

                # 获取cookies
                cookies = await context.cookies()
                logger.info(f"{self.platform_name}平台获取到 {len(cookies)} 个cookies")

                # 获取页面基础信息作为session信息
                session_info = {
                    'url': page.url,
                    'title': await page.title(),
                    'user_agent': self.get_random_user_agent()
                }

                # 关闭浏览器
                await browser.close()
                logger.info(f"{self.platform_name}平台浏览器已关闭")

                return cookies, session_info

        except Exception as e:
            logger.error(f"{self.platform_name}平台Playwright获取cookies异常: {str(e)}")
            return None
    
    def get_session_info(self) -> Dict:
        """获取当前session信息"""
        return {
            'platform': self.platform_name,
            'cookies_initialized': self.cookies_initialized,
            'session_info': self.session_info
        }
    
    def reset_cookies(self):
        """重置Cookie状态（用于重新初始化）"""
        self.cookies_initialized = False
        self.session_info = {}
        logger.info(f"{self.platform_name}平台Cookie状态已重置")

    async def validate_cookies(self, test_url: str) -> bool:
        """
        验证Cookie是否仍然有效
        Args:
            test_url: 用于测试的URL
        Returns:
            bool: Cookie是否有效
        """
        if not self.cookies_initialized:
            return False

        try:
            import aiohttp
            timeout = aiohttp.ClientTimeout(total=10)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                # 使用当前cookies进行测试请求
                async with session.get(test_url) as response:
                    # 检查响应状态和内容，判断Cookie是否有效
                    if response.status == 401 or response.status == 403:
                        logger.warning(f"{self.platform_name}平台Cookie已失效 - HTTP {response.status}")
                        return False
                    elif response.status == 200:
                        # 可以添加更多的内容检查逻辑
                        content = await response.text()
                        if "登录" in content or "login" in content.lower():
                            logger.warning(f"{self.platform_name}平台Cookie已失效 - 页面要求登录")
                            return False
                        return True
                    else:
                        logger.warning(f"{self.platform_name}平台Cookie验证异常 - HTTP {response.status}")
                        return False

        except Exception as e:
            logger.error(f"{self.platform_name}平台Cookie验证异常: {str(e)}")
            return False

    async def refresh_cookies_if_needed(self, target_url: str, session_obj: Any, test_url: str = None) -> bool:
        """
        检查并在需要时刷新Cookie
        Args:
            target_url: 目标网站URL
            session_obj: requests.Session对象
            test_url: 用于测试的URL，默认使用target_url
        Returns:
            bool: 刷新是否成功
        """
        test_url = test_url or target_url

        # 如果Cookie未初始化，直接初始化
        if not self.cookies_initialized:
            logger.info(f"{self.platform_name}平台Cookie未初始化，开始初始化...")
            return await self.initialize_cookies(target_url, session_obj)

        # 验证当前Cookie是否有效
        is_valid = await self.validate_cookies(test_url)
        if is_valid:
            logger.debug(f"{self.platform_name}平台Cookie验证通过，无需刷新")
            return True

        # Cookie失效，需要刷新
        logger.info(f"{self.platform_name}平台Cookie已失效，开始自动刷新...")
        self.reset_cookies()

        # 重新初始化Cookie
        success = await self.initialize_cookies(target_url, session_obj)
        if success:
            logger.info(f"{self.platform_name}平台Cookie自动刷新成功")
        else:
            logger.error(f"{self.platform_name}平台Cookie自动刷新失败")

        return success
