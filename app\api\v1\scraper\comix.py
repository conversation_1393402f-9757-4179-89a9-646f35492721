"""
齐心平台数据采集API路由
"""

from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from typing import Dict, Any
import json
import asyncio
from app.services.scraper.comix_service import comix_service
from app.services.analytics_service import analytics_service
from app.core.logging import logger
from app.core.config import get_settings
from app.core.auth import get_client_ip


router = APIRouter()


class ComixScraperRequest(BaseModel):
    """齐心平台数据采集请求模型"""
    query_content: str = Field(..., description="查询内容（SKU列表，支持多种分隔符）")


class ComixSingleSkuRequest(BaseModel):
    """齐心平台单个SKU重试请求模型"""
    sku: str = Field(..., description="SKU编码")


@router.post("/comix")
async def scrape_comix_data(request: ComixScraperRequest, http_request: Request) -> Dict[str, Any]:
    """
    齐心平台数据采集（批量模式）

    支持两种模式：
    - 直接模式：使用requests直接请求
    - Playwright模式：先获取cookie再使用requests请求
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        logger.info(f"收到齐心平台数据采集请求 - 查询内容: {request.query_content}, 客户端IP: {client_ip}")

        # 设置客户端IP头部
        comix_service.set_client_ip_headers(client_ip)

        # 解析查询内容为SKU列表用于统计
        sku_list = comix_service._parse_query_content(request.query_content)

        # 记录查询日志到分析统计
        await analytics_service.record_query_log(
            module="scraper-comix",
            user="友商数据采集",
            platform="SCRAPER",
            success=True,
            query_params={
                'sku_list': sku_list,
                'sku_count': len(sku_list),
                'query_type': 'batch'
            }
        )

        # 执行数据采集（模式由配置文件控制）
        result = await comix_service.scrape_data(
            query_content=request.query_content
        )

        logger.info(f"齐心平台数据采集完成 - 成功: {result.get('success_count', 0)}/{result.get('total_count', 0)} 条记录")

        return result

    except Exception as e:
        logger.error(f"齐心平台数据采集API异常: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"齐心平台数据采集失败: {str(e)}"
        )


@router.post("/comix/stream")
async def scrape_comix_data_stream(request: ComixScraperRequest, http_request: Request):
    """
    齐心平台数据采集（流式响应模式）

    实时返回每个SKU的采集结果，支持前端异步更新
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        logger.info(f"收到齐心平台流式数据采集请求 - 客户端IP: {client_ip}")

        # 设置客户端IP头部
        comix_service.set_client_ip_headers(client_ip)

        # 解析查询内容为SKU列表用于统计
        sku_list = comix_service._parse_query_content(request.query_content)

        # 记录查询日志到分析统计
        await analytics_service.record_query_log(
            module="scraper-comix",
            user="友商数据采集",
            platform="SCRAPER",
            success=True,
            query_params={
                'sku_list': sku_list,
                'sku_count': len(sku_list),
                'query_type': 'stream'
            }
        )

        async def generate_stream():
            """生成流式响应数据"""
            try:
                # 解析SKU列表
                skus = comix_service._parse_query_content(request.query_content)

                if not skus:
                    yield f"data: {json.dumps({'type': 'error', 'message': '未找到有效的SKU编码'})}\n\n"
                    return

                # 发送初始化信息
                yield f"data: {json.dumps({'type': 'init', 'total': len(skus), 'skus': skus})}\n\n"

                # 执行流式数据采集（模式由配置文件控制）
                async for result in comix_service.scrape_data_stream(
                    query_content=request.query_content
                ):
                    yield f"data: {json.dumps(result)}\n\n"

            except Exception as e:
                logger.error(f"流式数据采集异常: {str(e)}")
                yield f"data: {json.dumps({'type': 'error', 'message': f'数据采集失败: {str(e)}'})}\n\n"

        return StreamingResponse(
            generate_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream"
            }
        )

    except Exception as e:
        logger.error(f"齐心平台流式数据采集API异常: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"齐心平台流式数据采集失败: {str(e)}"
        )


@router.post("/comix/retry")
async def retry_single_sku(request: ComixSingleSkuRequest, http_request: Request) -> Dict[str, Any]:
    """
    重试单个SKU的数据采集
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        logger.info(f"收到单个SKU重试请求 - SKU: {request.sku}, 客户端IP: {client_ip}")

        # 设置客户端IP头部
        comix_service.set_client_ip_headers(client_ip)

        # 执行单个SKU采集（模式由配置文件控制）
        result = await comix_service.scrape_single_sku(
            sku=request.sku
        )

        logger.info(f"单个SKU重试完成 - SKU: {request.sku}, 状态: {result.get('status')}")

        return {
            "success": True,
            "data": result,
            "message": f"SKU {request.sku} 重试完成"
        }

    except Exception as e:
        logger.error(f"单个SKU重试API异常: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"SKU重试失败: {str(e)}"
        )





@router.get("/comix/config")
async def get_comix_config() -> Dict[str, Any]:
    """
    获取齐心平台采集配置信息

    返回当前的Playwright模式配置状态，用于前端显示
    """
    try:
        settings = get_settings()

        return {
            "success": True,
            "data": {
                "platform": "comix",
                "platform_name": "齐心",
                "playwright_mode": settings.COMIX_USE_PLAYWRIGHT,
                "mode_text": "Playwright增强模式" if settings.COMIX_USE_PLAYWRIGHT else "HTTP直接模式"
            },
            "message": "配置获取成功"
        }

    except Exception as e:
        logger.error(f"获取齐心平台配置失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取配置失败: {str(e)}"
        )
