{% extends "base.html" %}

{% block title %}{{ module_name }} - {{ app_name }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="/static/css/modules/xqd-detail.css?ver={{ app_version }}">
{% endblock %}

{% block content %}
<div class="page-wrapper">
    <!-- 页面头部 -->
    <div class="page-header d-print-none">
        <div class="container-xl">
            <div class="row g-2 align-items-center">
                <div class="col">
                    <!-- 面包屑导航 -->
                    <div class="page-pretitle">
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="/">首页</a></li>
                                <li class="breadcrumb-item"><a href="/modules/xqd">需求单查询</a></li>
                                <li class="breadcrumb-item active" aria-current="page">详情</li>
                            </ol>
                        </nav>
                    </div>
                    <h2 class="page-title">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"></path>
                            <path d="M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6"></path>
                        </svg>
                        需求单详情
                    </h2>
                    <div class="text-muted mt-1" id="detail-subtitle">
                        查看需求单的详细信息和产品列表
                    </div>
                </div>
                <div class="col-auto ms-auto d-print-none">
                    <div class="btn-list">
                        <a href="/modules/xqd" class="btn btn-outline-secondary">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M5 12l14 0"></path>
                                <path d="M5 12l6 6"></path>
                                <path d="M5 12l6 -6"></path>
                            </svg>
                            返回列表
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页面内容 -->
    <div class="page-body">
        <div class="container-xl">
            <!-- 登录提示 -->
            <div class="row" id="login-prompt" style="display: none;">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center">
                            <h3>需要登录</h3>
                            <p class="text-muted">请先登录到电子超市以查看需求单详情</p>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#login-modal">
                                立即登录
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- ELECTRON权限不足提示 -->
            <div class="row" id="electron-access-denied" style="display: none;">
                <div class="col-12">
                    <div class="card border-warning">
                        <div class="card-body text-center">
                            <div class="text-warning mb-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-lg" width="48" height="48" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M12 9v2m0 4v.01"></path>
                                    <path d="M5 19h14a2 2 0 0 0 1.84 -2.75l-7.1 -12.25a2 2 0 0 0 -3.5 0l-7.1 12.25a2 2 0 0 0 1.75 2.75"></path>
                                </svg>
                            </div>
                            <h3 class="text-warning">电子超市平台权限不足</h3>
                            <p class="text-muted">您当前的账号没有电子超市平台访问权限，无法查看需求单详情。</p>
                            <p class="text-muted">请联系管理员开通权限或使用具有相应权限的账号登录。</p>
                            <div class="btn-list">
                                <button class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#login-modal">
                                    重新登录
                                </button>
                                <a href="/modules/xqd" class="btn btn-outline-secondary">
                                    返回列表
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div id="main-content" style="display: none;">
            <!-- 基础信息卡片 -->
            <div class="card detail-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M14 3v4a1 1 0 0 0 1 1h4"></path>
                            <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"></path>
                        </svg>
                        基础信息
                    </h3>
                </div>
                <div class="card-body position-relative" id="detail-info-container">
                    <!-- 加载状态 -->
                    <div class="loading-overlay" id="detail-loading">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                    
                    <!-- 详情内容 -->
                    <div id="detail-content" style="display: none;">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="detail-field">
                                    <div class="label">比价单号</div>
                                    <div class="value" id="detail-askSheetCode">-</div>
                                </div>
                                <div class="detail-field">
                                    <div class="label">比价单名称</div>
                                    <div class="value" id="detail-askSheetName">-</div>
                                </div>
                                <div class="detail-field">
                                    <div class="label">比价状态</div>
                                    <div class="value" id="detail-askSheetStatus">-</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="detail-field">
                                    <div class="label">报价开始时间</div>
                                    <div class="value" id="detail-answerBeginTime">-</div>
                                </div>
                                <div class="detail-field">
                                    <div class="label">报价结束时间</div>
                                    <div class="value" id="detail-answerEndTime">-</div>
                                </div>
                                <div class="detail-field">
                                    <div class="label">报价有效期</div>
                                    <div class="value" id="detail-answerEffTime">-</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="detail-field">
                                    <div class="label">联系人</div>
                                    <div class="value" id="detail-askSheetUser">-</div>
                                </div>
                                <div class="detail-field">
                                    <div class="label">联系电话</div>
                                    <div class="value" id="detail-askSheetUserMobile">-</div>
                                </div>
                                <div class="detail-field">
                                    <div class="label">采购单位</div>
                                    <div class="value" id="detail-purchaserName">-</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="detail-field">
                                    <div class="label">询价范围</div>
                                    <div class="value" id="detail-askSheetType">-</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 错误状态 -->
                    <div id="detail-error" style="display: none;">
                        <div class="alert alert-danger">
                            <h4>加载失败</h4>
                            <p class="mb-0" id="detail-error-message">无法加载需求单详情信息</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 产品列表卡片 -->
            <div class="card detail-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M7 8h10"></path>
                            <path d="M7 12h4l1 8h4l1 -8h4"></path>
                            <path d="M17 4v4a1 1 0 0 1 -1 1h-8a1 1 0 0 1 -1 -1v-4a3 3 0 0 1 6 0"></path>
                        </svg>
                        产品列表
                    </h3>
                    <div class="card-actions">
                        <span class="badge bg-secondary" id="product-count">0 个产品</span>
                    </div>
                </div>
                <div class="card-body position-relative" id="product-list-container">
                    <!-- 加载状态 -->
                    <div class="loading-overlay" id="product-loading">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                    
                    <!-- 产品列表内容 -->
                    <div id="product-content" style="display: none;">
                        <div class="table-responsive">
                            <table class="table table-vcenter product-table">
                                <thead>
                                    <tr>
                                        <th>产品名称</th>
                                        <th>品牌</th>
                                        <th>数量</th>
                                        <th>单位</th>
                                        <th>规格描述</th>
                                        <th>制造商</th>
                                    </tr>
                                </thead>
                                <tbody id="product-table-body">
                                    <!-- 产品数据将在这里动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- 空状态 -->
                    <div id="product-empty" style="display: none;">
                        <div class="empty">
                            <div class="empty-img">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-xl text-muted" width="128" height="128" viewBox="0 0 24 24" stroke-width="1" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M7 4v16l13 -8z"></path>
                                </svg>
                            </div>
                            <p class="empty-title">暂无产品数据</p>
                            <p class="empty-subtitle text-muted">该需求单暂时没有关联的产品信息</p>
                        </div>
                    </div>
                    
                    <!-- 错误状态 -->
                    <div id="product-error" style="display: none;">
                        <div class="alert alert-danger">
                            <h4>加载失败</h4>
                            <p class="mb-0" id="product-error-message">无法加载产品列表信息</p>
                        </div>
                    </div>
                </div>
            </div>
            </div> <!-- 关闭 main-content -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="/static/js/modules/xqd-detail.js?ver={{ app_version }}"></script>
{% endblock %}


