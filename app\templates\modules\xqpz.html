{% extends "base.html" %}

{% block title %}{{ app_name }} - 需求凭证查询{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="/static/css/modules/xqpz.css?ver={{ app_version }}">
{% endblock %}

{% block content %}

<div class="page-header d-print-none">
    <div class="container-xl">
        <div class="row g-2 align-items-center">
            <div class="col">
                <!-- 面包屑导航 -->
                <div class="page-pretitle">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="/">控制台</a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">需求凭证查询</li>
                        </ol>
                    </nav>
                </div>
                <h2 class="page-title">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler me-2 text-primary" width="32" height="32" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M14 3v4a1 1 0 0 0 1 1h4"></path>
                        <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"></path>
                        <path d="M9 9l1 0"></path>
                        <path d="M9 13l6 0"></path>
                        <path d="M9 17l6 0"></path>
                    </svg>
                    需求凭证查询
                </h2>
                <div class="text-muted mt-1">
                    采集和管理需求凭证数据，支持比价单号和商品SKU查询
                </div>
            </div>
            <div class="col-auto ms-auto d-print-none">
                <div class="btn-list">
                    <a href="/" class="btn btn-outline-secondary">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M5 12l14 0"></path>
                            <path d="M5 12l6 6"></path>
                            <path d="M5 12l6 -6"></path>
                        </svg>
                        返回控制台
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="page-body">
    <div class="container-xl">
        <!-- 登录提示 -->
        <div class="row" id="login-prompt">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <h3>需要登录</h3>
                        <p class="text-muted">请先登录到电子超市以使用需求凭证查询功能</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#login-modal">
                            立即登录
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- ELECTRON权限不足提示 -->
        <div class="row" id="electron-access-denied" style="display: none;">
            <div class="col-12">
                <div class="card border-warning">
                    <div class="card-header bg-warning-lt">
                        <h3 class="card-title text-warning">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M12 9v2m0 4v.01"></path>
                                <path d="M5 19h14a2 2 0 0 0 1.84 -2.75l-7.1 -12.25a2 2 0 0 0 -3.5 0l-7.1 12.25a2 2 0 0 0 1.75 2.75"></path>
                            </svg>
                            电子超市平台权限不足
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <h4>无法访问需求凭证查询</h4>
                                <p class="text-muted mb-3">
                                    您已成功登录到商城，但电子超市系统登录失败。
                                    需求凭证查询功能需要电子超市平台权限才能正常使用。
                                </p>
                                <div class="alert alert-info">
                                    <h5 class="alert-title">可能的原因：</h5>
                                    <ul class="mb-0">
                                        <li>您的账号没有电子超市平台访问权限</li>
                                        <li>电子超市系统暂时不可用</li>
                                        <li>网络连接问题导致电子超市登录失败</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-auto">
                                <div class="avatar avatar-lg bg-warning-lt text-warning">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="32" height="32" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"></path>
                                        <path d="M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6"></path>
                                        <path d="M3 3l18 18"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4">
                            <button class="btn btn-warning me-2" data-bs-toggle="modal" data-bs-target="#login-modal">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"></path>
                                    <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"></path>
                                </svg>
                                重新登录
                            </button>
                            <a href="/" class="btn btn-outline-secondary">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M5 12l14 0"></path>
                                    <path d="M5 12l6 6"></path>
                                    <path d="M5 12l6 -6"></path>
                                </svg>
                                返回控制台
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 模块内容 -->
        <div id="main-content" style="display: none;">
            <!-- 查询输入卡片 -->
            <div class="card mb-4 query-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"></path>
                            <path d="M21 21l-6 -6"></path>
                        </svg>
                        需求凭证查询条件
                    </h5>
                    <div class="card-actions">
                        <button class="btn btn-sm btn-outline-secondary" onclick="clearXqpzQueryCondition()">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M4 7l16 0"></path>
                                <path d="M10 11l0 6"></path>
                                <path d="M14 11l0 6"></path>
                                <path d="M5 7l1 -4l4 0l1 4"></path>
                                <path d="M9 7l6 0"></path>
                            </svg>
                            清空条件
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <form id="xqpz-form">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">比价单号</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" name="askSheetCode" id="askSheetCodeInput"
                                        placeholder="XJD202506050012，支持多个值（空格、换行、逗号分隔）">

                                    <!-- 多行编辑下拉菜单 -->
                                    <div class="dropdown">
                                        <button class="btn btn-outline-secondary btn-icon" type="button"
                                                data-bs-toggle="dropdown" aria-expanded="false"
                                                title="多行编辑比价单号">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                <path d="M4 8v-2a2 2 0 0 1 2 -2h2" />
                                                <path d="M4 16v2a2 2 0 0 0 2 2h2" />
                                                <path d="M16 4h2a2 2 0 0 1 2 2v2" />
                                                <path d="M16 20h2a2 2 0 0 0 2 -2v-2" />
                                            </svg>
                                        </button>

                                        <div class="dropdown-menu dropdown-menu-end" style="min-width: 350px; padding: 1rem;" onclick="event.stopPropagation()">
                                            <div class="mb-2">
                                                <label class="form-label">多行编辑 - 比价单号</label>
                                                <div class="form-text small mb-2">
                                                    每行输入一个比价单号，或使用空格、制表符分隔
                                                </div>
                                            </div>
                                            <textarea class="form-control" id="askSheetCodeTextarea" rows="6"
                                                      placeholder="XJD202506050012&#10;XJD202506050013&#10;XJD202506050014"></textarea>

                                            <!-- 实时统计显示 -->
                                            <div class="mt-2 mb-2">
                                                <div id="askSheetCodeCountInfo" class="form-text d-flex align-items-center gap-2 text-muted">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M9 11l3 3l8 -8"></path>
                                                        <path d="M20 12v6a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2h9"></path>
                                                    </svg>
                                                    <span id="askSheetCodeCountText">暂无输入</span>
                                                </div>
                                            </div>

                                            <div class="d-flex gap-2">
                                                <button type="button" class="btn btn-primary btn-sm" onclick="XqpzModule.applyMultilineEdit('askSheetCode')">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-sm" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                        <path d="M5 12l5 5l10 -10" />
                                                    </svg>
                                                    应用
                                                </button>
                                                <button type="button" class="btn btn-secondary btn-sm" onclick="XqpzModule.cancelMultilineEdit('askSheetCode')">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-sm" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                        <path d="M18 6l-12 12" />
                                                        <path d="M6 6l12 12" />
                                                    </svg>
                                                    取消
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-text">
                                    模糊匹配，支持批量查询（空格、换行符、制表符分隔）
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">商品SKU编码</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" name="sku" id="skuInput"
                                        placeholder="请输入商品SKU编码，支持多个值（空格、换行、逗号分隔）">

                                    <!-- 多行编辑下拉菜单 -->
                                    <div class="dropdown">
                                        <button class="btn btn-outline-secondary btn-icon" type="button"
                                                data-bs-toggle="dropdown" aria-expanded="false"
                                                title="多行编辑SKU编码">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                <path d="M4 8v-2a2 2 0 0 1 2 -2h2" />
                                                <path d="M4 16v2a2 2 0 0 0 2 2h2" />
                                                <path d="M16 4h2a2 2 0 0 1 2 2v2" />
                                                <path d="M16 20h2a2 2 0 0 0 2 -2v-2" />
                                            </svg>
                                        </button>

                                        <div class="dropdown-menu dropdown-menu-end" style="min-width: 350px; padding: 1rem;" onclick="event.stopPropagation()">
                                            <div class="mb-2">
                                                <label class="form-label">多行编辑 - 商品SKU编码</label>
                                                <div class="form-text small mb-2">
                                                    每行输入一个SKU编码，或使用空格、制表符分隔
                                                </div>
                                            </div>
                                            <textarea class="form-control" id="skuTextarea" rows="6"
                                                      placeholder="SKU001&#10;SKU002&#10;SKU003"></textarea>

                                            <!-- 实时统计显示 -->
                                            <div class="mt-2 mb-2">
                                                <div id="skuCountInfo" class="form-text d-flex align-items-center gap-2 text-muted">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                        <path d="M9 11l3 3l8 -8"></path>
                                                        <path d="M20 12v6a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2h9"></path>
                                                    </svg>
                                                    <span id="skuCountText">暂无输入</span>
                                                </div>
                                            </div>

                                            <div class="d-flex gap-2">
                                                <button type="button" class="btn btn-primary btn-sm" onclick="XqpzModule.applyMultilineEdit('sku')">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-sm" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                        <path d="M5 12l5 5l10 -10" />
                                                    </svg>
                                                    应用
                                                </button>
                                                <button type="button" class="btn btn-secondary btn-sm" onclick="XqpzModule.cancelMultilineEdit('sku')">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-sm" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                        <path d="M18 6l-12 12" />
                                                        <path d="M6 6l12 12" />
                                                    </svg>
                                                    取消
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-text">
                                    模糊匹配，支持批量查询（空格、换行符、制表符分隔）
                                </div>
                            </div>
                        </div>
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary" id="xqpz-submit-btn">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"></path>
                                    <path d="M21 21l-6 -6"></path>
                                </svg>
                                查询需求凭证
                            </button>

                        </div>
                    </form>
                </div>
            </div>

            <!-- 筛选器面板 -->
            <div id="data-filter-container"></div>

            <!-- 数据结果卡片 -->
            <div class="card results-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M3 3m0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z"></path>
                            <path d="M7 7h10"></path>
                            <path d="M7 12h10"></path>
                            <path d="M7 17h10"></path>
                        </svg>
                        需求凭证查询结果
                    </h5>
                    <div class="card-actions">
                        <span class="badge bg-secondary" id="xqpz-count">0 条记录</span>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div id="xqpz-results"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="/static/js/button-manager.js?ver={{ app_version }}"></script>
<script src="/static/js/pagination-manager.js?ver={{ app_version }}"></script>
<script src="/static/js/data-filter.js?ver={{ app_version }}"></script>
<script src="/static/js/modules/xqpz.js?ver={{ app_version }}"></script>
{% endblock %}
