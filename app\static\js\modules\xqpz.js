/**
 * 需求凭证查询模块 JavaScript
 * 使用模块化设计模式，提供完整的数据采集、筛选、分页、导出功能
 */

// 需求凭证模块对象
const XqpzModule = {
    // 模块初始化
    init() {
        console.log('需求凭证查询模块已加载');

        // 检查ELECTRON访问权限
        this.checkElectronAccess();

        // 绑定表单提交事件
        this.bindFormEvents();

        // 加载保存的查询条件
        this.loadQueryConditionsFromStorage();

        // 初始化数据筛选器
        this.initializeDataFilter();

        // 初始化多行编辑功能
        this.initializeMultilineEdit();
    },

    // 检查ELECTRON访问权限
    checkElectronAccess() {
        if (typeof authManager !== 'undefined' && authManager.userInfo) {
            const loginPrompt = document.getElementById('login-prompt');
            const electronAccessDenied = document.getElementById('electron-access-denied');
            const mainContent = document.getElementById('main-content');

            if (authManager.token && authManager.userInfo.local_username) {
                // 用户已登录，检查ELECTRON权限
                if (authManager.userInfo.electron_access) {
                    // 有ELECTRON权限，显示主要内容
                    if (loginPrompt) loginPrompt.style.display = 'none';
                    if (electronAccessDenied) electronAccessDenied.style.display = 'none';
                    if (mainContent) mainContent.style.display = 'block';
                } else {
                    // 没有ELECTRON权限，显示权限不足提示
                    if (loginPrompt) loginPrompt.style.display = 'none';
                    if (electronAccessDenied) electronAccessDenied.style.display = 'block';
                    if (mainContent) mainContent.style.display = 'none';
                }
            } else {
                // 用户未登录，显示登录提示
                if (loginPrompt) loginPrompt.style.display = 'block';
                if (electronAccessDenied) electronAccessDenied.style.display = 'none';
                if (mainContent) mainContent.style.display = 'none';
            }
        }
    },

    // 绑定表单事件
    bindFormEvents() {
        const form = document.getElementById('xqpz-form');
        if (form) {
            form.addEventListener('submit', this.handleFormSubmit.bind(this));
        }
    },

    // 初始化数据筛选器
    initializeDataFilter() {
        if (!dataFilter) {
            dataFilter = new DataFilter('data-filter-container', {
                storageKey: 'xqpz-filter',
                useTabSeparatedCopy: true, // 使用制表符分隔的复制格式，适合Excel粘贴
                onFilterChange: (filteredData) => {
                    this.displayFilteredResults(filteredData);
                }
            });
        }
    },

    // 初始化多行编辑功能
    initializeMultilineEdit() {
        // 绑定输入框和文本域的同步事件
        this.bindMultilineSync('askSheetCode');
        this.bindMultilineSync('sku');

        // 绑定下拉菜单显示事件，自动聚焦到文本域
        this.bindDropdownFocus();

        // 初始化实时统计功能
        this.initializeRealtimeCount();
    },

    // 绑定单行输入框和多行文本域的双向同步
    bindMultilineSync(fieldType) {
        const inputId = fieldType === 'askSheetCode' ? 'askSheetCodeInput' : 'skuInput';
        const textareaId = fieldType === 'askSheetCode' ? 'askSheetCodeTextarea' : 'skuTextarea';

        const input = document.getElementById(inputId);
        const textarea = document.getElementById(textareaId);

        if (!input || !textarea) {
            console.warn(`多行编辑元素未找到: ${fieldType}`);
            return;
        }

        // 单行输入框变化时，同步到多行文本域（空格转换为换行）
        input.addEventListener('input', () => {
            // 使用正则表达式替换空格、制表符为换行符
            textarea.value = input.value.replace(/[\s\t]+/g, '\n').replace(/\n+/g, '\n');
        });

        // 多行文本域变化时，同步到单行输入框（换行转换为空格）
        textarea.addEventListener('input', () => {
            // 使用正则表达式替换换行符为空格，并清理多余空格
            input.value = textarea.value.replace(/\r\n|\n/g, ' ').replace(/\s+/g, ' ').trim();
        });
    },

    // 绑定下拉菜单显示时的自动聚焦和数据同步
    bindDropdownFocus() {
        // 比价单号下拉菜单
        const askSheetDropdown = document.querySelector('#askSheetCodeInput').closest('.input-group').querySelector('.dropdown');
        if (askSheetDropdown) {
            askSheetDropdown.addEventListener('shown.bs.dropdown', () => {
                const input = document.getElementById('askSheetCodeInput');
                const textarea = document.getElementById('askSheetCodeTextarea');
                if (input && textarea) {
                    // 将输入框的内容同步到多行编辑器（空格转换为换行）
                    if (input.value.trim()) {
                        textarea.value = input.value.replace(/[\s\t]+/g, '\n').replace(/\n+/g, '\n');
                    } else {
                        textarea.value = '';
                    }
                    // 更新统计信息
                    this.updateRealtimeCount('askSheetCode', textarea.value);
                    // 自动聚焦到文本域
                    setTimeout(() => textarea.focus(), 100);
                }
            });
        }

        // SKU下拉菜单
        const skuDropdown = document.querySelector('#skuInput').closest('.input-group').querySelector('.dropdown');
        if (skuDropdown) {
            skuDropdown.addEventListener('shown.bs.dropdown', () => {
                const input = document.getElementById('skuInput');
                const textarea = document.getElementById('skuTextarea');
                if (input && textarea) {
                    // 将输入框的内容同步到多行编辑器（空格转换为换行）
                    if (input.value.trim()) {
                        textarea.value = input.value.replace(/[\s\t]+/g, '\n').replace(/\n+/g, '\n');
                    } else {
                        textarea.value = '';
                    }
                    // 更新统计信息
                    this.updateRealtimeCount('sku', textarea.value);
                    // 自动聚焦到文本域
                    setTimeout(() => textarea.focus(), 100);
                }
            });
        }
    },

    // 应用多行编辑内容
    applyMultilineEdit(fieldType) {
        const inputId = fieldType === 'askSheetCode' ? 'askSheetCodeInput' : 'skuInput';
        const textareaId = fieldType === 'askSheetCode' ? 'askSheetCodeTextarea' : 'skuTextarea';

        const input = document.getElementById(inputId);
        const textarea = document.getElementById(textareaId);

        if (!input || !textarea) {
            console.warn(`多行编辑元素未找到: ${fieldType}`);
            return;
        }

        // 将多行文本域的内容转换为单行格式并应用到输入框
        const cleanedValue = textarea.value
            .split(/\r\n|\n/)  // 按换行符分割
            .map(line => line.trim())  // 去除每行的前后空白
            .filter(line => line.length > 0)  // 过滤空行
            .join(' ');  // 用空格连接

        input.value = cleanedValue;

        // 关闭下拉菜单
        const dropdown = input.closest('.input-group').querySelector('.dropdown');
        if (dropdown) {
            const bsDropdown = bootstrap.Dropdown.getInstance(dropdown.querySelector('[data-bs-toggle="dropdown"]'));
            if (bsDropdown) {
                bsDropdown.hide();
            }
        }
    },

    // 取消多行编辑
    cancelMultilineEdit(fieldType) {
        const inputId = fieldType === 'askSheetCode' ? 'askSheetCodeInput' : 'skuInput';
        const textareaId = fieldType === 'askSheetCode' ? 'askSheetCodeTextarea' : 'skuTextarea';

        const input = document.getElementById(inputId);
        const textarea = document.getElementById(textareaId);

        if (!input || !textarea) {
            console.warn(`多行编辑元素未找到: ${fieldType}`);
            return;
        }

        // 恢复文本域内容为输入框的内容
        textarea.value = input.value.replace(/[\s\t]+/g, '\n').replace(/\n+/g, '\n');

        // 关闭下拉菜单
        const dropdown = input.closest('.input-group').querySelector('.dropdown');
        if (dropdown) {
            const bsDropdown = bootstrap.Dropdown.getInstance(dropdown.querySelector('[data-bs-toggle="dropdown"]'));
            if (bsDropdown) {
                bsDropdown.hide();
            }
        }
    },

    // 初始化实时统计功能
    initializeRealtimeCount() {
        // 为比价单号多行编辑器绑定实时统计
        const askSheetCodeTextarea = document.getElementById('askSheetCodeTextarea');
        if (askSheetCodeTextarea) {
            askSheetCodeTextarea.addEventListener('input', () => {
                this.updateRealtimeCount('askSheetCode', askSheetCodeTextarea.value);
            });
        }

        // 为SKU多行编辑器绑定实时统计
        const skuTextarea = document.getElementById('skuTextarea');
        if (skuTextarea) {
            skuTextarea.addEventListener('input', () => {
                this.updateRealtimeCount('sku', skuTextarea.value);
            });
        }
    },

    // 更新实时统计显示
    updateRealtimeCount(fieldType, content) {
        const countInfoId = fieldType === 'askSheetCode' ? 'askSheetCodeCountInfo' : 'skuCountInfo';
        const countTextId = fieldType === 'askSheetCode' ? 'askSheetCodeCountText' : 'skuCountText';
        const fieldName = fieldType === 'askSheetCode' ? '比价单号' : 'SKU编码';

        const countInfo = document.getElementById(countInfoId);
        const countText = document.getElementById(countTextId);

        if (!countInfo || !countText) {
            return;
        }

        // 统计有效项目数量
        const count = this.countValidItems(content);

        if (count === 0) {
            // 没有输入
            countText.textContent = '暂无输入';
            countInfo.className = 'form-text d-flex align-items-center gap-2 text-muted';
        } else {
            // 有输入
            countText.textContent = `已输入 ${count} 个${fieldName}`;
            countInfo.className = 'form-text d-flex align-items-center gap-2 text-success';
        }
    },

    // 统计有效项目数量
    countValidItems(content) {
        if (!content || !content.trim()) {
            return 0;
        }

        // 按换行符分割并过滤空行
        const lines = content.split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0);

        return lines.length;
    },

    // 解析输入值，支持多种分隔符
    parseInputValues(inputStr) {
        if (!inputStr || !inputStr.trim()) {
            return [];
        }

        // 支持的分隔符：空格、换行符、制表符
        const values = inputStr.trim().split(/[\s\n\t]+/);

        // 过滤空值并去除前后空白
        return values.filter(value => value.trim()).map(value => value.trim());
    },

    // 检测是否为批量查询
    isBatchQuery(askSheetCode, sku) {
        const askSheetCodes = this.parseInputValues(askSheetCode);
        const skus = this.parseInputValues(sku);

        return (
            (askSheetCodes.length > 1) ||
            (skus.length > 1) ||
            (askSheetCodes.length > 0 && skus.length > 0)  // 同时有比价单号和SKU也算批量
        );
    },

    // 处理表单提交
    async handleFormSubmit(event) {
        event.preventDefault();

        // 检查ELECTRON权限
        if (!this.checkElectronAccessForQuery()) {
            return;
        }

        const form = event.target;
        const formData = new FormData(form);
        const askSheetCode = formData.get('askSheetCode')?.trim() || '';
        const sku = formData.get('sku')?.trim() || '';

        // 验证输入
        if (!askSheetCode && !sku) {
            authManager.showNotification('请输入比价单号或商品SKU编码', 'warning');
            return;
        }

        // 检测查询模式
        const isBatch = this.isBatchQuery(askSheetCode, sku);
        const askSheetCodes = this.parseInputValues(askSheetCode);
        const skus = this.parseInputValues(sku);
        const totalItems = askSheetCodes.length + skus.length;

        // 显示查询模式信息
        if (isBatch) {
            console.log(`检测到批量查询模式 - 比价单号: ${askSheetCodes.length}, SKU: ${skus.length}, 总计: ${totalItems} 个项目`);
        } else {
            console.log('使用单个查询模式');
        }

        // 保存查询条件到本地存储
        this.saveQueryConditionsToStorage({
            askSheetCode: askSheetCode,
            sku: sku
        });

        // 使用按钮管理器包装查询操作
        const queryButton = document.getElementById('xqpz-submit-btn');

        try {
            const loadingText = isBatch ?
                `批量查询需求凭证中... (${totalItems}个项目)` :
                '查询需求凭证中...';

            await buttonManager.wrapAsync(queryButton, async () => {
                // 构建查询参数
                const queryParams = {
                    askSheetCode: askSheetCode || null,
                    sku: sku || null
                };

                // 执行查询
                await this.executeQuery(queryParams, isBatch);
            }, loadingText);

        } catch (error) {
            console.error('需求凭证查询错误:', error);
            authManager.showNotification('查询过程中发生错误', 'error');
        }
    },

    // 检查ELECTRON访问权限（用于查询）
    checkElectronAccessForQuery() {
        if (typeof authManager === 'undefined' || !authManager.userInfo) {
            authManager.showNotification('请先登录', 'warning');
            return false;
        }

        if (!authManager.userInfo.electron_access) {
            authManager.showNotification('需要电子超市平台权限才能进行需求凭证查询', 'error');
            return false;
        }

        return true;
    },

    // 执行查询
    async executeQuery(queryParams, isBatch = false) {
        try {
            const response = await fetch('/api/v1/data/xqpz', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authManager.token}`
                },
                body: JSON.stringify(queryParams)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.success) {
                this.setupFilter(result.data);

                // 根据查询模式显示不同的成功消息
                let successMessage;
                if (isBatch && result.batch_summary) {
                    const summary = result.batch_summary;
                    if (summary.error_count > 0) {
                        successMessage = `批量查询完成，共查询 ${summary.total_queries} 个条件，成功 ${summary.success_count} 个，失败 ${summary.error_count} 个，找到 ${result.data.length} 条需求凭证记录`;
                    } else {
                        successMessage = `批量查询成功，共查询 ${summary.total_queries} 个条件，全部成功，找到 ${result.data.length} 条需求凭证记录`;
                    }
                } else {
                    successMessage = `查询成功，找到 ${result.data.length} 条需求凭证记录`;
                }

                authManager.showNotification(successMessage, 'success');

                // 记录查询统计
                if (typeof dashboardStats !== 'undefined') {
                    dashboardStats.recordQuery('xqpz', true);
                }
            } else {
                // 检查是否为权限相关错误
                if (result.permission_error) {
                    authManager.showNotification('权限已失效，正在重新登录...', 'warning');
                    authManager.handleTokenExpiry();
                    return;
                }

                authManager.showNotification(result.message || '查询失败', 'error');
                this.setupFilter([]);

                // 记录查询统计（失败）
                if (typeof dashboardStats !== 'undefined') {
                    dashboardStats.recordQuery('xqpz', false);
                }
            }

        } catch (error) {
            console.error('需求凭证查询API错误:', error);
            authManager.showNotification('查询请求失败，请检查网络连接', 'error');
            this.setupFilter([]);
        }
    },

    // 设置筛选器
    setupFilter(data) {
        if (!dataFilter) {
            this.initializeDataFilter();
        }

        // 初始化分页管理器
        if (!window.paginationManager_xqpz) {
            window.paginationManager_xqpz = new PaginationManager({
                moduleId: 'xqpz',
                pageSize: 20,
                renderCallback: () => {
                    const currentPageData = window.paginationManager_xqpz.getCurrentPageData();
                    this.displayFilteredResults(currentPageData, true);
                }
            });
        }

        // 定义列结构
        const columns = [
            { key: 'sku', label: 'SKU编码' },
            { key: 'prodName', label: '商品名称' },
            { key: 'answerPrice', label: '中标价格' },
            { key: 'unit', label: '计量单位' },
            { key: 'answerVoucherStatus', label: '状态' },
            { key: 'askSheetUser', label: '客户名称' },
            { key: 'purchaserName', label: '客户单位' },
            { key: 'voucherCode', label: '凭证编码' }
        ];

        // 设置数据和列
        dataFilter.setData(data, columns);

        // 设置分页数据
        window.paginationManager_xqpz.setData(data, data);

        // 显示第一页数据
        const firstPageData = window.paginationManager_xqpz.getCurrentPageData();
        this.displayFilteredResults(firstPageData, true);
    },

    // 显示筛选后的结果
    displayFilteredResults(data, isPaginated = false) {
        const resultsContainer = document.getElementById('xqpz-results');
        const countElement = document.getElementById('xqpz-count');

        if (!resultsContainer || !countElement) return;

        // 生成表格内容
        let tableHTML = '';

        if (isPaginated && window.paginationManager_xqpz) {
            // 分页模式
            const paginationInfo = window.paginationManager_xqpz.getInfo();
            countElement.textContent = `第${paginationInfo.startItem}-${paginationInfo.endItem}条，共${paginationInfo.totalItems}条记录`;

            // 生成表格
            tableHTML = dataFilter.generateSelectableTable(data, this.renderCustomTable.bind(this));

            // 添加分页控件
            const paginationHTML = window.paginationManager_xqpz.generatePaginationHTML();
            tableHTML += paginationHTML;
        } else {
            // 非分页模式
            countElement.textContent = `${data.length} 条记录`;
            tableHTML = dataFilter.generateSelectableTable(data, this.renderCustomTable.bind(this));
        }

        resultsContainer.innerHTML = tableHTML;
    },

    // 自定义表格渲染器
    renderCustomTable(data, filterInstance) {
        if (!data || data.length === 0) {
            return `
                <div class="empty">
                    <div class="empty-img">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDEyOCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik02NCA5NkM4MC41Njg1IDk2IDk0IDgyLjU2ODUgOTQgNjZDOTQgNDkuNDMxNSA4MC41Njg1IDM2IDY0IDM2QzQ3LjQzMTUgMzYgMzQgNDkuNDMxNSAzNCA2NkMzNCA4Mi41Njg1IDQ3LjQzMTUgOTYgNjQgOTZaIiBzdHJva2U9IiNEQURERTIiIHN0cm9rZS13aWR0aD0iMiIvPgo8cGF0aCBkPSJNNTcuMzMzMyA2NkM1Ny4zMzMzIDYyLjMxODEgNjAuMzE4MSA1OS4zMzMzIDY0IDU5LjMzMzNDNjcuNjgxOSA1OS4zMzMzIDcwLjY2NjcgNjIuMzE4MSA3MC42NjY3IDY2QzcwLjY2NjcgNjkuNjgxOSA2Ny42ODE5IDcyLjY2NjcgNjQgNzIuNjY2N0M2MC4zMTgxIDcyLjY2NjcgNTcuMzMzMyA2OS42ODE5IDU3LjMzMzMgNjZaIiBmaWxsPSIjREFEREUyIi8+Cjwvc3ZnPgo=" alt="暂无数据">
                    </div>
                    <p class="empty-title">暂无需求凭证数据</p>
                    <p class="empty-subtitle text-muted">请调整查询条件或筛选条件后重试</p>
                </div>
            `;
        }

        // 生成带选择功能的表格HTML
        let tableHTML = `
            <div class="table-responsive">
                <table class="table table-vcenter">
                    <thead>
                        <tr>
                            <th>SKU编码</th>
                            <th>商品名称</th>
                            <th>中标价格</th>
                            <th>计量单位</th>
                            <th>状态</th>
                            <th>客户名称</th>
                            <th>客户单位</th>
                            <th>凭证编码</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        data.forEach((item, index) => {
            const isSelected = filterInstance.selectedRows.has(index);

            tableHTML += `
                <tr data-row-index="${index}" class="${isSelected ? 'table-active' : ''}"
                    onclick="XqpzModule.handleRowClick(${index}, event)"
                    style="cursor: pointer;">
                    <td>
                        <code>${this.escapeHtml(item.sku || '')}</code>
                    </td>
                    <td>
                        <div class="table-cell-ellipsis product-name" title="${this.escapeHtml(item.prodName || '')}">
                            ${this.escapeHtml(item.prodName || '')}
                        </div>
                    </td>
                    <td>
                        <span class="text-success fw-bold">¥${this.formatPrice(item.answerPrice)}</span>
                    </td>
                    <td>
                        ${this.escapeHtml(item.unit || '')}
                    </td>
                    <td>
                        ${this.formatVoucherStatus(item.answerVoucherStatus)}
                    </td>
                    <td>
                        ${this.escapeHtml(item.askSheetUser || '')}
                    </td>
                    <td>
                        <div class="table-cell-ellipsis customer-unit" title="${this.escapeHtml(item.purchaserName || '')}">
                            ${this.escapeHtml(item.purchaserName || '')}
                        </div>
                    </td>
                    <td>
                        <code>${this.escapeHtml(item.voucherCode || '')}</code>
                    </td>
                    <td>
                        ${this.generateActionButtons(item)}
                    </td>
                </tr>
            `;
        });

        tableHTML += `
                    </tbody>
                </table>
            </div>
        `;

        // 更新选择状态UI
        setTimeout(() => {
            filterInstance.updateAllRowSelectionUI();
        }, 0);

        return tableHTML;
    },

    // 格式化价格
    formatPrice(price) {
        if (price === null || price === undefined) return '0.00';
        return parseFloat(price).toFixed(2);
    },

    // 格式化凭证状态
    formatVoucherStatus(status) {
        const statusMap = {
            'QUOTED': '<span class="badge bg-primary">已报价</span>',
            'BIDDED': '<span class="badge bg-success">已中标</span>',
            'NOTBID': '<span class="badge bg-danger">未中标</span>',
            'FLOWBID': '<span class="badge bg-warning">已流标</span>'
        };
        return statusMap[status] || '<span class="badge bg-secondary">未知</span>';
    },

    // 生成操作按钮
    generateActionButtons(item) {
        const voucherCode = item.voucherCode || '';
        const sku = item.sku || '';
        const answerVoucherStatus = item.answerVoucherStatus || '';

        // 只有状态为"BIDDED"（已中标）的凭证才显示操作按钮
        if (answerVoucherStatus === 'BIDDED') {
            return `
                <div class="btn-list flex-nowrap">
                    <button class="btn btn-sm btn-outline-success"
                            onclick="event.stopPropagation(); XqpzModule.viewProductStatus('${sku}', '${voucherCode}')"
                            title="查看商品状态">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M7 4v16l13 0v-16"></path>
                            <path d="M7 4l4 0l0 4"></path>
                            <path d="M11 4l4 0"></path>
                            <path d="M15 4l0 4l-4 0"></path>
                            <path d="M7 8l13 0"></path>
                            <path d="M7 12l13 0"></path>
                            <path d="M7 16l13 0"></path>
                        </svg>
                    </button>
                </div>
            `;
        } else {
            // 其他状态不显示操作按钮，显示"-"
            return '<span class="text-muted">-</span>';
        }
    },

    // 查看商品状态
    async viewProductStatus(sku, voucherCode) {
        // 检查ELECTRON权限
        if (!this.checkElectronAccessForQuery()) {
            return;
        }

        if (!sku) {
            return;
        }

        // 找到对应的按钮并显示加载状态
        const button = document.querySelector(`button[onclick*="XqpzModule.viewProductStatus('${sku}',"]`);
        if (button) {
            this.showProductStatusLoadingState(button);
        }

        try {
            // 执行商品状态查询
            const result = await this.queryProductStatusBySku(sku);

            if (result.success) {
                // 显示商品状态信息
                this.showProductStatusResult(sku, result.data, button);
            } else {
                // 检查是否为权限相关错误
                if (result.permission_error) {
                    authManager.showNotification('权限已失效，正在重新登录...', 'warning');
                    authManager.handleTokenExpiry();
                    return;
                }

                // 查询失败时，直接替换为"未上架"状态
                this.replaceButtonWithBadge(button, '未上架', 'secondary', '查询失败，商品可能未上架');
            }

        } catch (error) {
            console.error('查看商品状态错误:', error);
            // 网络异常时，直接替换为"网络异常"状态
            this.replaceButtonWithBadge(button, '网络异常', 'secondary', '网络连接异常，无法查询商品状态');
        }
    },

    // 查询商品状态API
    async queryProductStatusBySku(sku) {
        try {
            const response = await fetch('/api/v1/data/product/query', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authManager.token}`
                },
                body: JSON.stringify({
                    sku: sku
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            return result;

        } catch (error) {
            console.error('商品状态查询API错误:', error);
            return {
                success: false,
                message: '查询请求失败，请检查网络连接'
            };
        }
    },

    // 显示商品状态加载状态
    showProductStatusLoadingState(button) {
        if (button) {
            button.disabled = true;
            button.innerHTML = `
                <div class="spinner-border spinner-border-sm" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
            `;
        }
    },

    // 统一的按钮替换为徽章函数
    replaceButtonWithBadge(button, statusText, statusColor, tooltipText) {
        if (button) {
            // 创建状态徽章元素
            const statusBadge = document.createElement('span');
            statusBadge.className = `badge bg-${statusColor}`;
            statusBadge.textContent = statusText;
            if (tooltipText) {
                statusBadge.title = tooltipText;
            }

            // 替换按钮为状态徽章
            button.parentElement.replaceChild(statusBadge, button);
        }
    },

    // 显示商品状态查询结果
    showProductStatusResult(sku, productData, button) {
        if (!productData || productData.length === 0) {
            // 未找到商品信息时，直接替换为"未上架"状态
            this.replaceButtonWithBadge(button, '未上架', 'secondary', `SKU ${sku} 未找到商品信息`);
            return;
        }

        const product = productData[0];
        const shelfStatus = product.shelfStatus || '';

        // 状态值映射
        const statusMap = {
            'UP_SHELF': '已上架',
            'DOWN_SHELF': '已下架'
        };

        const statusText = statusMap[shelfStatus] || shelfStatus || '未知状态';
        const statusColor = shelfStatus === 'UP_SHELF' ? 'success' :
                           shelfStatus === 'DOWN_SHELF' ? 'danger' : 'secondary';

        // 构建详细的提示信息
        const tooltipText = `商品: ${product.name || '未知'}\nSKU: ${product.sku || sku}\n状态: ${statusText}${product.price ? `\n售价: ¥${product.price}` : ''}${product.updateTime ? `\n更新时间: ${product.updateTime}` : ''}`;

        // 直接在按钮位置替换为状态徽章
        this.replaceButtonWithBadge(button, statusText, statusColor, tooltipText);
    },

    // 保存查询条件到本地存储
    saveQueryConditionsToStorage(conditions) {
        try {
            // 只保存比价单号和SKU编码字段到本地存储
            const xqpzConditions = {
                askSheetCode: conditions.askSheetCode || '',
                sku: conditions.sku || ''
            };
            localStorage.setItem('xqpz-query-conditions', JSON.stringify(xqpzConditions));
        } catch (error) {
            console.warn('无法保存需求凭证查询条件到本地存储:', error);
        }
    },

    // 从本地存储加载查询条件
    loadQueryConditionsFromStorage() {
        try {
            const stored = localStorage.getItem('xqpz-query-conditions');
            if (stored) {
                const conditions = JSON.parse(stored);
                const form = document.getElementById('xqpz-form');
                if (form) {
                    // 恢复比价单号字段值
                    if (conditions.askSheetCode) {
                        const askSheetCodeField = form.querySelector('input[name="askSheetCode"]');
                        if (askSheetCodeField) askSheetCodeField.value = conditions.askSheetCode;
                    }

                    // 恢复SKU编码字段值
                    if (conditions.sku) {
                        const skuField = form.querySelector('input[name="sku"]');
                        if (skuField) skuField.value = conditions.sku;
                    }
                }
            }
        } catch (error) {
            console.warn('无法从本地存储加载需求凭证查询条件:', error);
        }
    },

    // 处理行点击事件（智能选择）
    handleRowClick(rowIndex, event) {
        // 检查是否在文本选择模式
        const selection = window.getSelection();
        if (selection.toString().length > 0) {
            // 如果有文本被选中，不触发行选择
            return;
        }

        // 检查点击的目标元素
        const target = event.target;

        // 如果点击的是可选择的文本内容，允许文本选择
        if (target.tagName === 'CODE' ||
            target.tagName === 'SPAN' ||
            target.closest('.table-cell-ellipsis') ||
            target.closest('.badge')) {
            // 延迟检查是否真的在选择文本
            setTimeout(() => {
                const newSelection = window.getSelection();
                if (newSelection.toString().length === 0) {
                    // 没有选择文本，触发行选择
                    dataFilter.toggleRowSelection(rowIndex, event);
                }
            }, 10);
        } else {
            // 点击其他区域，直接触发行选择
            dataFilter.toggleRowSelection(rowIndex, event);
        }
    },

    // HTML转义函数
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
};

// 全局函数，保持向后兼容性
function clearXqpzQueryCondition() {
    const form = document.getElementById('xqpz-form');
    if (form) {
        form.reset();
        // 清空本地存储
        try {
            localStorage.removeItem('xqpz-query-conditions');
        } catch (error) {
            console.warn('无法清空需求凭证本地存储:', error);
        }
        authManager.showNotification('查询条件已清空', 'info');
    }
}

// 重写authManager的显示主要内容方法，添加ELECTRON权限检查
if (typeof authManager !== 'undefined') {
    const originalShowMainContent = authManager.showMainContent;
    authManager.showMainContent = function() {
        originalShowMainContent.call(this);
        // 在需求凭证模块中，需要额外检查ELECTRON权限
        setTimeout(() => XqpzModule.checkElectronAccess(), 100);
    };

    const originalShowLoginPrompt = authManager.showLoginPrompt;
    authManager.showLoginPrompt = function() {
        originalShowLoginPrompt.call(this);
        // 在需求凭证模块中，隐藏ELECTRON权限提示
        const electronAccessDenied = document.getElementById('electron-access-denied');
        if (electronAccessDenied) electronAccessDenied.style.display = 'none';
    };
}

// 模块初始化
document.addEventListener('DOMContentLoaded', function() {
    XqpzModule.init();
});
