"""
异步认证服务
基于现有的login_hf.py和login_hk.py实现异步登录
"""
import asyncio
import base64
from typing import Dict, Any, Optional, Tuple
from urllib.parse import urlparse, parse_qs
import aiohttp
from bs4 import BeautifulSoup

from app.core.config import get_settings
from app.core.logging import get_logger

settings = get_settings()
logger = get_logger(__name__)


class AsyncAuthService:
    """异步认证服务"""
    
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        }

    def _get_headers_with_ip(self, client_ip: str = None) -> Dict[str, str]:
        """
        获取包含客户端IP的请求头

        Args:
            client_ip: 客户端IP地址

        Returns:
            包含IP信息的请求头字典
        """
        headers = self.headers.copy()
        if client_ip and client_ip != "unknown":
            headers.update({
                'X-Forwarded-For': client_ip,
                'X-Real-IP': client_ip
            })
            logger.debug(f"AVIATION平台设置IP请求头: X-Forwarded-For={client_ip}, X-Real-IP={client_ip}")
        return headers
    
    async def login_aviation_platform(self, username: str, password: str, client_ip: str = None) -> Dict[str, Any]:
        """
        航空平台异步登录
        基于login_hk.py的逻辑实现
        """
        logger.info(f"开始航空平台登录 - 用户: {username}")

        session = None
        try:
            # 获取包含IP的请求头
            request_headers = self._get_headers_with_ip(client_ip)
            session = aiohttp.ClientSession()

            # 1. 获取加密密码
            logger.debug(f"[航空平台-步骤1] 开始获取加密密码 - 用户: {username}")
            try:
                encrypted_password = await self._get_encrypted_password(
                    session, username, password, "ProjectNewHK", request_headers
                )

                if not encrypted_password:
                    logger.error(f"[航空平台-步骤1] 密码加密失败 - 用户: {username}")
                    return {
                        'success': False,
                        'message': '密码加密失败',
                        'platform': 'aviation'
                    }
                logger.debug(f"[航空平台-步骤1] 密码加密成功 - 用户: {username}")

            except Exception as e:
                logger.exception(f"[航空平台-步骤1] 密码加密过程异常 - 用户: {username}")
                return {
                    'success': False,
                    'message': f'密码加密过程中发生错误: {str(e)}',
                    'platform': 'aviation'
                }

            # 2. 请求登录页面获取表单参数
            logger.debug(f"[航空平台-步骤2] 开始请求登录页面 - 用户: {username}")
            try:
                login_url = f"{settings.AVIATION_SSO_URL}?service={settings.AVIATION_SERVICE_URL}"
                logger.debug(f"[航空平台-步骤2] 登录URL: {login_url}")

                async with session.get(login_url, headers=request_headers) as response:
                    if response.status != 200:
                        logger.error(f"[航空平台-步骤2] 登录页面请求失败 - 状态码: {response.status}, 用户: {username}")
                        return {
                            'success': False,
                            'message': f'登录页面请求失败: HTTP {response.status}',
                            'platform': 'aviation'
                        }

                    html_content = await response.text()
                    soup = BeautifulSoup(html_content, 'html.parser')
                    logger.debug(f"[航空平台-步骤2] 登录页面请求成功 - 用户: {username}")

            except Exception as e:
                logger.exception(f"[航空平台-步骤2] 登录页面请求异常 - 用户: {username}")
                return {
                    'success': False,
                    'message': f'登录页面请求过程中发生错误: {str(e)}',
                    'platform': 'aviation'
                }

            # 3. 提取表单参数
            logger.debug(f"[航空平台-步骤3] 开始提取表单参数 - 用户: {username}")
            try:
                lt_input = soup.find('input', {'name': 'lt'})
                execution_input = soup.find('input', {'name': 'execution'})

                if not lt_input or not execution_input:
                    logger.error(f"[航空平台-步骤3] 表单参数提取失败 - lt_input: {lt_input is not None}, execution_input: {execution_input is not None}, 用户: {username}")
                    return {
                        'success': False,
                        'message': '无法获取登录表单参数',
                        'platform': 'aviation'
                    }

                lt_value = lt_input['value']
                execution_value = execution_input['value']
                logger.debug(f"[航空平台-步骤3] 表单参数提取成功 - lt: {lt_value}, execution: {execution_value}, 用户: {username}")

            except Exception as e:
                logger.exception(f"[航空平台-步骤3] 表单参数提取异常 - 用户: {username}")
                return {
                    'success': False,
                    'message': f'表单参数提取过程中发生错误: {str(e)}',
                    'platform': 'aviation'
                }

            # 4. 提交登录表单
            logger.debug(f"[航空平台-步骤4] 开始提交登录表单 - 用户: {username}")
            try:
                login_data = {
                    'username': username,
                    'password': encrypted_password,
                    'captcha': '1234',  # 固定验证码
                    'lt': lt_value,
                    'execution': execution_value,
                    '_eventId': 'submit',
                    'isajax': 'true',
                    'isframe': 'true',
                    'sorceService': settings.AVIATION_SERVICE_URL
                }

                logger.debug(f"[航空平台-步骤4] 登录表单数据准备完成 - 用户: {username}")

                # 设置重定向参数以处理SSO登录流程中的多次重定向
                # 航空平台SSO可能需要多次重定向，设置合理的重定向次数限制
                async with session.post(
                    login_url,
                    headers=request_headers,
                    data=login_data,
                    max_redirects=15,  # 允许最多15次重定向，足以处理复杂的SSO流程
                    allow_redirects=True  # 明确启用自动重定向
                ) as response:
                    logger.debug(f"[航空平台-步骤4] 登录表单提交完成 - 状态码: {response.status}, 最终URL: {response.url}, 用户: {username}")

                    # 记录重定向历史（如果有的话）
                    if hasattr(response, 'history') and response.history:
                        redirect_count = len(response.history)
                        logger.debug(f"[航空平台-步骤4] 检测到重定向 - 重定向次数: {redirect_count}, 用户: {username}")
                        for i, redirect_response in enumerate(response.history):
                            logger.debug(f"[航空平台-步骤4] 重定向{i+1}: {redirect_response.status} -> {redirect_response.url}")
                    else:
                        logger.debug(f"[航空平台-步骤4] 无重定向发生 - 用户: {username}")

                    if response.status != 200:
                        logger.error(f"[航空平台-步骤4] 登录表单提交失败 - 状态码: {response.status}, 最终URL: {response.url}, 用户: {username}")
                        return {
                            'success': False,
                            'message': f'登录表单提交失败: HTTP {response.status}',
                            'platform': 'aviation'
                        }

                    result_html = await response.text()
                    result_soup = BeautifulSoup(result_html, 'html.parser')
                    logger.debug(f"[航空平台-步骤4] 登录表单提交成功 - 响应内容长度: {len(result_html)}, 用户: {username}")

            except aiohttp.TooManyRedirects as e:
                logger.exception(f"[航空平台-步骤4] 重定向次数过多异常 - URL: {e.request_info.url if hasattr(e, 'request_info') else 'Unknown'}, 用户: {username}")
                return {
                    'success': False,
                    'message': f'登录过程中重定向次数过多，请检查SSO配置: {str(e)}',
                    'platform': 'aviation'
                }
            except aiohttp.ClientError as e:
                logger.exception(f"[航空平台-步骤4] HTTP客户端异常 - 用户: {username}")
                return {
                    'success': False,
                    'message': f'网络请求异常: {str(e)}',
                    'platform': 'aviation'
                }
            except Exception as e:
                logger.exception(f"[航空平台-步骤4] 登录表单提交异常 - 用户: {username}")
                return {
                    'success': False,
                    'message': f'登录表单提交过程中发生错误: {str(e)}',
                    'platform': 'aviation'
                }

            # 5. 检查登录结果
            logger.debug(f"[航空平台-步骤5] 开始检查登录结果 - 用户: {username}")
            try:
                user_element = result_soup.find('a', class_='user-name')
                error_element = result_soup.find('div', class_='report-errors')
                error_hint = result_soup.find('div', {'id': 'msg', 'class': 'error-hint'})

                if error_hint:
                    error_text = error_hint.text.strip()
                    logger.warning(f"[航空平台-步骤5] 登录失败 - 错误信息: {error_text}, 用户: {username}")
                    return {
                        'success': False,
                        'message': f"登录失败: {error_text}",
                        'platform': 'aviation'
                    }
                elif user_element:
                    username_and_name = user_element.text.strip()
                    logger.info(f"[航空平台-步骤5] 登录成功 - 用户信息: {username_and_name}")

                    # 6. 尝试登录电子超市
                    logger.debug(f"[航空平台-步骤6] 开始登录电子超市 - 用户: {username}")
                    try:
                        electron_result = await self._login_electron_system(
                            session, settings.AVIATION_ELECTRON_URL, request_headers
                        )
                        logger.debug(f"[航空平台-步骤6] 电子超市登录完成 - 结果: {electron_result.get('success', False)}, 用户: {username}")

                    except Exception as e:
                        logger.exception(f"[航空平台-步骤6] 电子超市登录异常 - 用户: {username}")
                        electron_result = {
                            'success': False,
                            'message': f'电子超市登录过程中发生错误: {str(e)}'
                        }

                    return {
                        'success': True,
                        'message': f"航空平台登录成功! 您好{username_and_name}",
                        'platform': 'aviation',
                        'user_info': {
                            'local_username': username,
                            'service_username': username_and_name,
                            'electron_username': electron_result.get('user_name', '') if electron_result.get('success') else '',
                            'platform': 'aviation',
                            'platform_display': '航空平台',
                            'display_name': username_and_name,
                            'electron_access': electron_result.get('success', False),
                            'electron_token': electron_result.get('access_token', '') if electron_result.get('success') else ''
                        },
                        'electron_result': electron_result
                    }
                elif error_element:
                    logger.info(f"[航空平台-步骤5] 登录成功但无权限 - 用户: {username}")

                    # 6. 尝试登录电子超市
                    try:
                        electron_result = await self._login_electron_system(
                            session, settings.AVIATION_ELECTRON_URL, request_headers
                        )

                    except Exception as e:
                        logger.exception(f"[航空平台-步骤6] 电子超市登录异常 - 用户: {username}")
                        electron_result = {
                            'success': False,
                            'message': f'电子超市登录过程中发生错误: {str(e)}'
                        }

                    return {
                        'success': True,
                        'message': "航空平台登录成功但无权限!",
                        'platform': 'aviation',
                        'user_info': {
                            'local_username': username,
                            'service_username': username,
                            'electron_username': electron_result.get('user_name', '') if electron_result.get('success') else '',
                            'platform': 'aviation',
                            'platform_display': '航空平台',
                            'display_name': username,
                            'electron_access': electron_result.get('success', False),
                            'electron_token': electron_result.get('access_token', '') if electron_result.get('success') else ''
                        },
                        'electron_result': electron_result
                    }
                else:
                    logger.error(f"[航空平台-步骤5] 登录结果解析失败 - 未找到预期的页面元素, 用户: {username}")
                    return {
                        'success': False,
                        'message': '航空平台登录失败 - 无法解析登录结果',
                        'platform': 'aviation'
                    }

            except Exception as e:
                logger.exception(f"[航空平台-步骤5] 登录结果检查异常 - 用户: {username}")
                return {
                    'success': False,
                    'message': f'登录结果检查过程中发生错误: {str(e)}',
                    'platform': 'aviation'
                }

        except Exception as e:
            logger.exception(f"[航空平台-全局异常] 登录过程发生未预期的异常 - 用户: {username}")
            return {
                'success': False,
                'message': f'登录过程中发生未预期的错误: {str(e)}',
                'platform': 'aviation'
            }
        finally:
            if session:
                await session.close()
    
    async def login_engine_platform(self, username: str, password: str, client_ip: str = None) -> Dict[str, Any]:
        """
        航发平台异步登录
        基于login_hf.py的逻辑实现
        """
        logger.info(f"开始航发平台登录 - 用户: {username}")

        session = None
        try:
            # 获取包含IP的请求头
            request_headers = self._get_headers_with_ip(client_ip)
            session = aiohttp.ClientSession()

            # 1. 获取加密密码
            logger.debug(f"[航发平台-步骤1] 开始获取加密密码 - 用户: {username}")
            try:
                encrypted_password = await self._get_encrypted_password(
                    session, username, password, "ProjectNewHF", request_headers
                )

                if not encrypted_password:
                    logger.error(f"[航发平台-步骤1] 密码加密失败 - 用户: {username}")
                    return {
                        'success': False,
                        'message': '密码加密失败',
                        'platform': 'engine'
                    }
                logger.debug(f"[航发平台-步骤1] 密码加密成功 - 用户: {username}")

            except Exception as e:
                logger.exception(f"[航发平台-步骤1] 密码加密过程异常 - 用户: {username}")
                return {
                    'success': False,
                    'message': f'密码加密过程中发生错误: {str(e)}',
                    'platform': 'engine'
                }

            # 2. 请求登录页面获取表单参数
            logger.debug(f"[航发平台-步骤2] 开始请求登录页面 - 用户: {username}")
            try:
                login_url = f"{settings.ENGINE_SSO_URL}?service={settings.ENGINE_SERVICE_URL}"
                logger.debug(f"[航发平台-步骤2] 登录URL: {login_url}")

                async with session.get(login_url, headers=request_headers) as response:
                    if response.status != 200:
                        logger.error(f"[航发平台-步骤2] 登录页面请求失败 - 状态码: {response.status}, 用户: {username}")
                        return {
                            'success': False,
                            'message': f'登录页面请求失败: HTTP {response.status}',
                            'platform': 'engine'
                        }

                    html_content = await response.text()
                    soup = BeautifulSoup(html_content, 'html.parser')
                    logger.debug(f"[航发平台-步骤2] 登录页面请求成功 - 用户: {username}")

            except Exception as e:
                logger.exception(f"[航发平台-步骤2] 登录页面请求异常 - 用户: {username}")
                return {
                    'success': False,
                    'message': f'登录页面请求过程中发生错误: {str(e)}',
                    'platform': 'engine'
                }

            # 3. 提取表单参数
            logger.debug(f"[航发平台-步骤3] 开始提取表单参数 - 用户: {username}")
            try:
                lt_input = soup.find('input', {'name': 'lt'})
                execution_input = soup.find('input', {'name': 'execution'})

                if not lt_input or not execution_input:
                    logger.error(f"[航发平台-步骤3] 表单参数提取失败 - lt_input: {lt_input is not None}, execution_input: {execution_input is not None}, 用户: {username}")
                    return {
                        'success': False,
                        'message': '无法获取登录表单参数',
                        'platform': 'engine'
                    }

                lt_value = lt_input['value']
                execution_value = execution_input['value']
                logger.debug(f"[航发平台-步骤3] 表单参数提取成功 - lt: {lt_value}, execution: {execution_value}, 用户: {username}")

            except Exception as e:
                logger.exception(f"[航发平台-步骤3] 表单参数提取异常 - 用户: {username}")
                return {
                    'success': False,
                    'message': f'表单参数提取过程中发生错误: {str(e)}',
                    'platform': 'engine'
                }

            # 4. 提交登录表单
            logger.debug(f"[航发平台-步骤4] 开始提交登录表单 - 用户: {username}")
            try:
                login_data = {
                    'username': username,
                    'password': encrypted_password,
                    'captcha': '1234',  # 固定验证码
                    'lt': lt_value,
                    'execution': execution_value,
                    '_eventId': 'submit',
                    'isajax': 'true',
                    'isframe': 'true',
                    'sorceService': settings.ENGINE_SERVICE_URL
                }

                logger.debug(f"[航发平台-步骤4] 登录表单数据准备完成 - 用户: {username}")

                # 设置重定向参数以处理SSO登录流程中的多次重定向
                # 航发平台SSO可能需要多次重定向，设置合理的重定向次数限制
                async with session.post(
                    login_url,
                    headers=request_headers,
                    data=login_data,
                    max_redirects=15,  # 允许最多15次重定向，足以处理复杂的SSO流程
                    allow_redirects=True  # 明确启用自动重定向
                ) as response:
                    logger.debug(f"[航发平台-步骤4] 登录表单提交完成 - 状态码: {response.status}, 最终URL: {response.url}, 用户: {username}")

                    # 记录重定向历史（如果有的话）
                    if hasattr(response, 'history') and response.history:
                        redirect_count = len(response.history)
                        logger.debug(f"[航发平台-步骤4] 检测到重定向 - 重定向次数: {redirect_count}, 用户: {username}")
                        for i, redirect_response in enumerate(response.history):
                            logger.debug(f"[航发平台-步骤4] 重定向{i+1}: {redirect_response.status} -> {redirect_response.url}")
                    else:
                        logger.debug(f"[航发平台-步骤4] 无重定向发生 - 用户: {username}")

                    if response.status != 200:
                        logger.error(f"[航发平台-步骤4] 登录表单提交失败 - 状态码: {response.status}, 最终URL: {response.url}, 用户: {username}")
                        return {
                            'success': False,
                            'message': f'登录表单提交失败: HTTP {response.status}',
                            'platform': 'engine'
                        }

                    result_html = await response.text()
                    result_soup = BeautifulSoup(result_html, 'html.parser')
                    logger.debug(f"[航发平台-步骤4] 登录表单提交成功 - 响应内容长度: {len(result_html)}, 用户: {username}")

            except aiohttp.TooManyRedirects as e:
                logger.exception(f"[航发平台-步骤4] 重定向次数过多异常 - URL: {e.request_info.url if hasattr(e, 'request_info') else 'Unknown'}, 用户: {username}")
                return {
                    'success': False,
                    'message': f'登录过程中重定向次数过多，请检查SSO配置: {str(e)}',
                    'platform': 'engine'
                }
            except aiohttp.ClientError as e:
                logger.exception(f"[航发平台-步骤4] HTTP客户端异常 - 用户: {username}")
                return {
                    'success': False,
                    'message': f'网络请求异常: {str(e)}',
                    'platform': 'engine'
                }
            except Exception as e:
                logger.exception(f"[航发平台-步骤4] 登录表单提交异常 - 用户: {username}")
                return {
                    'success': False,
                    'message': f'登录表单提交过程中发生错误: {str(e)}',
                    'platform': 'engine'
                }

            # 5. 检查登录结果
            logger.debug(f"[航发平台-步骤5] 开始检查登录结果 - 用户: {username}")
            try:
                user_element = result_soup.find('a', class_='user word-color-0080cb')
                error_element = result_soup.find('div', class_='report-errors')
                error_hint = result_soup.find('div', class_='error-hint')

                if error_hint:
                    error_text = error_hint.text.strip()
                    logger.warning(f"[航发平台-步骤5] 登录失败 - 错误信息: {error_text}, 用户: {username}")
                    return {
                        'success': False,
                        'message': f"登录失败: {error_text}",
                        'platform': 'engine'
                    }
                elif user_element:
                    username_and_name = user_element.text.strip()
                    logger.info(f"[航发平台-步骤5] 登录成功 - 用户信息: {username_and_name}")

                    # 6. 尝试登录电子超市
                    logger.debug(f"[航发平台-步骤6] 开始登录电子超市 - 用户: {username}")
                    try:
                        electron_result = await self._login_electron_system(
                            session, settings.ENGINE_ELECTRON_URL, request_headers
                        )
                        logger.debug(f"[航发平台-步骤6] 电子超市登录完成 - 结果: {electron_result.get('success', False)}, 用户: {username}")

                    except Exception as e:
                        logger.exception(f"[航发平台-步骤6] 电子超市登录异常 - 用户: {username}")
                        electron_result = {
                            'success': False,
                            'message': f'电子超市登录过程中发生错误: {str(e)}'
                        }

                    return {
                        'success': True,
                        'message': f"航发平台登录成功! 您好{username_and_name}",
                        'platform': 'engine',
                        'user_info': {
                            'local_username': username,
                            'service_username': username_and_name,
                            'electron_username': electron_result.get('user_name', '') if electron_result.get('success') else '',
                            'platform': 'engine',
                            'platform_display': '航发平台',
                            'display_name': username_and_name,
                            'electron_access': electron_result.get('success', False),
                            'electron_token': electron_result.get('access_token', '') if electron_result.get('success') else ''
                        },
                        'electron_result': electron_result
                    }
                elif error_element:
                    logger.info(f"[航发平台-步骤5] 登录成功但无权限 - 用户: {username}")

                    # 6. 尝试登录电子超市
                    try:
                        electron_result = await self._login_electron_system(
                            session, settings.ENGINE_ELECTRON_URL, request_headers
                        )

                    except Exception as e:
                        logger.exception(f"[航发平台-步骤6] 电子超市登录异常 - 用户: {username}")
                        electron_result = {
                            'success': False,
                            'message': f'电子超市登录过程中发生错误: {str(e)}'
                        }

                    return {
                        'success': True,
                        'message': "航发平台登录成功! 无权限!",
                        'platform': 'engine',
                        'user_info': {
                            'local_username': username,
                            'service_username': username,
                            'electron_username': electron_result.get('user_name', '') if electron_result.get('success') else '',
                            'platform': 'engine',
                            'platform_display': '航发平台',
                            'display_name': username,
                            'electron_access': electron_result.get('success', False),
                            'electron_token': electron_result.get('access_token', '') if electron_result.get('success') else ''
                        },
                        'electron_result': electron_result
                    }
                else:
                    logger.error(f"[航发平台-步骤5] 登录结果解析失败 - 未找到预期的页面元素, 用户: {username}")
                    return {
                        'success': False,
                        'message': '航发平台登录失败 - 无法解析登录结果',
                        'platform': 'engine'
                    }

            except Exception as e:
                logger.exception(f"[航发平台-步骤5] 登录结果检查异常 - 用户: {username}")
                return {
                    'success': False,
                    'message': f'登录结果检查过程中发生错误: {str(e)}',
                    'platform': 'engine'
                }

        except Exception as e:
            logger.exception(f"[航发平台-全局异常] 登录过程发生未预期的异常 - 用户: {username}")
            return {
                'success': False,
                'message': f'登录过程中发生未预期的错误: {str(e)}',
                'platform': 'engine'
            }
        finally:
            if session:
                await session.close()
    
    async def _get_encrypted_password(
        self,
        session: aiohttp.ClientSession,
        username: str,
        password: str,
        program: str,
        headers: Dict[str, str] = None
    ) -> Optional[str]:
        """获取加密密码"""
        logger.debug(f"[密码加密] 开始密码加密 - 用户: {username}, 程序: {program}")

        try:
            # 1. Base64编码密码
            try:
                encoded_password = base64.b64encode(password.encode()).decode('utf-8')
                logger.debug(f"[密码加密] Base64编码成功 - 用户: {username}")
            except Exception as e:
                logger.exception(f"[密码加密] Base64编码失败 - 用户: {username}")
                return None

            # 2. 构建加密服务URL
            try:
                crypto_url = f"{settings.CRYPTO_SERVICE_URL}?user={username}&pass={encoded_password}&program={program}"
                logger.debug(f"[密码加密] 加密服务URL构建成功 - 用户: {username}, URL: {crypto_url}")
            except Exception as e:
                logger.exception(f"[密码加密] 加密服务URL构建失败 - 用户: {username}")
                return None

            # 3. 请求加密服务
            try:
                request_headers = headers or self.headers
                async with session.get(crypto_url, headers=request_headers) as response:
                    logger.debug(f"[密码加密] 加密服务请求完成 - 状态码: {response.status}, 用户: {username}")

                    if response.status == 200:
                        try:
                            response_text = await response.text()
                            logger.debug(f"[密码加密] 加密服务响应获取成功 - 用户: {username}, 响应长度: {len(response_text)}")

                            response_lines = response_text.splitlines()
                            if response_lines:
                                encrypted_password = response_lines[0].strip().replace("<br />", "")
                                if encrypted_password:
                                    logger.debug(f"[密码加密] 密码加密成功 - 用户: {username}")
                                    return encrypted_password
                                else:
                                    logger.error(f"[密码加密] 加密密码为空 - 用户: {username}")
                                    return None
                            else:
                                logger.error(f"[密码加密] 加密响应内容为空 - 用户: {username}")
                                return None
                        except Exception as e:
                            logger.exception(f"[密码加密] 解析加密响应失败 - 用户: {username}")
                            return None
                    else:
                        logger.error(f"[密码加密] 加密服务请求失败 - 状态码: {response.status}, 用户: {username}")
                        return None
            except Exception as e:
                logger.exception(f"[密码加密] 加密服务请求异常 - 用户: {username}")
                return None

        except Exception as e:
            logger.exception(f"[密码加密] 密码加密过程发生未预期的异常 - 用户: {username}")
            return None
    
    async def _login_electron_system(
        self,
        session: aiohttp.ClientSession,
        electron_url: str,
        headers: Dict[str, str] = None
    ) -> Dict[str, Any]:
        """登录电子超市系统"""
        logger.debug(f"[电子超市] 开始电子超市登录 - URL: {electron_url}")

        try:
            # 1. 请求电子超市获取重定向地址
            logger.debug(f"[电子超市-步骤1] 开始请求电子超市重定向")
            try:
                request_headers = headers or self.headers
                async with session.get(electron_url, headers=request_headers, allow_redirects=False) as response:
                    logger.debug(f"[电子超市-步骤1] 电子超市请求完成 - 状态码: {response.status}")

                    location_url = response.headers.get("Location")
                    if not location_url:
                        logger.error(f"[电子超市-步骤1] 未获取到重定向地址 - 响应头: {dict(response.headers)}")
                        return {
                            'success': False,
                            'message': '未获取到电子超市重定向地址'
                        }

                    logger.debug(f"[电子超市-步骤1] 重定向地址获取成功 - URL: {location_url}")

            except Exception as e:
                logger.exception(f"[电子超市-步骤1] 电子超市重定向请求异常")
                return {
                    'success': False,
                    'message': f'电子超市重定向请求过程中发生错误: {str(e)}'
                }

            # 2. 解析重定向URL中的data参数
            logger.debug(f"[电子超市-步骤2] 开始解析重定向URL参数")
            try:
                parsed_url = urlparse(location_url)
                query_params = parse_qs(parsed_url.query)
                data_value = query_params.get("data", [""])[0]

                if not data_value:
                    logger.error(f"[电子超市-步骤2] 未获取到data参数 - 查询参数: {query_params}")
                    return {
                        'success': False,
                        'message': '未获取到电子超市登录数据'
                    }

                logger.debug(f"[电子超市-步骤2] data参数解析成功 - 长度: {len(data_value)}")

            except Exception as e:
                logger.exception(f"[电子超市-步骤2] URL参数解析异常")
                return {
                    'success': False,
                    'message': f'URL参数解析过程中发生错误: {str(e)}'
                }

            # 3. 发送电子超市登录请求
            logger.debug(f"[电子超市-步骤3] 开始发送电子超市登录请求")
            try:
                post_data = {"data": data_value}
                async with session.post(
                    settings.ESHOP_API_URL,
                    json=post_data,
                    headers=request_headers
                ) as eshop_response:
                    logger.debug(f"[电子超市-步骤3] 电子超市登录请求完成 - 状态码: {eshop_response.status}")

                    if eshop_response.status == 200:
                        try:
                            response_json = await eshop_response.json()
                            logger.debug(f"[电子超市-步骤3] 响应JSON解析成功 - 代码: {response_json.get('code', 'N/A')}")

                            if response_json.get("code") == "200":
                                try:
                                    name = response_json["data"]["authUserInfo"]["name"]
                                    access_token = response_json["data"]["accessToken"]

                                    logger.info(f"[电子超市-步骤3] 电子超市登录成功 - 用户: {name}")
                                    return {
                                        'success': True,
                                        'message': f'电子超市登录成功! {name}',
                                        'user_name': name,
                                        'access_token': access_token
                                    }
                                except KeyError as e:
                                    logger.exception(f"[电子超市-步骤3] 响应数据结构异常 - 缺少字段: {str(e)}")
                                    return {
                                        'success': False,
                                        'message': f'电子超市响应数据结构异常: {str(e)}'
                                    }
                            else:
                                error_msg = response_json.get('msg', 'Unknown error')
                                logger.warning(f"[电子超市-步骤3] 电子超市登录失败 - 错误信息: {error_msg}")
                                return {
                                    'success': False,
                                    'message': f"电子超市登录失败: {error_msg}"
                                }
                        except Exception as e:
                            logger.exception(f"[电子超市-步骤3] 响应JSON解析异常")
                            return {
                                'success': False,
                                'message': f'电子超市响应解析过程中发生错误: {str(e)}'
                            }
                    else:
                        logger.error(f"[电子超市-步骤3] 电子超市登录请求失败 - 状态码: {eshop_response.status}")
                        return {
                            'success': False,
                            'message': f'电子超市登录请求失败: HTTP {eshop_response.status}'
                        }

            except Exception as e:
                logger.exception(f"[电子超市-步骤3] 电子超市登录请求异常")
                return {
                    'success': False,
                    'message': f'电子超市登录请求过程中发生错误: {str(e)}'
                }

        except Exception as e:
            logger.exception(f"[电子超市-全局异常] 电子超市登录过程发生未预期的异常")
            return {
                'success': False,
                'message': f'电子超市登录过程中发生未预期的错误: {str(e)}'
            }


# 创建全局实例
auth_service = AsyncAuthService()
