"""
数据库连接和会话管理
支持SQLite数据库，用于分析统计数据存储
"""

import os
from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from contextlib import contextmanager
from typing import Generator
from pathlib import Path

from app.models.analytics_models import Base
from app.core.config import get_settings
from app.core.logging import logger

settings = get_settings()

# 数据库文件路径
DATABASE_DIR = Path("data")
DATABASE_DIR.mkdir(exist_ok=True)
DATABASE_FILE = DATABASE_DIR / "analytics.db"

# 数据库URL
DATABASE_URL = f"sqlite:///{DATABASE_FILE}"

# 创建数据库引擎
engine = create_engine(
    DATABASE_URL,
    poolclass=StaticPool,
    connect_args={
        "check_same_thread": False,
        "timeout": 30
    },
    echo=settings.DEBUG,  # 在调试模式下显示SQL语句
    pool_pre_ping=True,
    pool_recycle=3600
)

# 启用SQLite的WAL模式和外键约束
@event.listens_for(engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    """设置SQLite优化参数"""
    cursor = dbapi_connection.cursor()
    # 启用WAL模式（Write-Ahead Logging）
    cursor.execute("PRAGMA journal_mode=WAL")
    # 启用外键约束
    cursor.execute("PRAGMA foreign_keys=ON")
    # 设置同步模式为NORMAL（平衡性能和安全性）
    cursor.execute("PRAGMA synchronous=NORMAL")
    # 设置缓存大小（8MB）
    cursor.execute("PRAGMA cache_size=8192")
    # 设置临时存储为内存
    cursor.execute("PRAGMA temp_store=MEMORY")
    cursor.close()

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def init_database():
    """
    初始化数据库，创建所有表
    """
    try:
        logger.info("正在初始化分析统计数据库...")

        # 创建所有表（如果不存在）
        Base.metadata.create_all(bind=engine, checkfirst=True)

        # 初始化默认配置
        with get_db_session() as db:
            init_default_config(db)

        logger.info(f"数据库初始化完成，数据库文件: {DATABASE_FILE}")

    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")
        # 如果是索引已存在的错误，可以忽略
        if "already exists" in str(e):
            logger.warning("数据库表或索引已存在，跳过创建")
        else:
            raise


def init_default_config(db: Session):
    """
    初始化默认配置
    """
    from app.models.analytics_models import AnalyticsConfig
    
    default_configs = [
        {
            'config_key': 'data_retention_days',
            'config_value': '365',
            'config_type': 'integer',
            'description': '数据保留天数，超过此天数的数据将被清理'
        },
        {
            'config_key': 'aggregation_enabled',
            'config_value': 'true',
            'config_type': 'boolean',
            'description': '是否启用数据聚合功能'
        },
        {
            'config_key': 'realtime_monitoring',
            'config_value': 'true',
            'config_type': 'boolean',
            'description': '是否启用实时监控'
        },
        {
            'config_key': 'max_response_time_threshold',
            'config_value': '5000',
            'config_type': 'integer',
            'description': '响应时间阈值(毫秒)，超过此值将被标记为慢查询'
        },
        {
            'config_key': 'module_names',
            'config_value': '{"product": "商品状态", "xqpz": "需求凭证", "xqd": "需求单", "brand": "品牌库", "scraper": "友商数据"}',
            'config_type': 'json',
            'description': '模块名称映射'
        }
    ]
    
    for config_data in default_configs:
        # 检查配置是否已存在
        existing_config = db.query(AnalyticsConfig).filter(
            AnalyticsConfig.config_key == config_data['config_key']
        ).first()
        
        if not existing_config:
            config = AnalyticsConfig(**config_data)
            db.add(config)
    
    db.commit()


@contextmanager
def get_db_session() -> Generator[Session, None, None]:
    """
    获取数据库会话的上下文管理器
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        db.rollback()
        logger.error(f"数据库操作失败: {str(e)}")
        raise
    finally:
        db.close()


def get_db() -> Generator[Session, None, None]:
    """
    FastAPI依赖注入用的数据库会话获取函数
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def check_database_health() -> dict:
    """
    检查数据库健康状态
    """
    try:
        from sqlalchemy import text

        with get_db_session() as db:
            # 执行简单查询测试连接
            result = db.execute(text("SELECT 1")).fetchone()

            # 检查表是否存在
            tables_query = text("""
            SELECT name FROM sqlite_master
            WHERE type='table' AND name NOT LIKE 'sqlite_%'
            """)
            tables = db.execute(tables_query).fetchall()
            table_names = [table[0] for table in tables]

            # 检查数据库文件大小
            db_size = DATABASE_FILE.stat().st_size if DATABASE_FILE.exists() else 0

            return {
                'status': 'healthy',
                'connection': 'ok',
                'database_file': str(DATABASE_FILE),
                'database_size_mb': round(db_size / 1024 / 1024, 2),
                'tables': table_names,
                'table_count': len(table_names)
            }

    except Exception as e:
        return {
            'status': 'unhealthy',
            'error': str(e),
            'database_file': str(DATABASE_FILE)
        }


def cleanup_old_data(retention_days: int = 90):
    """
    清理旧数据
    """
    try:
        from app.models.analytics_models import QueryLog, ModuleStats, SystemHealth
        from datetime import datetime, timedelta
        
        cutoff_date = datetime.now() - timedelta(days=retention_days)
        cutoff_date_str = cutoff_date.strftime('%Y-%m-%d')
        
        with get_db_session() as db:
            # 清理查询日志
            deleted_logs = db.query(QueryLog).filter(
                QueryLog.date < cutoff_date_str
            ).delete()
            
            # 清理模块统计
            deleted_stats = db.query(ModuleStats).filter(
                ModuleStats.date < cutoff_date_str
            ).delete()
            
            # 清理系统健康记录
            deleted_health = db.query(SystemHealth).filter(
                SystemHealth.date < cutoff_date_str
            ).delete()
            
            db.commit()
            
            logger.info(f"数据清理完成 - 删除记录: 查询日志({deleted_logs}), 模块统计({deleted_stats}), 系统健康({deleted_health})")
            
            return {
                'success': True,
                'deleted_logs': deleted_logs,
                'deleted_stats': deleted_stats,
                'deleted_health': deleted_health,
                'cutoff_date': cutoff_date_str
            }
            
    except Exception as e:
        logger.error(f"数据清理失败: {str(e)}")
        return {
            'success': False,
            'error': str(e)
        }


# 在模块导入时初始化数据库
if not DATABASE_FILE.exists() or DATABASE_FILE.stat().st_size == 0:
    init_database()
