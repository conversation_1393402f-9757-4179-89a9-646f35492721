{% extends "base.html" %}

{% block title %}{{ app_name }} - 需求单查询{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="/static/css/modules/xqd.css?ver={{ app_version }}">
{% endblock %}

{% block content %}
<div class="page-header d-print-none">
    <div class="container-xl">
        <div class="row g-2 align-items-center">
            <div class="col">
                <!-- 面包屑导航 -->
                <div class="page-pretitle">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="/">控制台</a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">需求单查询</li>
                        </ol>
                    </nav>
                </div>
                <h2 class="page-title">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler me-2 text-success" width="32" height="32" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2"></path>
                        <path d="M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z"></path>
                        <path d="M9 12l2 2l4 -4"></path>
                    </svg>
                    需求单查询
                </h2>
                <div class="text-muted mt-1">
                    采集和管理需求单数据，支持多条件查询和状态跟踪
                </div>
            </div>
            <div class="col-auto ms-auto d-print-none">
                <div class="btn-list">
                    <a href="/" class="btn btn-outline-secondary">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M5 12l14 0"></path>
                            <path d="M5 12l6 6"></path>
                            <path d="M5 12l6 -6"></path>
                        </svg>
                        返回控制台
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="page-body">
    <div class="container-xl">
        <!-- 登录提示 -->
        <div class="row" id="login-prompt">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <h3>需要登录</h3>
                        <p class="text-muted">请先登录到电子超市以使用需求单查询功能</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#login-modal">
                            立即登录
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- ELECTRON权限不足提示 -->
        <div class="row" id="electron-access-denied" style="display: none;">
            <div class="col-12">
                <div class="card border-warning">
                    <div class="card-header bg-warning-lt">
                        <h3 class="card-title text-warning">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M12 9v2m0 4v.01"></path>
                                <path d="M5 19h14a2 2 0 0 0 1.84 -2.75l-7.1 -12.25a2 2 0 0 0 -3.5 0l-7.1 12.25a2 2 0 0 0 1.75 2.75"></path>
                            </svg>
                            电子超市平台权限不足
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <h4>无法访问需求单查询</h4>
                                <p class="text-muted mb-3">
                                    您已成功登录到商城，但电子超市系统登录失败。
                                    需求单查询功能需要电子超市平台权限才能正常使用。
                                </p>
                                <div class="alert alert-info">
                                    <h5 class="alert-title">可能的原因：</h5>
                                    <ul class="mb-0">
                                        <li>您的账号没有电子超市平台访问权限</li>
                                        <li>电子超市系统暂时不可用</li>
                                        <li>网络连接问题导致电子超市登录失败</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-auto">
                                <div class="avatar avatar-lg bg-warning-lt text-warning">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="32" height="32" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"></path>
                                        <path d="M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6"></path>
                                        <path d="M3 3l18 18"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4">
                            <button class="btn btn-warning me-2" data-bs-toggle="modal" data-bs-target="#login-modal">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"></path>
                                    <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"></path>
                                </svg>
                                重新登录
                            </button>
                            <a href="/" class="btn btn-outline-secondary">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M5 12l14 0"></path>
                                    <path d="M5 12l6 6"></path>
                                    <path d="M5 12l6 -6"></path>
                                </svg>
                                返回控制台
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 模块内容 -->
        <div id="main-content" style="display: none;">
            <!-- 查询输入卡片 -->
            <div class="card mb-3 query-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"></path>
                            <path d="M21 21l-6 -6"></path>
                        </svg>
                        需求单查询条件
                    </h5>
                    <div class="card-actions">
                        <button class="btn btn-sm btn-outline-secondary" onclick="clearXqdQueryCondition()">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M4 7l16 0"></path>
                                <path d="M10 11l0 6"></path>
                                <path d="M14 11l0 6"></path>
                                <path d="M5 7l1 -4l4 0l1 4"></path>
                                <path d="M9 7l6 0"></path>
                            </svg>
                            清空条件
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <form id="xqd-form" class="compact-form">
                        <!-- 第一行：比价单号 + 比价单名称 + 采购清单信息 -->
                        <div class="row">
                            <div class="col-md-4 mb-2">
                                <label class="form-label">比价单号</label>
                                <input type="text" class="form-control" name="askSheetCode"
                                    placeholder="如：XJD202506050099">
                                <div class="form-text">模糊匹配</div>
                            </div>
                            <div class="col-md-4 mb-2">
                                <label class="form-label">比价单名称</label>
                                <input type="text" class="form-control" name="askSheetName"
                                    placeholder="如：生产辅料材料">
                                <div class="form-text">模糊搜索</div>
                            </div>
                            <div class="col-md-4 mb-2">
                                <label class="form-label">采购清单信息</label>
                                <input type="text" class="form-control" name="prodName"
                                    placeholder="如：茶叶">
                                <div class="form-text">商品关键词</div>
                            </div>
                        </div>

                        <!-- 第二行：发布企业 + 询价单状态 -->
                        <div class="row">
                            <div class="col-md-6 mb-2">
                                <label class="form-label">发布企业</label>
                                <!-- 搜索下拉框容器 -->
                                <div class="search-dropdown-container" id="purchaser-search-container">
                                    <!-- 搜索输入框 -->
                                    <div class="search-input-wrapper">
                                        <input type="text"
                                               class="form-control search-input"
                                               id="purchaser-search-input"
                                               name="purchaserName"
                                               placeholder="搜索或选择发布企业..."
                                               autocomplete="off">
                                        <!-- 清除按钮 -->
                                        <button type="button" class="clear-btn" id="purchaser-clear-btn" style="display: none;" title="清除选择">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                <line x1="18" y1="6" x2="6" y2="18"></line>
                                                <line x1="6" y1="6" x2="18" y2="18"></line>
                                            </svg>
                                        </button>
                                        <!-- 刷新按钮 -->
                                        <button type="button" class="refresh-btn" id="purchaser-refresh-btn" title="刷新企业列表">
                                            <svg class="refresh-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"></path>
                                                <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"></path>
                                            </svg>
                                        </button>
                                        <!-- 下拉箭头 -->
                                        <svg class="dropdown-arrow" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                            <polyline points="6,9 12,15 18,9"></polyline>
                                        </svg>
                                    </div>
                                    <!-- 下拉选项列表 -->
                                    <div class="search-dropdown-list" id="purchaser-dropdown-list">
                                        <div class="dropdown-item no-results" style="display: none;">
                                            <span class="text-muted">未找到匹配的企业</span>
                                        </div>
                                        <div class="dropdown-item loading-item">
                                            <i class="spinner-border spinner-border-sm me-2" role="status"></i>
                                            <span>加载中...</span>
                                        </div>
                                    </div>
                                </div>
                                <!-- 隐藏的原始select元素，用于表单提交 -->
                                <select class="d-none" name="purchaserName" id="purchaser-select">
                                    <option value="">请选择发布企业</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-2">
                                <label class="form-label">询价单状态</label>
                                <select class="form-select" name="askSheetStatus">
                                    <option value="">请选择状态</option>
                                    <option value="DRAFT">草稿</option>
                                    <option value="INQUIRING">询价中</option>
                                    <option value="CONFIRM_WAIT">决标中</option>
                                    <option value="FINISHED">已完成</option>
                                    <option value="APPROVAL_ING">审批中</option>
                                </select>
                            </div>
                        </div>

                        <!-- 第三行：日期时间范围选择器 -->
                        <div class="row">
                            <div class="col-md-6 mb-2">
                                <label class="form-label">报价开始时间范围</label>
                                <div class="datetime-range-picker" data-field="answerBeginTime">
                                    <input type="text" class="form-control datetime-range-input"
                                           placeholder="请选择报价开始时间范围" readonly>
                                    <input type="hidden" name="answerBeginTimeStart">
                                    <input type="hidden" name="answerBeginTimeEnd">
                                    <div class="datetime-range-panel">
                                        <div class="datetime-range-header">
                                            <h6 class="mb-0">选择报价开始时间范围</h6>
                                            <button type="button" class="btn-close" onclick="closeDateTimeRangePicker(this)"></button>
                                        </div>
                                        <div class="datetime-range-content">
                                            <div class="datetime-range-section">
                                                <h6>开始时间</h6>
                                                <input type="date" class="form-control start-date">
                                                <input type="time" class="form-control start-time" value="00:00">
                                            </div>
                                            <div class="datetime-range-section">
                                                <h6>结束时间</h6>
                                                <input type="date" class="form-control end-date">
                                                <input type="time" class="form-control end-time" value="23:59">
                                            </div>
                                        </div>
                                        <div class="datetime-range-footer">
                                            <div class="datetime-range-shortcuts">
                                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setDateTimeRangeShortcut(this, 'today')">今天</button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setDateTimeRangeShortcut(this, 'last7days')">最近7天</button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setDateTimeRangeShortcut(this, 'thisMonth')">本月</button>
                                            </div>
                                            <div class="datetime-range-actions">
                                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearDateTimeRange(this)">清空</button>
                                                <button type="button" class="btn btn-sm btn-primary" onclick="confirmDateTimeRange(this)">确定</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-2">
                                <label class="form-label">报价截止时间范围</label>
                                <div class="datetime-range-picker" data-field="answerEndTime">
                                    <input type="text" class="form-control datetime-range-input"
                                           placeholder="请选择报价截止时间范围" readonly>
                                    <input type="hidden" name="answerEndTimeStart">
                                    <input type="hidden" name="answerEndTimeEnd">
                                    <div class="datetime-range-panel">
                                        <div class="datetime-range-header">
                                            <h6 class="mb-0">选择报价截止时间范围</h6>
                                            <button type="button" class="btn-close" onclick="closeDateTimeRangePicker(this)"></button>
                                        </div>
                                        <div class="datetime-range-content">
                                            <div class="datetime-range-section">
                                                <h6>开始时间</h6>
                                                <input type="date" class="form-control start-date">
                                                <input type="time" class="form-control start-time" value="00:00">
                                            </div>
                                            <div class="datetime-range-section">
                                                <h6>结束时间</h6>
                                                <input type="date" class="form-control end-date">
                                                <input type="time" class="form-control end-time" value="23:59">
                                            </div>
                                        </div>
                                        <div class="datetime-range-footer">
                                            <div class="datetime-range-shortcuts">
                                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setDateTimeRangeShortcut(this, 'today')">今天</button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setDateTimeRangeShortcut(this, 'last7days')">最近7天</button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setDateTimeRangeShortcut(this, 'thisMonth')">本月</button>
                                            </div>
                                            <div class="datetime-range-actions">
                                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearDateTimeRange(this)">清空</button>
                                                <button type="button" class="btn btn-sm btn-primary" onclick="confirmDateTimeRange(this)">确定</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 按钮区域 -->
                        <div class="d-flex gap-2 mt-3">
                            <button type="submit" class="btn btn-primary" id="xqd-submit-btn">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"></path>
                                    <path d="M21 21l-6 -6"></path>
                                </svg>
                                查询需求单
                            </button>

                        </div>
                    </form>
                </div>
            </div>

            <!-- 筛选器面板 -->
            <div id="data-filter-container"></div>

            <!-- 数据结果卡片 -->
            <div class="card results-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M3 3m0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z"></path>
                            <path d="M7 7h10"></path>
                            <path d="M7 12h10"></path>
                            <path d="M7 17h10"></path>
                        </svg>
                        需求单查询结果
                    </h5>
                    <div class="card-actions">
                        <span class="badge bg-secondary" id="xqd-count">0 条记录</span>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div id="xqd-results"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="/static/js/button-manager.js?ver={{ app_version }}"></script>
<script src="/static/js/pagination-manager.js?ver={{ app_version }}"></script>
<script src="/static/js/data-filter.js?ver={{ app_version }}"></script>
<script src="/static/js/modules/xqd.js?ver={{ app_version }}"></script>
{% endblock %}
