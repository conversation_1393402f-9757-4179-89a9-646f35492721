/**
 * 数据筛选器组件
 * 为数据采集模块提供动态筛选功能
 */

class DataFilter {
    constructor(containerId, options = {}) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        this.options = {
            storageKey: options.storageKey || 'data-filter',
            debounceDelay: options.debounceDelay || 300,
            useTabSeparatedCopy: options.useTabSeparatedCopy || false, // 是否使用制表符分隔的复制格式
            ...options
        };

        this.originalData = [];
        this.filteredData = [];
        this.filters = [];
        this.columns = [];
        this.isCollapsed = true;
        this.selectedRows = new Set(); // 选中的行索引
        this.lastSelectedIndex = null; // 上次选择的行索引，用于范围选择
        this.isInitialized = false; // 初始化标志

        this.init();
    }
    
    init() {
        if (!this.container) {
            console.error(`筛选器容器 ${this.containerId} 未找到`);
            return;
        }

        this.createFilterPanel();
        this.loadFilterState();
        this.bindKeyboardShortcuts();
    }
    
    /**
     * 创建筛选器面板
     */
    createFilterPanel() {
        const panelHTML = `
            <div class="card mb-3" id="filter-panel">
                <div class="card-header">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center cursor-pointer" onclick="dataFilter.togglePanel()">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M5.5 5h13a1 1 0 0 1 .5 1.5l-5 5.5l0 7l-4 0l0 -7l-5 -5.5a1 1 0 0 1 .5 -1.5"></path>
                            </svg>
                            <h5 class="card-title mb-0">数据筛选器</h5>
                            <span class="badge bg-primary ms-2" id="filter-count">0个筛选条件</span>
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-2" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round" id="collapse-icon">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M6 9l6 6l6 -6"></path>
                            </svg>
                        </div>
                        <div class="d-flex align-items-center gap-2">
                            <span class="text-muted" id="filter-stats">显示0条，共0条记录</span>
                            <span class="text-muted small" id="selection-stats" style="display: none;">已选择0条记录</span>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="dataFilter.selectAll()" title="全选当前数据 (Ctrl+A)">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M9 11l3 3l8 -8"></path>
                                        <path d="M20 12v6a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12c0 -1.1 .9 -2 2 -2h9"></path>
                                    </svg>
                                    全选
                                </button>
                                <button type="button" class="btn btn-outline-warning btn-sm" onclick="dataFilter.clearSelection()" title="取消选择 (Esc)">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M18 6l-12 12"></path>
                                        <path d="M6 6l12 12"></path>
                                    </svg>
                                    取消
                                </button>
                                <button type="button" class="btn btn-outline-success btn-sm" onclick="dataFilter.copyData()" title="复制数据 (Ctrl+C)">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M8 8m0 2a2 2 0 0 1 2 -2h8a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-8a2 2 0 0 1 -2 -2z"></path>
                                        <path d="M16 8v-2a2 2 0 0 0 -2 -2h-8a2 2 0 0 0 -2 2v8a2 2 0 0 0 2 2h2"></path>
                                    </svg>
                                    复制
                                </button>
                                <button type="button" class="btn btn-outline-info btn-sm" onclick="dataFilter.exportData('excel')" title="导出Excel">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M14 3v4a1 1 0 0 0 1 1h4"></path>
                                        <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"></path>
                                        <path d="M9 9l1 0"></path>
                                        <path d="M9 13l6 0"></path>
                                        <path d="M9 17l6 0"></path>
                                    </svg>
                                    Excel
                                </button>
                                <button type="button" class="btn btn-outline-info btn-sm" onclick="dataFilter.exportData('csv')" title="导出CSV">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M14 3v4a1 1 0 0 0 1 1h4"></path>
                                        <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"></path>
                                        <path d="M9 9l1 0"></path>
                                        <path d="M9 13l6 0"></path>
                                        <path d="M9 17l6 0"></path>
                                    </svg>
                                    CSV
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body" id="filter-body" style="display: none;">
                    <div id="filter-controls"></div>
                    <div class="mt-3">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="dataFilter.addFilter()">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M12 5l0 14"></path>
                                    <path d="M5 12l14 0"></path>
                                </svg>
                                添加筛选条件
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="dataFilter.clearAllFilters()">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M4 7l16 0"></path>
                                    <path d="M10 11l0 6"></path>
                                    <path d="M14 11l0 6"></path>
                                    <path d="M5 7l1 -4l4 0l1 4"></path>
                                    <path d="M9 7l6 0"></path>
                                </svg>
                                清除所有筛选
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        this.container.innerHTML = panelHTML;
    }
    
    /**
     * 设置数据并生成筛选器
     */
    setData(data, columns) {
        this.originalData = data;
        this.filteredData = [...data];
        this.columns = columns;
        this.selectedRows.clear(); // 清空选择状态

        this.updateStats();
        this.updateSelectionStats();
        this.generateColumnFilters();
    }

    /**
     * 生成列筛选器
     */
    generateColumnFilters() {
        if (this.columns.length === 0) return;

        // 只有在首次初始化且没有保存的筛选条件时才添加默认筛选条件
        if (!this.isInitialized && this.filters.length === 0) {
            this.addFilter();
            this.isInitialized = true;
        } else if (this.filters.length > 0) {
            // 如果有保存的筛选条件，重新渲染
            this.renderFilters();
            this.applyFilters();
        }
    }
    
    /**
     * 添加筛选条件
     */
    addFilter() {
        const filterId = `filter-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const filter = {
            id: filterId,
            column: '',
            operator: 'equals',
            value: ''
        };
        
        this.filters.push(filter);
        this.renderFilters();
        this.saveFilterState();
    }
    
    /**
     * 移除筛选条件
     */
    removeFilter(filterId) {
        this.filters = this.filters.filter(f => f.id !== filterId);
        this.renderFilters();
        this.applyFilters();
        this.saveFilterState();
    }
    
    /**
     * 渲染筛选条件
     */
    renderFilters() {
        const controlsContainer = document.getElementById('filter-controls');
        if (!controlsContainer) return;
        
        controlsContainer.innerHTML = '';
        
        this.filters.forEach((filter, index) => {
            const filterHTML = this.createFilterHTML(filter, index);
            controlsContainer.insertAdjacentHTML('beforeend', filterHTML);
        });
        
        this.updateFilterCount();
    }
    
    /**
     * 创建单个筛选条件HTML
     */
    createFilterHTML(filter, index) {
        const operators = [
            { value: 'equals', label: '等于' },
            { value: 'not_equals', label: '不等于' },
            { value: 'contains', label: '包含' },
            { value: 'not_contains', label: '不包含' },
            { value: 'is_empty', label: '为空' },
            { value: 'is_not_empty', label: '非空' }
        ];
        
        const columnOptions = this.columns.map(col => 
            `<option value="${col.key}" ${filter.column === col.key ? 'selected' : ''}>${col.label}</option>`
        ).join('');
        
        const operatorOptions = operators.map(op => 
            `<option value="${op.value}" ${filter.operator === op.value ? 'selected' : ''}>${op.label}</option>`
        ).join('');
        
        return `
            <div class="row mb-2" id="${filter.id}">
                <div class="col-md-3">
                    <select class="form-select form-select-sm" onchange="dataFilter.updateFilter('${filter.id}', 'column', this.value)">
                        <option value="">选择字段</option>
                        ${columnOptions}
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select form-select-sm" onchange="dataFilter.updateFilter('${filter.id}', 'operator', this.value)">
                        ${operatorOptions}
                    </select>
                </div>
                <div class="col-md-5">
                    ${this.createValueInput(filter)}
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="dataFilter.removeFilter('${filter.id}')">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M4 7l16 0"></path>
                            <path d="M10 11l0 6"></path>
                            <path d="M14 11l0 6"></path>
                            <path d="M5 7l1 -4l4 0l1 4"></path>
                            <path d="M9 7l6 0"></path>
                        </svg>
                    </button>
                </div>
            </div>
        `;
    }
    
    /**
     * 创建值输入控件
     */
    createValueInput(filter) {
        if (filter.operator === 'is_empty' || filter.operator === 'is_not_empty') {
            return '<span class="form-control-plaintext text-muted small">无需输入值</span>';
        }

        if (filter.operator === 'equals' || filter.operator === 'not_equals') {
            // 生成下拉列表，包含该字段的所有唯一值
            const uniqueValues = this.getUniqueValues(filter.column);
            const options = uniqueValues.map(value =>
                `<option value="${this.escapeHtml(value)}" ${filter.value === value ? 'selected' : ''}>${this.escapeHtml(value)}</option>`
            ).join('');

            return `
                <select class="form-select form-select-sm" onchange="dataFilter.updateFilter('${filter.id}', 'value', this.value)">
                    <option value="">选择值</option>
                    ${options}
                </select>
            `;
        }

        // 文本输入框
        return `
            <input type="text" class="form-control form-control-sm"
                   value="${this.escapeHtml(filter.value || '')}"
                   placeholder="输入筛选值"
                   oninput="dataFilter.debounceUpdateFilter('${filter.id}', 'value', this.value)">
        `;
    }
    
    /**
     * 获取字段的唯一值
     */
    getUniqueValues(columnKey) {
        if (!columnKey || this.originalData.length === 0) return [];
        
        const values = this.originalData.map(item => {
            const value = item[columnKey];
            return value !== null && value !== undefined ? String(value) : '';
        });
        
        return [...new Set(values)].filter(v => v !== '').sort();
    }
    
    /**
     * 更新筛选条件
     */
    updateFilter(filterId, property, value) {
        const filter = this.filters.find(f => f.id === filterId);
        if (!filter) return;
        
        filter[property] = value;
        
        // 如果改变了字段或操作符，重新渲染该筛选条件
        if (property === 'column' || property === 'operator') {
            filter.value = ''; // 重置值
            this.renderFilters();
        }
        
        this.applyFilters();
        this.saveFilterState();
    }
    
    /**
     * 防抖更新筛选条件
     */
    debounceUpdateFilter(filterId, property, value) {
        clearTimeout(this.debounceTimer);
        this.debounceTimer = setTimeout(() => {
            this.updateFilter(filterId, property, value);
        }, this.options.debounceDelay);
    }
    
    /**
     * 应用筛选条件
     */
    applyFilters() {
        this.filteredData = this.originalData.filter(item => {
            return this.filters.every(filter => {
                if (!filter.column) return true;

                const itemValue = String(item[filter.column] || '');
                const filterValue = String(filter.value || '');

                switch (filter.operator) {
                    case 'equals':
                        if (filterValue === '') return true; // 空值时不筛选
                        return itemValue === filterValue;
                    case 'not_equals':
                        if (filterValue === '') return true; // 空值时不筛选
                        return itemValue !== filterValue;
                    case 'contains':
                        if (filterValue === '') return true; // 空值时不筛选
                        return itemValue.toLowerCase().includes(filterValue.toLowerCase());
                    case 'not_contains':
                        if (filterValue === '') return true; // 空值时不筛选
                        return !itemValue.toLowerCase().includes(filterValue.toLowerCase());
                    case 'is_empty':
                        return itemValue === '' || itemValue === null || itemValue === undefined;
                    case 'is_not_empty':
                        return itemValue !== '' && itemValue !== null && itemValue !== undefined;
                    default:
                        return true;
                }
            });
        });

        this.updateStats();
        this.updateFilterCount();

        // 筛选后需要清理无效的选择状态
        this.cleanupInvalidSelections();
        this.updateSelectionStats();

        this.onFilterChange(this.filteredData);
    }
    
    /**
     * 筛选变化回调
     */
    onFilterChange(filteredData) {
        // 子类可以重写此方法
        if (this.options.onFilterChange) {
            this.options.onFilterChange(filteredData);
        }
    }
    
    /**
     * 更新统计信息
     */
    updateStats() {
        const statsElement = document.getElementById('filter-stats');
        if (statsElement) {
            statsElement.textContent = `显示${this.filteredData.length}条，共${this.originalData.length}条记录`;
        }
    }

    /**
     * 更新选择状态统计
     */
    updateSelectionStats() {
        const selectionStatsElement = document.getElementById('selection-stats');
        if (selectionStatsElement) {
            if (this.selectedRows.size > 0) {
                selectionStatsElement.textContent = `已选择${this.selectedRows.size}条记录`;
                selectionStatsElement.style.display = 'inline';
            } else {
                selectionStatsElement.style.display = 'none';
            }
        }
    }
    
    /**
     * 更新筛选条件数量
     */
    updateFilterCount() {
        const countElement = document.getElementById('filter-count');
        if (countElement) {
            const activeFilters = this.filters.filter(f => {
                if (!f.column) return false;
                if (f.operator === 'is_empty' || f.operator === 'is_not_empty') return true;
                return f.value !== '' && f.value !== null && f.value !== undefined;
            });
            countElement.textContent = `${activeFilters.length}个筛选条件`;
        }
    }
    
    /**
     * 切换面板展开/折叠
     */
    togglePanel() {
        this.isCollapsed = !this.isCollapsed;
        const body = document.getElementById('filter-body');
        const icon = document.getElementById('collapse-icon');
        
        if (body && icon) {
            if (this.isCollapsed) {
                body.style.display = 'none';
                icon.innerHTML = '<path stroke="none" d="M0 0h24v24H0z" fill="none"></path><path d="M6 9l6 6l6 -6"></path>';
            } else {
                body.style.display = 'block';
                icon.innerHTML = '<path stroke="none" d="M0 0h24v24H0z" fill="none"></path><path d="M18 15l-6 -6l-6 6"></path>';
            }
        }
        
        this.saveFilterState();
    }
    
    /**
     * 清除所有筛选条件
     */
    clearAllFilters() {
        this.filters = [];
        this.filteredData = [...this.originalData];
        this.selectedRows.clear(); // 清空选择状态
        this.renderFilters();
        this.updateStats();
        this.updateSelectionStats();
        this.onFilterChange(this.filteredData);
        this.saveFilterState();
    }

    /**
     * 切换行选择状态
     */
    toggleRowSelection(rowIndex, event) {
        if (event) {
            event.stopPropagation(); // 防止事件冒泡

            // 处理行点击的高级选择功能
            if (event.ctrlKey || event.metaKey) {
                // Ctrl+点击：多选模式
                this.handleCtrlClick(rowIndex);
                this.updateRowSelectionUI(rowIndex);
            } else if (event.shiftKey) {
                // Shift+点击：范围选择（UI更新在handleShiftClick中处理）
                this.handleShiftClick(rowIndex);
            } else {
                // 普通点击：单选模式（UI更新在handleNormalClick中处理）
                this.handleNormalClick(rowIndex);
            }
        } else {
            // 无事件参数的切换（程序调用）
            if (this.selectedRows.has(rowIndex)) {
                this.selectedRows.delete(rowIndex);
            } else {
                this.selectedRows.add(rowIndex);
            }
            this.updateRowSelectionUI(rowIndex);
        }

        this.updateSelectionStats();
    }

    /**
     * 处理普通点击（单选模式）
     */
    handleNormalClick(rowIndex) {
        const wasSelected = this.selectedRows.has(rowIndex);
        this.selectedRows.clear();
        if (!wasSelected) {
            this.selectedRows.add(rowIndex);
        }
        this.lastSelectedIndex = rowIndex;

        // 更新所有行的UI，因为清空选择会影响多行
        this.updateAllRowSelectionUI();
    }

    /**
     * 处理Ctrl+点击（多选模式）
     */
    handleCtrlClick(rowIndex) {
        if (this.selectedRows.has(rowIndex)) {
            this.selectedRows.delete(rowIndex);
        } else {
            this.selectedRows.add(rowIndex);
        }
        this.lastSelectedIndex = rowIndex;
    }

    /**
     * 处理Shift+点击（范围选择）
     */
    handleShiftClick(rowIndex) {
        if (this.lastSelectedIndex !== undefined && this.lastSelectedIndex !== null) {
            const start = Math.min(this.lastSelectedIndex, rowIndex);
            const end = Math.max(this.lastSelectedIndex, rowIndex);

            // 选择范围内的所有行
            for (let i = start; i <= end; i++) {
                this.selectedRows.add(i);
            }

            // 不更新lastSelectedIndex，保持原来的起始点用于下次范围选择
        } else {
            // 如果没有上次选择的索引，就当作普通点击
            this.selectedRows.add(rowIndex);
            this.lastSelectedIndex = rowIndex;
        }

        // 统一更新所有UI
        this.updateAllRowSelectionUI();
    }



    /**
     * 更新单行选择UI
     */
    updateRowSelectionUI(rowIndex) {
        // 更新行的视觉状态
        const row = document.querySelector(`tr[data-row-index="${rowIndex}"]`);
        if (row) {
            if (this.selectedRows.has(rowIndex)) {
                row.classList.add('table-active');
            } else {
                row.classList.remove('table-active');
            }
        }
    }

    /**
     * 更新所有行选择UI
     */
    updateAllRowSelectionUI() {
        // 更新所有行
        for (let i = 0; i < this.filteredData.length; i++) {
            this.updateRowSelectionUI(i);
        }

        // 更新选择状态统计
        this.updateSelectionStats();
    }
    
    /**
     * 复制选中的数据或所有数据（使用逗号分隔符，兼容CSV格式）
     */
    async copySelectedData() {
        let dataToCopy;
        let message;

        if (this.selectedRows.size > 0) {
            // 复制选中的行
            dataToCopy = Array.from(this.selectedRows).map(index => this.filteredData[index]);
            message = `已复制${this.selectedRows.size}条选中记录到剪贴板`;
        } else {
            // 复制所有筛选后的数据
            if (this.filteredData.length === 0) {
                this.showNotification('没有数据可复制', 'warning');
                return;
            }
            dataToCopy = this.filteredData;
            message = `已复制${this.filteredData.length}条记录到剪贴板`;
        }

        try {
            const csvData = this.convertToCSV(dataToCopy);
            await navigator.clipboard.writeText(csvData);
            this.showNotification(message, 'success');
        } catch (error) {
            console.error('复制失败:', error);
            this.showNotification('复制失败，请手动选择数据', 'error');
        }
    }

    /**
     * 复制选中的数据或所有数据（使用制表符分隔符，适合Excel粘贴）
     */
    async copySelectedDataWithTabs() {
        let dataToCopy;
        let message;

        if (this.selectedRows.size > 0) {
            // 复制选中的行
            dataToCopy = Array.from(this.selectedRows).map(index => this.filteredData[index]);
            message = `已复制${this.selectedRows.size}条选中记录到剪贴板（制表符格式）`;
        } else {
            // 复制所有筛选后的数据
            if (this.filteredData.length === 0) {
                this.showNotification('没有数据可复制', 'warning');
                return;
            }
            dataToCopy = this.filteredData;
            message = `已复制${this.filteredData.length}条记录到剪贴板（制表符格式）`;
        }

        try {
            const tabData = this.convertToTabSeparated(dataToCopy);
            await navigator.clipboard.writeText(tabData);
            this.showNotification(message, 'success');
        } catch (error) {
            console.error('复制失败:', error);
            this.showNotification('复制失败，请手动选择数据', 'error');
        }
    }

    /**
     * 根据配置选择复制格式的统一复制方法
     */
    async copyData() {
        if (this.options.useTabSeparatedCopy) {
            await this.copySelectedDataWithTabs();
        } else {
            await this.copySelectedData();
        }
    }
    
    /**
     * 导出数据
     */
    exportData(format) {
        let dataToExport;
        let filenamePrefix;

        if (this.selectedRows.size > 0) {
            // 导出选中的行
            dataToExport = Array.from(this.selectedRows).map(index => this.filteredData[index]);
            filenamePrefix = `选中数据导出_${this.selectedRows.size}条`;
        } else {
            // 导出所有筛选后的数据
            if (this.filteredData.length === 0) {
                this.showNotification('没有数据可导出', 'warning');
                return;
            }
            dataToExport = this.filteredData;
            filenamePrefix = `数据导出_${this.filteredData.length}条`;
        }

        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const filename = `${filenamePrefix}_${timestamp}`;

        if (format === 'csv') {
            this.downloadCSV(dataToExport, `${filename}.csv`);
        } else if (format === 'excel') {
            this.downloadExcel(dataToExport, `${filename}.xlsx`);
        }
    }
    
    /**
     * 状态字段映射配置（用于导出时的中文转换）
     */
    getStatusMappings() {
        return {
            // 上架状态映射
            shelfStatus: {
                'UP_SHELF': '上架',
                'DOWN_SHELF': '下架'
            },
            // 下架类型映射
            delistType: {
                'OPERATE_DELIST': '运营下架',
                'SYSTEM_DELIST': '系统下架',
                'OPERATE_DELIST_FOREVER': '永久下架',
                'SUPPLIER_DELIST': '供应商下架'
            },
            // 审核状态映射
            approvalStatus: {
                'APPROVE': '待审核',
                'REJECT': '驳回',
                'END': '结束'
            }
        };
    }

    /**
     * 转换状态字段值为中文（用于导出）
     */
    convertStatusToChineseForExport(key, value) {
        const statusMappings = this.getStatusMappings();

        // 如果是状态字段，进行中文转换
        if (statusMappings[key] && statusMappings[key][value]) {
            return statusMappings[key][value];
        }

        // 非状态字段或未找到映射，返回原值
        return value;
    }

    /**
     * 转换为CSV格式（使用逗号分隔符）
     */
    convertToCSV(data) {
        if (data.length === 0) return '';

        const headers = this.columns.map(col => col.label);
        const csvHeaders = headers.map(header => `"${String(header).replace(/"/g, '""')}"`);

        const rows = data.map(item =>
            this.columns.map(col => {
                let value = item[col.key] || '';

                // 对状态字段进行中文转换
                value = this.convertStatusToChineseForExport(col.key, value);

                // 处理特殊字符：引号、逗号、换行符
                const cleanValue = String(value)
                    .replace(/"/g, '""')  // 转义引号
                    .replace(/\r?\n/g, ' ')  // 替换换行符为空格
                    .replace(/\r/g, ' ');  // 替换回车符为空格
                return `"${cleanValue}"`;
            })
        );

        // 使用逗号分隔符，符合标准CSV格式
        return [csvHeaders.join(','), ...rows.map(row => row.join(','))].join('\r\n');
    }

    /**
     * 转换为制表符分隔格式（适合Excel粘贴）
     */
    convertToTabSeparated(data) {
        if (data.length === 0) return '';

        const headers = this.columns.map(col => col.label);

        const rows = data.map(item =>
            this.columns.map(col => {
                let value = item[col.key] || '';

                // 对状态字段进行中文转换
                value = this.convertStatusToChineseForExport(col.key, value);

                // 处理特殊字符：制表符、换行符
                const cleanValue = String(value)
                    .replace(/\t/g, ' ')     // 替换制表符为空格
                    .replace(/\r?\n/g, ' ')  // 替换换行符为空格
                    .replace(/\r/g, ' ');    // 替换回车符为空格
                return cleanValue;
            })
        );

        // 使用制表符分隔符，适合Excel自动分列
        return [headers.join('\t'), ...rows.map(row => row.join('\t'))].join('\r\n');
    }
    
    /**
     * 下载CSV文件
     */
    downloadCSV(data, filename) {
        const csvContent = this.convertToCSV(data);
        // 使用UTF-8 BOM确保中文正确显示，设置正确的MIME类型
        const blob = new Blob(['\ufeff' + csvContent], {
            type: 'text/csv;charset=utf-8;'
        });
        this.downloadBlob(blob, filename);
        this.showNotification(`成功导出 ${data.length} 条数据到CSV文件`, 'success');
    }
    
    /**
     * 下载Excel文件
     */
    downloadExcel(data, filename) {
        try {
            // 检查是否有XLSX库可用
            if (typeof XLSX !== 'undefined') {
                this.generateRealExcel(data, filename);
            } else {
                // 降级到Excel兼容的CSV格式
                this.generateExcelCompatibleCSV(data, filename);
            }
        } catch (error) {
            console.error('Excel导出失败:', error);
            // 降级到CSV导出
            this.generateExcelCompatibleCSV(data, filename);
        }
    }

    /**
     * 生成真正的Excel文件
     */
    generateRealExcel(data, filename) {
        // 准备工作表数据
        const headers = this.columns.map(col => col.label);
        const wsData = [headers];

        data.forEach(item => {
            const row = this.columns.map(col => {
                let value = item[col.key] || '';

                // 对状态字段进行中文转换
                value = this.convertStatusToChineseForExport(col.key, value);

                // 处理数字类型
                if (typeof value === 'number') {
                    return value;
                }
                // 处理日期类型
                if (value instanceof Date) {
                    return value.toISOString().slice(0, 10);
                }
                // 处理字符串类型
                return String(value);
            });
            wsData.push(row);
        });

        // 创建工作表
        const ws = XLSX.utils.aoa_to_sheet(wsData);

        // 设置列宽（根据内容自适应）
        const colWidths = this.columns.map((col) => {
            const maxLength = Math.max(
                col.label.length,
                ...data.map(item => String(item[col.key] || '').length)
            );
            return { wch: Math.min(Math.max(maxLength, 10), 50) };
        });
        ws['!cols'] = colWidths;

        // 设置表头样式
        const headerRange = XLSX.utils.decode_range(ws['!ref']);
        for (let col = headerRange.s.c; col <= headerRange.e.c; col++) {
            const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
            if (!ws[cellAddress]) continue;
            ws[cellAddress].s = {
                font: { bold: true },
                fill: { fgColor: { rgb: "EEEEEE" } },
                alignment: { horizontal: "center" }
            };
        }

        // 创建工作簿
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, this.moduleName || '数据');

        // 导出文件
        XLSX.writeFile(wb, filename);

        this.showNotification(`成功导出 ${data.length} 条数据到Excel文件`, 'success');
    }

    /**
     * 生成Excel兼容的CSV文件
     */
    generateExcelCompatibleCSV(data, filename) {
        const csvContent = this.convertToCSV(data);
        // 使用UTF-8 BOM确保中文在Excel中正确显示
        const blob = new Blob(['\ufeff' + csvContent], {
            type: 'application/vnd.ms-excel;charset=utf-8;'
        });

        // 使用.xls扩展名以便Excel自动识别
        const excelFilename = filename.replace('.xlsx', '.xls').replace('.csv', '.xls');
        this.downloadBlob(blob, excelFilename);

        this.showNotification(`成功导出 ${data.length} 条数据（Excel兼容格式）`, 'success');
    }
    
    /**
     * 下载Blob文件
     */
    downloadBlob(blob, filename) {
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }
    
    /**
     * 保存筛选状态
     */
    saveFilterState() {
        const state = {
            filters: this.filters,
            isCollapsed: this.isCollapsed
        };
        localStorage.setItem(this.options.storageKey, JSON.stringify(state));
    }

    /**
     * 加载筛选状态
     */
    loadFilterState() {
        try {
            const saved = localStorage.getItem(this.options.storageKey);
            if (saved) {
                const state = JSON.parse(saved);
                this.filters = state.filters || [];
                this.isCollapsed = state.isCollapsed !== false; // 默认折叠
            }
        } catch (error) {
            console.warn('加载筛选状态失败:', error);
        }
    }



    /**
     * 清理无效的选择状态（筛选后某些行可能不存在了）
     */
    cleanupInvalidSelections() {
        const validSelections = new Set();
        for (const rowIndex of this.selectedRows) {
            if (rowIndex < this.filteredData.length) {
                validSelections.add(rowIndex);
            }
        }
        this.selectedRows = validSelections;
        this.updateSelectionStats();
        // 更新视觉状态以确保同步
        this.updateAllRowSelectionUI();
    }

    /**
     * 绑定键盘快捷键
     */
    bindKeyboardShortcuts() {
        document.addEventListener('keydown', (event) => {
            // 只在数据表格区域处理快捷键
            if (!this.container || !this.container.contains(document.activeElement)) {
                return;
            }

            // Ctrl+A 或 Cmd+A：全选
            if ((event.ctrlKey || event.metaKey) && event.key === 'a') {
                event.preventDefault();
                this.selectAll();
            }

            // Escape：取消选择
            else if (event.key === 'Escape') {
                event.preventDefault();
                this.clearSelection();
            }

            // Ctrl+C 或 Cmd+C：复制选中数据
            else if ((event.ctrlKey || event.metaKey) && event.key === 'c') {
                event.preventDefault();
                this.copyData();
            }
        });
    }

    /**
     * 全选当前筛选的数据
     */
    selectAll() {
        this.selectedRows.clear();
        for (let i = 0; i < this.filteredData.length; i++) {
            this.selectedRows.add(i);
        }
        this.updateSelectionStats();
        this.updateAllRowSelectionUI();
        this.showNotification(`已选择全部${this.filteredData.length}条记录`, 'success');
    }

    /**
     * 清除所有选择
     */
    clearSelection() {
        if (this.selectedRows.size > 0) {
            const count = this.selectedRows.size;
            this.selectedRows.clear();
            this.lastSelectedIndex = null;
            this.updateSelectionStats();
            this.updateAllRowSelectionUI();
            this.showNotification(`已取消选择${count}条记录`, 'info');
        }
    }
    
    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        if (typeof authManager !== 'undefined' && authManager.showNotification) {
            authManager.showNotification(message, type);
        } else {
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }
    
    /**
     * HTML转义
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 生成带选择功能的表格HTML
     */
    generateSelectableTable(data, customRenderer = null) {
        if (!data || data.length === 0) {
            return `
                <div class="empty">
                    <div class="empty-img">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDEyOCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik02NCA5NkM4MC41Njg1IDk2IDk0IDgyLjU2ODUgOTQgNjZDOTQgNDkuNDMxNSA4MC41Njg1IDM2IDY0IDM2QzQ3LjQzMTUgMzYgMzQgNDkuNDMxNSAzNCA2NkMzNCA4Mi41Njg1IDQ3LjQzMTUgOTYgNjQgOTZaIiBzdHJva2U9IiNEQURERTIiIHN0cm9rZS13aWR0aD0iMiIvPgo8cGF0aCBkPSJNNTcuMzMzMyA2NkM1Ny4zMzMzIDYyLjMxODEgNjAuMzE4MSA1OS4zMzMzIDY0IDU5LjMzMzNDNjcuNjgxOSA1OS4zMzMzIDcwLjY2NjcgNjIuMzE4MSA3MC42NjY3IDY2QzcwLjY2NjcgNjkuNjgxOSA2Ny42ODE5IDcyLjY2NjcgNjQgNzIuNjY2N0M2MC4zMTgxIDcyLjY2NjcgNTcuMzMzMyA2OS42ODE5IDU3LjMzMzMgNjZaIiBmaWxsPSIjREFEREUyIi8+Cjwvc3ZnPgo=" alt="暂无数据">
                    </div>
                    <p class="empty-title">暂无数据</p>
                    <p class="empty-subtitle text-muted">请调整查询条件或筛选条件后重试</p>
                </div>
            `;
        }

        // 如果提供了自定义渲染器，使用自定义渲染器
        if (customRenderer && typeof customRenderer === 'function') {
            return customRenderer(data, this);
        }

        // 默认表格渲染
        let tableHTML = `
            <div class="table-responsive">
                <table class="table table-vcenter">
                    <thead>
                        <tr>
        `;

        // 添加列标题
        this.columns.forEach(col => {
            tableHTML += `<th>${this.escapeHtml(col.label)}</th>`;
        });

        tableHTML += `
                        </tr>
                    </thead>
                    <tbody>
        `;

        // 添加数据行
        data.forEach((item, index) => {
            const isSelected = this.selectedRows.has(index);
            tableHTML += `
                <tr data-row-index="${index}" class="${isSelected ? 'table-active' : ''}"
                    onclick="dataFilter.toggleRowSelection(${index}, event)"
                    style="cursor: pointer; user-select: none;">
            `;

            this.columns.forEach(col => {
                const value = item[col.key] || '';
                tableHTML += `<td>${this.escapeHtml(String(value))}</td>`;
            });

            tableHTML += `</tr>`;
        });

        tableHTML += `
                    </tbody>
                </table>
            </div>
        `;

        // 更新选择状态UI
        setTimeout(() => {
            this.updateAllRowSelectionUI();
        }, 0);

        return tableHTML;
    }
}

// 全局实例
let dataFilter = null;
