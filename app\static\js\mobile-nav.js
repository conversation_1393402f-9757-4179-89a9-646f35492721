/**
 * 移动端底部导航栏功能
 * 处理移动端子菜单显示、隐藏和触摸事件
 */

class MobileNavigation {
    constructor() {
        this.currentSubmenu = null;
        this.isSubmenuOpen = false;
        this.init();
    }

    init() {
        this.bindEvents();
        this.setupTouchEvents();
        console.log('移动端导航栏初始化完成');
    }

    bindEvents() {
        // 绑定子菜单触发器事件
        document.querySelectorAll('[data-submenu]').forEach(trigger => {
            // 移除默认的href行为
            trigger.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.toggleSubmenu(trigger.dataset.submenu);
            });

            // 添加触摸事件支持
            trigger.addEventListener('touchstart', (e) => {
                e.preventDefault();
                e.stopPropagation();
            });

            trigger.addEventListener('touchend', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.toggleSubmenu(trigger.dataset.submenu);
            });
        });

        // 绑定子菜单关闭按钮事件
        document.querySelectorAll('.mobile-submenu-close').forEach(closeBtn => {
            closeBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.closeSubmenu();
            });

            closeBtn.addEventListener('touchend', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.closeSubmenu();
            });
        });

        // 点击外部关闭子菜单
        document.addEventListener('click', (e) => {
            if (this.isSubmenuOpen && !this.isClickInsideSubmenu(e.target)) {
                e.preventDefault();
                this.closeSubmenu();
            }
        });

        // 触摸外部关闭子菜单 - 使用 touchend 而不是 touchstart
        document.addEventListener('touchend', (e) => {
            if (this.isSubmenuOpen && !this.isClickInsideSubmenu(e.target)) {
                e.preventDefault();
                this.closeSubmenu();
            }
        });

        // ESC键关闭子菜单
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isSubmenuOpen) {
                this.closeSubmenu();
            }
        });
    }

    setupTouchEvents() {
        // 为移动端优化触摸体验
        const navItems = document.querySelectorAll('.mobile-bottom-nav-item');
        navItems.forEach(item => {
            // 添加触摸反馈
            item.addEventListener('touchstart', () => {
                item.style.opacity = '0.7';
            });

            item.addEventListener('touchend', () => {
                item.style.opacity = '1';
            });

            item.addEventListener('touchcancel', () => {
                item.style.opacity = '1';
            });
        });
    }

    toggleSubmenu(submenuId) {
        const submenu = document.getElementById(`${submenuId}-submenu`);
        if (!submenu) {
            console.warn(`子菜单未找到: ${submenuId}-submenu`);
            return;
        }

        if (this.currentSubmenu === submenu && this.isSubmenuOpen) {
            this.closeSubmenu();
        } else {
            this.openSubmenu(submenu);
        }
    }

    openSubmenu(submenu) {
        // 先关闭当前打开的子菜单
        if (this.currentSubmenu && this.isSubmenuOpen) {
            this.closeSubmenu();
        }

        this.currentSubmenu = submenu;
        this.isSubmenuOpen = true;

        // 重置内联样式，确保CSS类能正常工作
        submenu.style.transform = '';
        submenu.style.opacity = '';
        submenu.style.visibility = '';

        // 防止页面滚动
        document.body.style.overflow = 'hidden';

        // 添加显示类，使用CSS过渡效果
        submenu.classList.add('show');

        console.log('子菜单已打开:', submenu.id);
    }

    closeSubmenu() {
        if (!this.currentSubmenu || !this.isSubmenuOpen) {
            return;
        }

        const submenu = this.currentSubmenu;

        // 移除显示类，触发CSS过渡动画
        submenu.classList.remove('show');

        // 清除所有内联样式，确保CSS类能正常工作
        submenu.style.transform = '';
        submenu.style.opacity = '';
        submenu.style.visibility = '';

        // 恢复页面滚动
        document.body.style.overflow = '';

        // 重置状态
        this.currentSubmenu = null;
        this.isSubmenuOpen = false;

        console.log('子菜单已关闭:', submenu.id);
    }

    isClickInsideSubmenu(target) {
        if (!this.currentSubmenu) {
            return false;
        }

        // 检查点击是否在当前打开的子菜单内
        const isInsideSubmenu = this.currentSubmenu.contains(target) ||
                               target.closest('.mobile-submenu') === this.currentSubmenu;

        // 检查是否点击了触发子菜单的按钮（避免重复触发）
        const isSubmenuTrigger = target.closest('[data-submenu]');

        // 如果点击的是子菜单内部或者是触发按钮，则认为是内部点击
        return isInsideSubmenu || isSubmenuTrigger;
    }

    // 检查是否为移动设备
    isMobileDevice() {
        return window.innerWidth <= 768 || 
               /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    // 获取当前打开的子菜单
    getCurrentSubmenu() {
        return this.currentSubmenu;
    }

    // 检查子菜单是否打开
    isSubmenuOpened() {
        return this.isSubmenuOpen;
    }
}

// 全局函数，供HTML模板调用
function closeMobileSubmenu() {
    if (window.mobileNav) {
        window.mobileNav.closeSubmenu();
    }
}

function openMobileSubmenu(submenuId) {
    if (window.mobileNav) {
        const submenu = document.getElementById(`${submenuId}-submenu`);
        if (submenu) {
            window.mobileNav.openSubmenu(submenu);
        }
    }
}

function toggleMobileSubmenu(submenuId) {
    if (window.mobileNav) {
        window.mobileNav.toggleSubmenu(submenuId);
    }
}

// 初始化移动端导航
document.addEventListener('DOMContentLoaded', function() {
    // 只在移动端初始化
    if (window.innerWidth <= 768) {
        window.mobileNav = new MobileNavigation();
    }
});

// 监听窗口大小变化
window.addEventListener('resize', function() {
    if (window.innerWidth <= 768) {
        // 切换到移动端
        if (!window.mobileNav) {
            window.mobileNav = new MobileNavigation();
        }
    } else {
        // 切换到桌面端，关闭子菜单
        if (window.mobileNav && window.mobileNav.isSubmenuOpened()) {
            window.mobileNav.closeSubmenu();
        }
    }
});
