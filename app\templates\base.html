<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <title>{% block title %}{{ app_name }}{% endblock %}</title>

    <!-- 基础 Meta 标签 -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon" />
    <link rel="shortcut icon" href="/static/favicon.ico" type="image/x-icon" />
	
    <!-- PWA Meta 标签 -->
    <meta name="theme-color" content="#206bc4">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="{{ app_name }}">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="application-name" content="{{ app_name }}">
    <meta name="msapplication-TileColor" content="#206bc4">
    <meta name="msapplication-tap-highlight" content="no">

    <!-- Web App Manifest -->
    <link rel="manifest" href="/static/manifest.json">

    <!-- Tabler CSS v1.3+1 -->
    <link href="/static/css/tabler.min.css" rel="stylesheet"/>
    <!-- Tabler Icons CSS -->
    <link href="/static/css/tabler-icons.min.css" rel="stylesheet"/>

    <!-- 自定义CSS -->
    <link href="/static/css/custom.css?ver={{ app_version }}" rel="stylesheet">

    <!-- PWA 安全区域适配 -->
    <style>
        :root {
            --safe-area-inset-top: env(safe-area-inset-top);
            --safe-area-inset-right: env(safe-area-inset-right);
            --safe-area-inset-bottom: env(safe-area-inset-bottom);
            --safe-area-inset-left: env(safe-area-inset-left);
            --navbar-height: 60px; /* 导航栏高度变量 */
        }

        /* 悬浮导航栏样式 */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1030;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            padding-top: calc(0.5rem + var(--safe-area-inset-top));
            padding-left: calc(1rem + var(--safe-area-inset-left));
            padding-right: calc(1rem + var(--safe-area-inset-right));
            min-height: var(--navbar-height);
        }


        /* 页面主体内容适配悬浮导航栏 */
        .page-wrapper {
            padding-top: calc(var(--navbar-height) + 1rem + var(--safe-area-inset-top));
            padding-bottom: calc(1rem + var(--safe-area-inset-bottom));
            padding-left: calc(1rem + var(--safe-area-inset-left));
            padding-right: calc(1rem + var(--safe-area-inset-right));
        }

        /* 移动端底部导航栏样式 */
        .mobile-bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 1050;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            padding-bottom: calc(0.5rem + var(--safe-area-inset-bottom));
            display: none;
        }

        .mobile-bottom-nav-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 0.25rem;
            text-decoration: none;
            color: #6c757d;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            min-height: 44px;
            position: relative;
            border-radius: 12px;
            margin: 0 0.25rem;
            touch-action: manipulation;
            -webkit-tap-highlight-color: transparent;
        }

        .mobile-bottom-nav-item:hover,
        .mobile-bottom-nav-item:focus,
        .mobile-bottom-nav-item:active {
            color: #206bc4;
            text-decoration: none;
            transform: translateY(-2px);
            background: rgba(32, 107, 196, 0.1);
        }

        .mobile-bottom-nav-item.active {
            color: #206bc4;
            background: rgba(32, 107, 196, 0.15);
            font-weight: 600;
        }

        .mobile-bottom-nav-item.active::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 24px;
            height: 2px;
            background: #206bc4;
            border-radius: 1px;
        }

        .mobile-bottom-nav-icon {
            width: 20px;
            height: 20px;
            margin-bottom: 0.25rem;
        }

        .mobile-bottom-nav-text {
            font-size: 0.75rem;
            font-weight: 500;
            line-height: 1;
        }

        .mobile-submenu {
            position: fixed;
            bottom: 61px;
            left: 0;
            right: 0;
            z-index: 1060;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 12px 12px 0 0;
            padding: 1rem;
            padding-bottom: calc(1rem + var(--safe-area-inset-bottom));
            transform: translateY(100%) !important;
            opacity: 0 !important;
            visibility: hidden !important;
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                       opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                       visibility 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
            max-height: 60vh;
            overflow-y: auto;
            will-change: transform, opacity, visibility;
        }

        .mobile-submenu.show {
            transform: translateY(0) !important;
            opacity: 1 !important;
            visibility: visible !important;
        }


        .mobile-submenu-item {
            display: flex;
            align-items: center;
            padding: 1rem 0.75rem;
            margin-bottom: 0.5rem;
            border-radius: 12px;
            text-decoration: none;
            color: #495057;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(32, 107, 196, 0.05);
            min-height: 44px;
            touch-action: manipulation;
            -webkit-tap-highlight-color: transparent;
            position: relative;
            overflow: hidden;
        }

        .mobile-submenu-item:hover,
        .mobile-submenu-item:focus,
        .mobile-submenu-item:active {
            background: rgba(32, 107, 196, 0.1);
            color: #206bc4;
            text-decoration: none;
            transform: translateX(4px);
        }

        .mobile-submenu-item.active {
            background: rgba(32, 107, 196, 0.15);
            color: #206bc4;
            font-weight: 600;
        }

        .mobile-submenu-item.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: #206bc4;
            border-radius: 0 2px 2px 0;
        }

        .mobile-submenu-icon {
            width: 18px;
            height: 18px;
            margin-right: 0.75rem;
            flex-shrink: 0;
        }

        .mobile-submenu-text {
            font-size: 0.9rem;
            font-weight: 500;
        }

        /* 移动端显示底部导航，隐藏顶部导航链接 */
        @media (max-width: 768px) {
            .mobile-bottom-nav {
                display: flex;
            }

            /* 隐藏汉堡菜单按钮，因为底部导航已接管页面导航功能 */
            .navbar-toggler {
                display: none !important;
            }

            /* 只隐藏页面导航链接，保留用户功能区域 */
            .navbar-collapse .navbar-nav {
                display: none !important;
            }

            /* 移动端顶部导航栏优化 */
            .navbar-brand {
                font-size: 1rem !important;
            }

            /* 移动端用户菜单按钮优化 */
            .navbar-nav.flex-row .nav-item .btn {
                font-size: 0.875rem;
                padding: 0.375rem 0.75rem;
            }


            /* 移动端用户头像菜单优化 */
            .navbar-nav.flex-row .nav-item .nav-link .d-none.d-xl-block {
                display: none !important;
            }

            .page-wrapper {
                padding-bottom: calc(80px + var(--safe-area-inset-bottom));
            }
        }


        /* 模态框在安全区域内显示 */
        .modal-dialog {
            margin-top: calc(1.75rem + var(--safe-area-inset-top));
            margin-bottom: calc(1.75rem + var(--safe-area-inset-bottom));
        }

        /* 通知容器适配安全区域 */
        #notification-container {
            top: calc(20px + var(--safe-area-inset-top));
            right: calc(20px + var(--safe-area-inset-right));
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <header class="navbar navbar-expand-md navbar-light d-print-none">
        <div class="container-fluid">
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-menu">
                <span class="navbar-toggler-icon"></span>
            </button>
            <h1 class="navbar-brand navbar-brand-autodark d-none-navbar-horizontal pe-0 pe-md-3">
                <a href="/">
                    {{ app_name }}
                </a>
            </h1>

            <!-- 导航菜单 -->
            <div class="collapse navbar-collapse" id="navbar-menu">
                <div class="d-flex flex-column flex-md-row flex-fill align-items-stretch align-items-md-center">
                    <ul class="navbar-nav">
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbar-modules" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M4 4m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"></path>
                                    <path d="M14 4m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"></path>
                                    <path d="M4 14m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"></path>
                                    <path d="M14 14m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"></path>
                                </svg>
                                平台数据
                            </a>
                            <div class="dropdown-menu">
                                <a class="dropdown-item {% if current_module == 'xqpz' %}active{% endif %}" href="/modules/xqpz">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon dropdown-item-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M14 3v4a1 1 0 0 0 1 1h4"></path>
                                        <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"></path>
                                        <path d="M9 9l1 0"></path>
                                        <path d="M9 13l6 0"></path>
                                        <path d="M9 17l6 0"></path>
                                    </svg>
                                    需求凭证
                                </a>
                                <a class="dropdown-item {% if current_module == 'xqd' %}active{% endif %}" href="/modules/xqd">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon dropdown-item-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2"></path>
                                        <path d="M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z"></path>
                                        <path d="M9 12l2 2l4 -4"></path>
                                    </svg>
                                    需求单
                                </a>
                                <a class="dropdown-item {% if current_module == 'product' %}active{% endif %}" href="/modules/product">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon dropdown-item-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M7 4v16l13 0v-16"></path>
                                        <path d="M7 4l4 0l0 4"></path>
                                        <path d="M11 4l4 0"></path>
                                        <path d="M15 4l0 4l-4 0"></path>
                                        <path d="M7 8l13 0"></path>
                                        <path d="M7 12l13 0"></path>
                                        <path d="M7 16l13 0"></path>
                                    </svg>
                                    商品状态
                                </a>
                                <a class="dropdown-item {% if current_module == 'brand' %}active{% endif %}" href="/modules/brand">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon dropdown-item-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M12 3l8 4.5l0 9l-8 4.5l-8 -4.5l0 -9l8 -4.5"></path>
                                        <path d="M12 12l8 -4.5"></path>
                                        <path d="M12 12l0 9"></path>
                                        <path d="M12 12l-8 -4.5"></path>
                                    </svg>
                                    品牌库
                                </a>

                            </div>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if current_module == 'scraper' %}active{% endif %}" href="/scraper">
                                <span class="nav-link-icon d-md-none d-lg-inline-block">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M4 4m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"></path>
                                        <path d="M14 4m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"></path>
                                        <path d="M4 14m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"></path>
                                        <path d="M14 17h6"></path>
                                        <path d="M17 14v6"></path>
                                    </svg>
                                </span>
                                <span class="nav-link-title">
                                    友商数据
                                </span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="navbar-nav flex-row order-md-last">
                <!-- 账号切换按钮组 - 始终可见 -->
                <div class="nav-item me-3">
                    <div class="btn-group" role="group">
                        <!-- 主切换账号按钮 -->
                        <button class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#login-modal" id="switch-account-btn">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-user-plus" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M8 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0"></path>
                                <path d="M16 19h6"></path>
                                <path d="M19 16v6"></path>
                                <path d="M6 21v-2a4 4 0 0 1 4 -4h4"></path>
                            </svg>
                            <span class="d-none d-sm-inline ms-1">切换账号</span>
                        </button>

                        <!-- 快速切换下拉按钮 -->
                        <button class="btn btn-outline-primary btn-sm dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false" id="quick-switch-btn">
                            <span class="visually-hidden">快速切换</span>
                        </button>

                        <!-- 快速切换下拉菜单 -->
                        <ul class="dropdown-menu dropdown-menu-end" id="quick-switch-dropdown">
                            <li>
                                <span class="dropdown-item-text text-muted">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0"></path>
                                        <path d="M12 10m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0"></path>
                                        <path d="M6.168 18.849a4 4 0 0 1 3.832 -2.849h4a4 4 0 0 1 3.834 2.855"></path>
                                    </svg>
                                    正在加载账号...
                                </span>
                            </li>
                        </ul>
                    </div>
                </div>
                
                <!-- 用户菜单 -->
                <div class="nav-item dropdown d-none" id="user-menu">
                    <a href="#" class="nav-link d-flex lh-1 text-reset p-0" data-bs-toggle="dropdown">
                        <span class="avatar avatar-sm text-white" id="user-avatar-container">
                            <span id="user-avatar">U</span>
                        </span>
                        <div class="d-none d-xl-block ps-2">
                            <div class="d-flex align-items-center">
                                <span id="user-name">账号</span>
                            </div>
                            <div class="mt-1 small text-muted" id="user-details">
                                <span id="service-username">商城用户</span> | <span id="electron-username">超市用户</span>
                            </div>
                        </div>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end dropdown-menu-arrow" style="min-width: 280px;">
                        <!-- 用户详细信息 -->
                        <div class="dropdown-item-text">
                            <div class="row g-2">
                                <div class="col-12">
                                    <div class="small text-muted">账号</div>
                                    <div class="fw-bold" id="dropdown-local-username">--</div>
                                </div>
                                <div class="col-6">
                                    <div class="small text-muted">商城权限</div>
                                    <div id="dropdown-service-username">--</div>
                                </div>
                                <div class="col-6">
                                    <div class="small text-muted">超市权限</div>
                                    <div id="dropdown-electron-username">--</div>
                                </div>
                                <div class="dropdown-divider"></div>
                                <div class="col-12">
                                    <div class="small text-muted">电子超市访问状态</div>
                                    <div id="electron-access-status">
                                        <span class="badge bg-secondary">未知</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#account-manager-modal">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon dropdown-item-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z"></path>
                                <path d="M9 12a3 3 0 1 0 6 0a3 3 0 0 0 -6 0"></path>
                            </svg>
                            管理保存的账号
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="dropdown-item" onclick="logout()">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon dropdown-item-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M14 8v-2a2 2 0 0 0 -2 -2h-7a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h7a2 2 0 0 0 2 -2v-2"></path>
                                <path d="M9 12h12l-3 -3m0 6l3 -3"></path>
                            </svg>
                            退出登录
                        </a>
                    </div>
                </div>
                
                <!-- 登录按钮 - 已隐藏，使用switch-account-btn作为唯一登录入口 -->
                <div class="nav-item d-none" id="login-btn">
                    <a href="#" class="nav-link" data-bs-toggle="modal" data-bs-target="#login-modal">
                        登录
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <div class="page-wrapper">
        {% block content %}{% endblock %}
    </div>

    <!-- 登录模态框 -->
    <div class="modal modal-blur fade" id="login-modal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">登录到电子超市</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="login-form">
                        <!-- 平台选择按钮组 -->
                        <div class="mb-4">
                            <label class="form-label">选择登录平台</label>
                            <div class="btn-group w-100" role="group" aria-label="平台选择">
                                <input type="radio" class="btn-check" name="platform" id="platform-aviation" value="aviation" required>
                                <label class="btn btn-outline-primary" for="platform-aviation">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M16 10h4a2 2 0 0 1 0 4h-4l-4 7h-3l2 -7h-4l-2 2h-3l2 -4l-2 -4h3l2 2h4l-2 -7h3z"></path>
                                    </svg>
                                    航空平台
                                </label>

                                <input type="radio" class="btn-check" name="platform" id="platform-engine" value="engine" required>
                                <label class="btn btn-outline-primary" for="platform-engine">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M3 10v6"></path>
                                        <path d="M12 5v3"></path>
                                        <path d="M12 15v3"></path>
                                        <path d="M5 7h2a1 1 0 0 1 1 1v1a1 1 0 0 0 1 1h6a1 1 0 0 0 1 -1v-1a1 1 0 0 1 1 -1h2"></path>
                                        <path d="M5 17h2a1 1 0 0 0 1 -1v-1a1 1 0 0 1 1 -1h6a1 1 0 0 1 1 1v1a1 1 0 0 0 1 1h2"></path>
                                        <path d="M21 10v6"></path>
                                    </svg>
                                    航发平台
                                </label>
                            </div>
                        </div>

                        <!-- 已保存账号选择 -->
                        <div class="mb-3" id="saved-accounts-section" style="display: none;">
                            <label class="form-label">选择已保存的账号</label>
                            <select class="form-select" id="saved-accounts-select">
                                <option value="">选择已保存的账号...</option>
                            </select>
                            <div class="form-text">选择已保存的账号将自动填充登录信息</div>
                        </div>

                        <!-- 用户名输入 -->
                        <div class="mb-3">
                            <label class="form-label">用户名</label>
                            <input type="text" class="form-control" name="username" placeholder="请输入用户名或选择已保存的账号" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">密码</label>
                            <div class="input-group">
                                <input type="password" class="form-control" name="password" required>
                                <button class="btn btn-outline-secondary" type="button" id="toggle-password">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"></path>
                                        <path d="M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-check">
                                <input type="checkbox" class="form-check-input" name="remember_password">
                                <span class="form-check-label">记住密码</span>
                                <span class="form-check-description">将账号和密码保存到本地浏览器</span>
                            </label>
                        </div>
                        <div class="form-footer">
                            <button type="submit" class="btn btn-primary w-100" id="login-submit-btn">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M14 8v-2a2 2 0 0 0 -2 -2h-7a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h7a2 2 0 0 0 2 -2v-2"></path>
                                    <path d="M9 12h12l-3 -3m0 6l3 -3"></path>
                                </svg>
                                登录
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 账号管理模态框 -->
    <div class="modal modal-blur fade" id="account-manager-modal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">管理保存的账号</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="mb-3">航空平台账号</h6>
                            <div id="aviation-accounts-list" class="account-list">
                                <div class="empty-state text-center py-4">
                                    <div class="empty-icon mb-3">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-user-off" width="48" height="48" viewBox="0 0 24 24" stroke-width="1" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                            <path d="M8.18 8.189a4.01 4.01 0 0 0 2.616 2.627m3.507 -.545a4 4 0 0 0 -5.59 -5.552"></path>
                                            <path d="M6 21v-2a4 4 0 0 1 4 -4h4c.412 0 .81 .062 1.183 .178m2.633 2.618c.12 .38 .184 .785 .184 1.204v2"></path>
                                            <path d="M3 3l18 18"></path>
                                        </svg>
                                    </div>
                                    <p class="text-muted">暂无保存的航空平台账号</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="mb-3">航发平台账号</h6>
                            <div id="engine-accounts-list" class="account-list">
                                <div class="empty-state text-center py-4">
                                    <div class="empty-icon mb-3">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-user-off" width="48" height="48" viewBox="0 0 24 24" stroke-width="1" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                            <path d="M8.18 8.189a4.01 4.01 0 0 0 2.616 2.627m3.507 -.545a4 4 0 0 0 -5.59 -5.552"></path>
                                            <path d="M6 21v-2a4 4 0 0 1 4 -4h4c.412 0 .81 .062 1.183 .178m2.633 2.618c.12 .38 .184 .785 .184 1.204v2"></path>
                                            <path d="M3 3l18 18"></path>
                                        </svg>
                                    </div>
                                    <p class="text-muted">暂无保存的航发平台账号</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-danger" onclick="clearAllSavedAccounts()">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M4 7l16 0"></path>
                            <path d="M10 11l0 6"></path>
                            <path d="M14 11l0 6"></path>
                            <path d="M5 7l1 -4l4 0l1 4"></path>
                            <path d="M9 7l6 0"></path>
                        </svg>
                        清空所有账号
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 通知容器 -->
    <div id="notification-container" style="position: fixed; top: 20px; right: 20px; z-index: 9999;"></div>

    <!-- 更新日志模态框 -->
    <div class="modal modal-blur fade" id="changelog-modal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
            <div class="modal-content changelog-modal-content">
                <!-- 固定头部 -->
                <div class="modal-header changelog-modal-header">
                    <h5 class="modal-title">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-file-text me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M14 3v4a1 1 0 0 0 1 1h4"></path>
                            <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"></path>
                            <path d="M9 9l1 0"></path>
                            <path d="M9 13l6 0"></path>
                            <path d="M9 17l6 0"></path>
                        </svg>
                        更新日志
                    </h5>
                    <button type="button" class="btn-close changelog-close-btn" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>

                <!-- 可滚动内容区域 -->
                <div class="modal-body changelog-modal-body">
                    <div class="changelog-scroll-container">
                        <div id="changelog-loading" class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <div class="mt-2">正在加载更新日志...</div>
                        </div>
                        <div id="changelog-content" class="d-none">
                            <!-- 更新日志内容将在这里动态加载 -->
                        </div>
                        <div id="changelog-error" class="d-none">
                            <div class="alert alert-danger">
                                <h4 class="alert-title">加载失败</h4>
                                <div class="text-muted">无法加载更新日志，请稍后重试。</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 固定底部 -->
                <div class="modal-footer changelog-modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 移动端底部导航栏 -->
    <nav class="mobile-bottom-nav">
        <!-- 仪表盘 -->
        <a href="/" class="mobile-bottom-nav-item {% if request.endpoint == 'dashboard' %}active{% endif %}">
            <svg class="mobile-bottom-nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                <path d="M4 4h6v8h-6z"></path>
                <path d="M4 16h6v4h-6z"></path>
                <path d="M14 12h6v8h-6z"></path>
                <path d="M14 4h6v4h-6z"></path>
            </svg>
            <span class="mobile-bottom-nav-text">仪表盘</span>
        </a>

        <!-- 友商数据 -->
        <a href="/scraper" class="mobile-bottom-nav-item {% if current_module == 'scraper' %}active{% endif %}">
            <svg class="mobile-bottom-nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                <path d="M4 4m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"></path>
                <path d="M14 4m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"></path>
                <path d="M4 14m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"></path>
                <path d="M14 17h6"></path>
                <path d="M17 14v6"></path>
            </svg>
            <span class="mobile-bottom-nav-text">友商数据</span>
        </a>

        <!-- 平台数据 (子菜单) -->
        <a href="#" class="mobile-bottom-nav-item {% if current_module in ['xqpz', 'xqd', 'product', 'brand'] %}active{% endif %}" data-submenu="platform-data">
            <svg class="mobile-bottom-nav-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                <path d="M4 4m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"></path>
                <path d="M14 4m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"></path>
                <path d="M4 14m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"></path>
                <path d="M14 14m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"></path>
            </svg>
            <span class="mobile-bottom-nav-text">平台数据</span>
        </a>

        <!-- 平台数据子菜单 -->
        <div class="mobile-submenu" id="platform-data-submenu">

            <a href="/modules/xqpz" class="mobile-submenu-item {% if current_module == 'xqpz' %}active{% endif %}">
                <svg class="mobile-submenu-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M14 3v4a1 1 0 0 0 1 1h4"></path>
                    <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"></path>
                    <path d="M9 9l1 0"></path>
                    <path d="M9 13l6 0"></path>
                    <path d="M9 17l6 0"></path>
                </svg>
                <span class="mobile-submenu-text">需求凭证</span>
            </a>

            <a href="/modules/xqd" class="mobile-submenu-item {% if current_module == 'xqd' %}active{% endif %}">
                <svg class="mobile-submenu-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2"></path>
                    <path d="M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z"></path>
                    <path d="M9 12l2 2l4 -4"></path>
                </svg>
                <span class="mobile-submenu-text">需求单</span>
            </a>

            <a href="/modules/product" class="mobile-submenu-item {% if current_module == 'product' %}active{% endif %}">
                <svg class="mobile-submenu-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M7 4v16l13 0v-16"></path>
                    <path d="M7 4l4 0l0 4"></path>
                    <path d="M11 4l4 0"></path>
                    <path d="M15 4l0 4l-4 0"></path>
                    <path d="M7 8l13 0"></path>
                    <path d="M7 12l13 0"></path>
                    <path d="M7 16l13 0"></path>
                </svg>
                <span class="mobile-submenu-text">商品状态</span>
            </a>

            <a href="/modules/brand" class="mobile-submenu-item {% if current_module == 'brand' %}active{% endif %}">
                <svg class="mobile-submenu-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M12 3l8 4.5l0 9l-8 4.5l-8 -4.5l0 -9l8 -4.5"></path>
                    <path d="M12 12l8 -4.5"></path>
                    <path d="M12 12l0 9"></path>
                    <path d="M12 12l-8 -4.5"></path>
                </svg>
                <span class="mobile-submenu-text">品牌库</span>
            </a>
        </div>
    </nav>

    <!-- 统一底部版权信息栏 -->
    {% include "footer.html" %}

    <!-- Tabler JS -->
    <script src="/static/js/tabler.min.js"></script>

    <!-- Chart.js - 使用UMD版本避免模块导入问题 -->
    <script src="/static/js/chart.min.js"></script>


    <!-- 自定义JS -->
    <script src="/static/js/account-manager.js?ver={{ app_version }}"></script>
    <script src="/static/js/auth.js?ver={{ app_version }}"></script>
    <script src="/static/js/changelog.js?ver={{ app_version }}"></script>
    <script src="/static/js/mobile-nav.js?ver={{ app_version }}"></script>
    <script src="/static/js/version-update-notification.js?ver={{ app_version }}"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
