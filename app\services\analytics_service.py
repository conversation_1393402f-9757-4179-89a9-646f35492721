"""
分析统计服务 - 重构版
处理查询日志记录、趋势分析和模块统计
支持SQLite数据库持久化存储，提供高性能的数据分析功能
"""

import asyncio
import aiohttp
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from collections import defaultdict
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, desc, case

from app.core.database import get_db_session, check_database_health
from app.models.analytics_models import QueryLog, ModuleStats, SystemHealth, AnalyticsConfig
from app.core.logging import logger


class AnalyticsService:
    """分析统计服务类 - 基于SQLite数据库的高性能版本"""

    def __init__(self):
        # 模块映射
        self.module_names = {
            'product': '商品状态',
            'xqpz': '需求凭证',
            'xqd': '需求单',
            'brand': '品牌库',
            'scraper': '友商数据'
        }

        # 友商数据采集子模块映射
        self.scraper_modules = {
            'scraper-colipu': '晨光',
            'scraper-officemate': '欧菲斯',
            'scraper-comix': '齐心',
            'scraper-xfs': '鑫方盛',
            'scraper-lxwl': '领先未来',
            'scraper-lxwl-new': '领先未来(新)',
            'scraper-xhgj': '新华国际'
        }

        # 模块API端点映射（用于健康检查）
        self.module_endpoints = {
            'product': '/api/v1/data/product/test',
            'xqpz': '/api/v1/data/xqpz/test',
            'xqd': '/api/v1/data/xqd/test',
            'brand': '/api/v1/data/brand/test'
        }

        # 实时状态缓存
        self.api_status_cache = {}
        self.last_status_check = {}

        # 初始化时检查数据库健康状态
        self._check_database_health()

    def _check_database_health(self):
        """检查数据库健康状态"""
        try:
            health_status = check_database_health()
            if health_status['status'] == 'healthy':
                logger.info(f"数据库连接正常 - 表数量: {health_status['table_count']}, 数据库大小: {health_status['database_size_mb']}MB")
            else:
                logger.error(f"数据库连接异常: {health_status.get('error', 'Unknown error')}")
        except Exception as e:
            logger.error(f"数据库健康检查失败: {str(e)}")

    def _is_permission_error(self, error_message: Optional[str], status_code: Optional[int]) -> bool:
        """
        判断是否为权限相关错误

        Args:
            error_message: 错误信息
            status_code: HTTP状态码

        Returns:
            bool: 是否为权限错误
        """
        if not error_message and not status_code:
            return False

        # 检查HTTP状态码
        if status_code in [401, 403]:
            return True

        # 检查错误信息中的权限关键词
        if error_message:
            error_lower = error_message.lower()
            permission_keywords = [
                "权限", "unauthorized", "forbidden", "access denied",
                "没有权限", "权限不足", "未授权", "token失效",
                "登录失效", "会话过期", "无权限", "权限错误",
                "需要电子超市平台访问权限", "缺少电子超市平台访问令牌",
                "authentication", "login required", "session expired",
                "invalid token", "token expired", "permission denied"
            ]

            return any(keyword in error_lower for keyword in permission_keywords)

        return False

    async def record_query_log(
        self,
        module: str,
        user: str,
        platform: str = "ELECTRON",
        success: bool = True,
        response_time: Optional[float] = None,
        status_code: Optional[int] = None,
        error_message: Optional[str] = None,
        client_ip: Optional[str] = None,
        user_agent: Optional[str] = None,
        query_params: Optional[Dict[str, Any]] = None,
        result_count: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        记录API查询操作日志到数据库

        Args:
            module: 模块名称
            user: 用户名
            platform: 平台名称
            success: 查询是否成功
            response_time: 响应时间(毫秒)
            status_code: HTTP状态码
            error_message: 错误信息
            client_ip: 客户端IP地址
            user_agent: 用户代理
            query_params: 查询参数
            result_count: 返回结果数量

        Returns:
            记录结果
        """
        try:
            now = datetime.now()

            # 生成唯一日志ID
            log_id = f"{module}_{now.strftime('%Y%m%d_%H%M%S_%f')}"

            # 过滤权限相关错误信息
            filtered_error_message = error_message
            if not success and self._is_permission_error(error_message, status_code):
                # 权限错误不记录详细错误信息，但保留统计数据
                filtered_error_message = None
                logger.debug(f"过滤权限错误信息 - 模块: {module}, 原始错误: {error_message}")

            # 创建查询日志记录
            log_entry = QueryLog(
                log_id=log_id,
                timestamp=now,
                module=module,
                user=user,
                platform=platform,
                success=success,
                response_time=response_time,
                status_code=status_code,
                error_message=filtered_error_message,
                client_ip=client_ip,
                user_agent=user_agent,
                query_params=query_params or {},
                result_count=result_count,
                hour=now.hour,
                date=now.date().isoformat(),
                week=now.isocalendar()[1],
                month=now.month,
                year=now.year,
                weekday=now.weekday()
            )

            # 保存到数据库
            with get_db_session() as db:
                db.add(log_entry)
                db.commit()

                # 获取插入的记录ID
                db.refresh(log_entry)
                record_id = log_entry.id

            logger.debug(f"记录API查询日志: {module} - {user} - 成功: {success}")

            return {
                'success': True,
                'message': 'API查询日志记录成功',
                'log_id': log_id,
                'record_id': record_id
            }

        except Exception as e:
            logger.error(f"记录查询日志失败: {str(e)}")
            return {
                'success': False,
                'message': f'记录查询日志失败: {str(e)}',
                'log_id': None,
                'record_id': None
            }

    async def get_hourly_trends(self, hours: int = 24, include_realtime: bool = False) -> Dict[str, Any]:
        """
        获取指定小时数的查询趋势数据

        Args:
            hours: 获取的小时数
            include_realtime: 是否包含实时API状态检查

        Returns:
            小时趋势数据
        """
        try:
            now = datetime.now()
            start_time = now - timedelta(hours=hours)

            with get_db_session() as db:
                # 查询指定时间范围内的数据
                query = db.query(
                    QueryLog.hour,
                    QueryLog.date,
                    func.count(QueryLog.id).label('total_count'),
                    func.sum(case((QueryLog.success == True, 1), else_=0)).label('success_count'),
                    func.avg(QueryLog.response_time).label('avg_response_time')
                ).filter(
                    QueryLog.timestamp >= start_time
                ).group_by(
                    QueryLog.date, QueryLog.hour
                ).order_by(
                    QueryLog.date, QueryLog.hour
                )

                results = query.all()

                # 初始化小时数据数组
                hourly_data = [0] * hours
                hourly_success = [0] * hours
                hourly_response_times = [0] * hours
                hour_labels = []

                # 生成小时标签
                for i in range(hours):
                    hour_time = start_time + timedelta(hours=i)
                    hour_labels.append(hour_time.strftime('%H:%M'))

                # 填充实际数据
                for result in results:
                    result_time = datetime.strptime(f"{result.date} {result.hour:02d}:00:00", "%Y-%m-%d %H:%M:%S")
                    if result_time >= start_time:
                        hours_diff = int((result_time - start_time).total_seconds() / 3600)
                        if 0 <= hours_diff < hours:
                            hourly_data[hours_diff] = result.total_count or 0
                            hourly_success[hours_diff] = result.success_count or 0
                            hourly_response_times[hours_diff] = result.avg_response_time or 0

                # 计算统计信息
                total_queries = sum(hourly_data)
                total_success = sum(hourly_success)
                success_rate = round((total_success / total_queries * 100) if total_queries > 0 else 100.0, 1)

                # 找出峰值时段
                peak_hour_index = hourly_data.index(max(hourly_data)) if hourly_data else 0
                peak_hour = hour_labels[peak_hour_index] if hour_labels else "00:00"

                # 平均响应时间
                valid_response_times = [rt for rt in hourly_response_times if rt > 0]
                avg_response_time = round(sum(valid_response_times) / len(valid_response_times), 2) if valid_response_times else 0

            return {
                'success': True,
                'data': {
                    'hourly_data': hourly_data,
                    'hourly_success': hourly_success,
                    'hourly_response_times': hourly_response_times,
                    'hour_labels': hour_labels,
                    'total_queries': total_queries,
                    'success_rate': success_rate,
                    'peak_hour': peak_hour,
                    'avg_response_time': avg_response_time,
                    'time_range': f'{hours}小时',
                    'start_time': start_time.isoformat(),
                    'end_time': now.isoformat(),
                    'data_source': 'SQLite数据库'
                }
            }

        except Exception as e:
            logger.error(f"获取小时趋势数据失败: {str(e)}")
            return {
                'success': False,
                'message': f'获取小时趋势数据失败: {str(e)}',
                'data': {}
            }

    async def get_weekly_module_stats(self, include_realtime: bool = False) -> Dict[str, Any]:
        """
        获取本周各模块API调用统计（基于数据库存储）

        Args:
            include_realtime: 是否包含实时API状态检查

        Returns:
            各模块API调用统计数据
        """
        try:
            now = datetime.now()
            current_week = now.isocalendar()[1]
            current_year = now.year
            last_week_date = now - timedelta(weeks=1)
            last_week = last_week_date.isocalendar()[1]
            last_week_year = last_week_date.year

            # 初始化统计数据
            module_stats = {}
            for module_key, module_name in self.module_names.items():
                module_stats[module_key] = {
                    'name': module_name,
                    'this_week': 0,
                    'last_week': 0,
                    'change': 0,
                    'change_percent': 0,
                    'success_rate': 100.0,
                    'total_calls': 0,
                    'failed_calls': 0,
                    'avg_response_time': 0
                }

            with get_db_session() as db:
                # 查询本周数据
                this_week_query = db.query(
                    QueryLog.module,
                    func.count(QueryLog.id).label('total_count'),
                    func.sum(case((QueryLog.success == True, 1), else_=0)).label('success_count'),
                    func.avg(QueryLog.response_time).label('avg_response_time')
                ).filter(
                    and_(QueryLog.week == current_week, QueryLog.year == current_year)
                ).group_by(QueryLog.module)

                this_week_results = this_week_query.all()

                # 查询上周数据
                last_week_query = db.query(
                    QueryLog.module,
                    func.count(QueryLog.id).label('total_count'),
                    func.sum(case((QueryLog.success == True, 1), else_=0)).label('success_count')
                ).filter(
                    and_(QueryLog.week == last_week, QueryLog.year == last_week_year)
                ).group_by(QueryLog.module)

                last_week_results = last_week_query.all()

                # 处理本周数据
                for result in this_week_results:
                    module = result.module
                    # 处理友商数据采集子模块，合并到主模块统计
                    target_module = module
                    if module in self.scraper_modules:
                        target_module = 'scraper'

                    if target_module in module_stats:
                        module_stats[target_module]['total_calls'] += result.total_count or 0
                        module_stats[target_module]['this_week'] += result.success_count or 0
                        module_stats[target_module]['failed_calls'] += (result.total_count or 0) - (result.success_count or 0)
                        if result.avg_response_time:
                            module_stats[target_module]['avg_response_time'] = result.avg_response_time

                # 处理上周数据
                for result in last_week_results:
                    module = result.module
                    target_module = module
                    if module in self.scraper_modules:
                        target_module = 'scraper'

                    if target_module in module_stats:
                        module_stats[target_module]['last_week'] += result.success_count or 0

                # 计算变化和成功率
                for module_key, stats in module_stats.items():
                    # 计算变化
                    stats['change'] = stats['this_week'] - stats['last_week']
                    if stats['last_week'] > 0:
                        stats['change_percent'] = round((stats['change'] / stats['last_week']) * 100, 1)
                    else:
                        stats['change_percent'] = 100.0 if stats['this_week'] > 0 else 0.0

                    # 计算成功率
                    if stats['total_calls'] > 0:
                        stats['success_rate'] = round((stats['this_week'] / stats['total_calls']) * 100, 1)

            # 计算总计
            total_this_week = sum(stats['this_week'] for stats in module_stats.values())
            total_last_week = sum(stats['last_week'] for stats in module_stats.values())
            total_api_calls = sum(stats['total_calls'] for stats in module_stats.values())

            return {
                'success': True,
                'data': {
                    'modules': module_stats,
                    'total_this_week': total_this_week,
                    'total_last_week': total_last_week,
                    'total_api_calls': total_api_calls,
                    'current_week': current_week,
                    'last_week': last_week,
                    'last_updated': now.isoformat(),
                    'data_source': 'SQLite数据库'
                }
            }

        except Exception as e:
            logger.error(f"获取模块统计数据失败: {str(e)}")
            return {
                'success': False,
                'message': f'获取模块统计数据失败: {str(e)}',
                'data': {}
            }

    async def get_realtime_dashboard_data(self) -> Dict[str, Any]:
        """
        获取实时仪表板数据（综合统计信息）

        Returns:
            包含趋势、模块统计和实时状态的综合数据
        """
        try:
            # 并行获取趋势数据和模块统计
            trends_task = self.get_hourly_trends(24, include_realtime=True)
            stats_task = self.get_weekly_module_stats(include_realtime=True)

            trends_result, stats_result = await asyncio.gather(trends_task, stats_task)

            # 计算系统整体健康状态
            system_health = self._calculate_system_health(trends_result, stats_result)

            return {
                'success': True,
                'data': {
                    'trends': trends_result['data'] if trends_result['success'] else {},
                    'module_stats': stats_result['data'] if stats_result['success'] else {},
                    'system_health': system_health,
                    'generated_at': datetime.now().isoformat()
                }
            }

        except Exception as e:
            logger.error(f"获取实时仪表板数据失败: {str(e)}")
            return {
                'success': False,
                'message': f'获取实时仪表板数据失败: {str(e)}',
                'data': {}
            }

    def _calculate_system_health(self, trends_result: Dict, stats_result: Dict) -> Dict[str, Any]:
        """计算系统整体健康状态"""
        try:
            health_score = 100
            issues = []

            # 检查趋势数据
            if trends_result.get('success') and trends_result.get('data'):
                trends_data = trends_result['data']
                success_rate = trends_data.get('success_rate', 100)
                avg_response_time = trends_data.get('avg_response_time', 0)

                # 成功率检查
                if success_rate < 95:
                    health_score -= 20
                    issues.append(f"成功率偏低: {success_rate}%")
                elif success_rate < 98:
                    health_score -= 10
                    issues.append(f"成功率需关注: {success_rate}%")

                # 响应时间检查
                if avg_response_time > 3000:
                    health_score -= 15
                    issues.append(f"响应时间过长: {avg_response_time}ms")
                elif avg_response_time > 1000:
                    health_score -= 5
                    issues.append(f"响应时间偏高: {avg_response_time}ms")

            # 检查模块统计
            if stats_result.get('success') and stats_result.get('data'):
                stats_data = stats_result['data']
                modules = stats_data.get('modules', {})

                for module_key, module_stats in modules.items():
                    module_success_rate = module_stats.get('success_rate', 100)
                    if module_success_rate < 90:
                        health_score -= 10
                        issues.append(f"{module_stats.get('name', module_key)}模块成功率低: {module_success_rate}%")

            # 确定健康状态
            if health_score >= 90:
                status = 'excellent'
                status_text = '优秀'
                color = 'success'
            elif health_score >= 75:
                status = 'good'
                status_text = '良好'
                color = 'info'
            elif health_score >= 60:
                status = 'warning'
                status_text = '警告'
                color = 'warning'
            else:
                status = 'critical'
                status_text = '严重'
                color = 'danger'

            return {
                'score': max(0, health_score),
                'status': status,
                'status_text': status_text,
                'color': color,
                'issues': issues,
                'last_check': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"计算系统健康状态失败: {str(e)}")
            return {
                'score': 0,
                'status': 'unknown',
                'status_text': '未知',
                'color': 'secondary',
                'issues': ['健康状态计算失败'],
                'last_check': datetime.now().isoformat()
            }

    async def get_module_detail_logs(
        self,
        module: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        status_filter: Optional[str] = None,
        search_term: Optional[str] = None,
        page: int = 1,
        page_size: int = 50
    ) -> Dict[str, Any]:
        """
        获取模块详细日志记录

        Args:
            module: 模块名称
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            status_filter: 状态筛选 ('success', 'failed', 'all')
            search_term: 搜索关键词
            page: 页码
            page_size: 每页大小

        Returns:
            详细日志数据
        """
        try:
            # 设置默认时间范围（最近7天）
            if not start_date:
                start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
            if not end_date:
                end_date = datetime.now().strftime('%Y-%m-%d')

            # 计算偏移量
            offset = (page - 1) * page_size

            with get_db_session() as db:
                # 构建基础查询
                query = db.query(QueryLog).filter(
                    QueryLog.date >= start_date,
                    QueryLog.date <= end_date
                )

                # 模块筛选（支持友商数据采集子模块）
                if module == 'scraper':
                    # 友商数据采集模块，包含所有子模块
                    scraper_modules = list(self.scraper_modules.keys())
                    query = query.filter(QueryLog.module.in_(scraper_modules))
                else:
                    query = query.filter(QueryLog.module == module)

                # 状态筛选
                if status_filter == 'success':
                    query = query.filter(QueryLog.success == True)
                elif status_filter == 'failed':
                    query = query.filter(QueryLog.success == False)

                # 搜索筛选
                if search_term:
                    search_pattern = f"%{search_term}%"
                    query = query.filter(
                        or_(
                            QueryLog.user.ilike(search_pattern),
                            QueryLog.client_ip.ilike(search_pattern),
                            QueryLog.error_message.ilike(search_pattern),
                            func.json_extract(QueryLog.query_params, '$.sku_list').ilike(search_pattern)
                        )
                    )

                # 获取总数
                total_count = query.count()

                # 分页查询
                logs = query.order_by(desc(QueryLog.timestamp)).offset(offset).limit(page_size).all()

                # 转换为字典格式
                log_data = []
                for log in logs:
                    log_dict = log.to_dict()
                    # 添加模块显示名称
                    if log.module in self.scraper_modules:
                        log_dict['module_display_name'] = self.scraper_modules[log.module]
                    else:
                        log_dict['module_display_name'] = self.module_names.get(log.module, log.module)

                    # 处理查询参数显示
                    if log.query_params:
                        sku_list = log.query_params.get('sku_list', [])
                        sku_count = log.query_params.get('sku_count', 0)
                        if sku_list:
                            log_dict['query_summary'] = f"查询{sku_count}个SKU: {', '.join(sku_list[:3])}{'...' if len(sku_list) > 3 else ''}"
                        else:
                            log_dict['query_summary'] = "无查询参数"
                    else:
                        log_dict['query_summary'] = "无查询参数"

                    log_data.append(log_dict)

                # 计算分页信息
                total_pages = (total_count + page_size - 1) // page_size

                return {
                    'success': True,
                    'data': {
                        'logs': log_data,
                        'pagination': {
                            'current_page': page,
                            'page_size': page_size,
                            'total_count': total_count,
                            'total_pages': total_pages,
                            'has_next': page < total_pages,
                            'has_prev': page > 1
                        },
                        'filters': {
                            'module': module,
                            'start_date': start_date,
                            'end_date': end_date,
                            'status_filter': status_filter,
                            'search_term': search_term
                        },
                        'module_display_name': self.scraper_modules.get(module) or self.module_names.get(module, module)
                    }
                }

        except Exception as e:
            logger.error(f"获取模块详细日志失败: {str(e)}")
            return {
                'success': False,
                'message': f'获取模块详细日志失败: {str(e)}',
                'data': {}
            }

    async def get_module_summary_stats(
        self,
        module: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取模块汇总统计信息

        Args:
            module: 模块名称
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)

        Returns:
            模块汇总统计数据
        """
        try:
            # 设置默认时间范围（最近7天）
            if not start_date:
                start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
            if not end_date:
                end_date = datetime.now().strftime('%Y-%m-%d')

            with get_db_session() as db:
                # 构建基础查询
                query = db.query(QueryLog).filter(
                    QueryLog.date >= start_date,
                    QueryLog.date <= end_date
                )

                # 模块筛选
                if module == 'scraper':
                    scraper_modules = list(self.scraper_modules.keys())
                    query = query.filter(QueryLog.module.in_(scraper_modules))
                else:
                    query = query.filter(QueryLog.module == module)

                # 总体统计
                total_requests = query.count()
                success_requests = query.filter(QueryLog.success == True).count()
                failed_requests = total_requests - success_requests
                success_rate = round((success_requests / total_requests * 100) if total_requests > 0 else 100.0, 2)

                # 响应时间统计
                response_time_stats = query.filter(QueryLog.response_time.isnot(None)).with_entities(
                    func.avg(QueryLog.response_time).label('avg_time'),
                    func.min(QueryLog.response_time).label('min_time'),
                    func.max(QueryLog.response_time).label('max_time')
                ).first()

                avg_response_time = round(response_time_stats.avg_time, 2) if response_time_stats.avg_time else 0
                min_response_time = round(response_time_stats.min_time, 2) if response_time_stats.min_time else 0
                max_response_time = round(response_time_stats.max_time, 2) if response_time_stats.max_time else 0

                # 用户统计
                unique_users = query.with_entities(func.count(func.distinct(QueryLog.user))).scalar() or 0
                unique_ips = query.filter(QueryLog.client_ip.isnot(None)).with_entities(
                    func.count(func.distinct(QueryLog.client_ip))
                ).scalar() or 0

                # 每日趋势
                daily_stats = query.with_entities(
                    QueryLog.date,
                    func.count(QueryLog.id).label('total'),
                    func.sum(case((QueryLog.success == True, 1), else_=0)).label('success')
                ).group_by(QueryLog.date).order_by(QueryLog.date).all()

                daily_trends = []
                for stat in daily_stats:
                    daily_trends.append({
                        'date': stat.date,
                        'total': stat.total,
                        'success': stat.success,
                        'failed': stat.total - stat.success,
                        'success_rate': round((stat.success / stat.total * 100) if stat.total > 0 else 100.0, 2)
                    })

                # 错误分析（最近的失败记录）
                recent_errors = query.filter(QueryLog.success == False).order_by(
                    desc(QueryLog.timestamp)
                ).limit(10).all()

                error_analysis = []
                for error in recent_errors:
                    error_analysis.append({
                        'timestamp': error.timestamp.isoformat(),
                        'error_message': error.error_message,
                        'status_code': error.status_code,
                        'user': error.user,
                        'client_ip': error.client_ip
                    })

                return {
                    'success': True,
                    'data': {
                        'module': module,
                        'module_display_name': self.scraper_modules.get(module) or self.module_names.get(module, module),
                        'date_range': {
                            'start_date': start_date,
                            'end_date': end_date
                        },
                        'summary': {
                            'total_requests': total_requests,
                            'success_requests': success_requests,
                            'failed_requests': failed_requests,
                            'success_rate': success_rate,
                            'unique_users': unique_users,
                            'unique_ips': unique_ips
                        },
                        'performance': {
                            'avg_response_time': avg_response_time,
                            'min_response_time': min_response_time,
                            'max_response_time': max_response_time
                        },
                        'daily_trends': daily_trends,
                        'recent_errors': error_analysis
                    }
                }

        except Exception as e:
            logger.error(f"获取模块汇总统计失败: {str(e)}")
            return {
                'success': False,
                'message': f'获取模块汇总统计失败: {str(e)}',
                'data': {}
            }

    async def create_test_data(self) -> Dict[str, Any]:
        """
        创建测试数据，用于验证响应时间和错误信息显示
        """
        try:
            now = datetime.now()
            test_logs = [
                # 快速响应（绿色）
                {
                    'module': 'product',
                    'user': 'test_user_fast',
                    'success': True,
                    'response_time': 500.0,
                    'error_message': None,
                    'query_params': {'sku_list': ['TEST001', 'TEST002'], 'sku_count': 2}
                },
                # 中等响应（黄色）
                {
                    'module': 'product',
                    'user': 'test_user_medium',
                    'success': True,
                    'response_time': 2000.0,
                    'error_message': None,
                    'query_params': {'sku_list': ['TEST003'], 'sku_count': 1}
                },
                # 慢响应（红色）
                {
                    'module': 'product',
                    'user': 'test_user_slow',
                    'success': True,
                    'response_time': 5000.0,
                    'error_message': None,
                    'query_params': {'sku_list': ['TEST004', 'TEST005', 'TEST006'], 'sku_count': 3}
                },
                # 失败请求（有错误信息）
                {
                    'module': 'product',
                    'user': 'test_user_error',
                    'success': False,
                    'response_time': 1500.0,
                    'error_message': '网络连接超时，请检查网络设置',
                    'status_code': 500,
                    'query_params': {'sku_list': ['ERROR001'], 'sku_count': 1}
                },
                # 另一个失败请求
                {
                    'module': 'product',
                    'user': 'test_user_error2',
                    'success': False,
                    'response_time': 3500.0,
                    'error_message': 'API接口返回错误：商品信息不存在',
                    'status_code': 404,
                    'query_params': {'sku_list': ['ERROR002', 'ERROR003'], 'sku_count': 2}
                },
                # 权限错误测试（这些错误信息应该被过滤）
                {
                    'module': 'xqd',
                    'user': 'test_user_permission1',
                    'success': False,
                    'response_time': 500.0,
                    'error_message': '没有权限访问该资源',
                    'status_code': 403,
                    'query_params': {'test': 'permission_error', 'sku_count': 1}
                },
                {
                    'module': 'brand',
                    'user': 'test_user_permission2',
                    'success': False,
                    'response_time': 300.0,
                    'error_message': '需要电子超市平台访问权限',
                    'status_code': 401,
                    'query_params': {'test': 'electron_permission_error', 'sku_count': 1}
                },
                {
                    'module': 'product',
                    'user': 'test_user_permission3',
                    'success': False,
                    'response_time': 400.0,
                    'error_message': 'Unauthorized access',
                    'status_code': 401,
                    'query_params': {'test': 'unauthorized_error', 'sku_count': 1}
                }
            ]

            with get_db_session() as db:
                for i, log_data in enumerate(test_logs):
                    # 创建时间稍微错开
                    timestamp = now - timedelta(minutes=i * 5)

                    log_entry = QueryLog(
                        log_id=f"test_{timestamp.strftime('%Y%m%d_%H%M%S')}_{i}",
                        timestamp=timestamp,
                        module=log_data['module'],
                        user=log_data['user'],
                        platform='ELECTRON',
                        success=log_data['success'],
                        response_time=log_data['response_time'],
                        status_code=log_data.get('status_code', 200),
                        error_message=log_data['error_message'],
                        client_ip='127.0.0.1',
                        user_agent='Test User Agent',
                        query_params=log_data['query_params'],
                        result_count=log_data['query_params']['sku_count'],
                        hour=timestamp.hour,
                        date=timestamp.date().isoformat(),
                        week=timestamp.isocalendar()[1],
                        month=timestamp.month,
                        year=timestamp.year,
                        weekday=timestamp.weekday()
                    )

                    db.add(log_entry)

                db.commit()

            return {
                'success': True,
                'message': f'成功创建{len(test_logs)}条测试数据',
                'count': len(test_logs)
            }

        except Exception as e:
            logger.error(f"创建测试数据失败: {str(e)}")
            return {
                'success': False,
                'message': f'创建测试数据失败: {str(e)}',
                'count': 0
            }


# 创建全局实例
analytics_service = AnalyticsService()