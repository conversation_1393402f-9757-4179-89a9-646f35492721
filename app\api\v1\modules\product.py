"""
商品状态查询API路由
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
import time
from app.core.auth import get_current_user
from app.services.modules.product_service import product_status_service
from app.services.analytics_service import analytics_service
from app.core.logging import logger


router = APIRouter()


class ProductStatusQueryRequest(BaseModel):
    """商品状态查询请求模型"""
    name: Optional[str] = Field(None, description="商品名称，支持模糊搜索")
    sku: Optional[str] = Field(None, description="商品SKU编码，支持多个编码用空格分隔")
    shelf_status: Optional[str] = Field(None, description="上架状态：UP_SHELF(上架)/DOWN_SHELF(下架)")
    delist_type: Optional[str] = Field(None, description="下架类型：OPERATE_DELIST(运营下架)/SYSTEM_DELIST(系统下架)/OPERATE_DELIST_FOREVER(永久下架)/SUPPLIER_DELIST(供应商下架)")
    approval_status: Optional[str] = Field(None, description="审核状态：APPROVED(审核通过)/DRAFT(待审核)")


class ProductStatusQueryResponse(BaseModel):
    """商品状态查询响应模型"""
    success: bool = Field(..., description="查询是否成功")
    message: str = Field(..., description="响应消息")
    data: list = Field(..., description="商品数据列表")
    total: Optional[str] = Field(None, description="总记录数")
    permission_error: Optional[bool] = Field(None, description="是否为权限相关错误")


@router.post("/product-integrated", response_model=ProductStatusQueryResponse)
async def query_product_integrated(
    request: ProductStatusQueryRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    智能整合查询商品状态和审核状态

    自动执行两阶段查询策略：
    1. 第一阶段：商品状态批量查询
    2. 第二阶段：审核状态补充查询
    3. 第三阶段：数据整合与排序

    返回完整的11字段数据：SKU编码、商品名称、商品品目、售价、上架状态、
    下架类型、下架原因、审核状态、驳回原因、操作员、更新时间
    """
    try:
        logger.info(f"智能整合查询开始 - 用户: {current_user.get('username')}")

        # 检查用户权限
        if not current_user.get('electron_access'):
            logger.warning(f"用户 {current_user.get('username')} 缺少ELECTRON权限")
            raise HTTPException(
                status_code=403,
                detail="需要电子超市平台权限才能进行商品状态查询"
            )

        # 获取访问令牌
        electron_token = current_user.get('electron_token')
        if not electron_token:
            logger.warning(f"用户 {current_user.get('username')} 缺少ELECTRON访问令牌")
            raise HTTPException(
                status_code=401,
                detail="缺少有效的电子超市访问令牌，请重新登录"
            )

        # 验证查询参数
        if not request.name and not request.sku:
            raise HTTPException(
                status_code=400,
                detail="请提供商品名称或SKU编码"
            )

        # 调用服务层执行智能整合查询
        result = await product_status_service.query_product_integrated(
            access_token=electron_token,
            client_ip=current_user.get('client_ip'),
            product_name=request.name,
            product_sku=request.sku,
            shelf_status=request.shelf_status,
            delist_type=request.delist_type,
            approval_status=request.approval_status
        )

        logger.info(f"智能整合查询完成 - 用户: {current_user.get('username')}, 结果: {result.get('success')}")

        # 记录查询统计（异步，不影响响应）
        try:
            # 安全获取结果数量，确保数据类型正确
            data = result.get('data', [])
            if isinstance(data, list):
                result_count = len(data)
            elif isinstance(data, dict):
                # 如果data是字典，尝试获取results字段
                result_count = len(data.get('results', []))
            else:
                result_count = 0

            await analytics_service.record_query_log(
                module='product',
                user=current_user.get('username', 'unknown'),
                platform=current_user.get('platform', 'ELECTRON'),
                success=result.get('success', False),
                response_time=result.get('response_time'),
                status_code=200 if result.get('success') else 500,
                error_message=result.get('message') if not result.get('success') else None,
                query_params={
                    'name': request.name,
                    'sku': request.sku,
                    'shelf_status': request.shelf_status,
                    'delist_type': request.delist_type,
                    'approval_status': request.approval_status,
                    'query_type': 'integrated'
                },
                result_count=result_count
            )
        except Exception as e:
            logger.warning(f"记录智能整合查询统计失败: {str(e)}")

        return ProductStatusQueryResponse(
            success=result['success'],
            message=result['message'],
            data=result['data'],
            total=result.get('total'),
            permission_error=result.get('permission_error')
        )

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"智能整合查询API异常: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"查询过程中发生内部错误: {str(e)}"
        )


class SingleSkuQueryRequest(BaseModel):
    """单个SKU查询请求模型"""
    sku: str = Field(..., description="商品SKU编码")


class ProductDetailRequest(BaseModel):
    """商品详情查询请求模型"""
    id: str = Field(..., description="商品ID")


class ProductDetailResponse(BaseModel):
    """商品详情查询响应模型"""
    success: bool = Field(..., description="查询是否成功")
    message: str = Field(..., description="响应消息")
    data: dict = Field(default_factory=dict, description="商品详情数据")
    permission_error: Optional[bool] = Field(None, description="是否为权限相关错误")


@router.post("/product/query", response_model=ProductStatusQueryResponse)
async def query_single_product_by_sku(
    request: SingleSkuQueryRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    根据SKU查询单个商品状态

    用于需求凭证模块的商品状态查看功能
    需要ELECTRON平台访问权限
    """
    start_time = time.time()

    try:
        logger.info(f"用户 {current_user.get('username')} 请求单个商品状态查询 - SKU: {request.sku}")

        # 检查用户是否有ELECTRON访问权限
        if not current_user.get('electron_access'):
            logger.warning(f"用户 {current_user.get('username')} 没有ELECTRON访问权限")
            raise HTTPException(
                status_code=403,
                detail="需要电子超市平台访问权限才能查询商品状态"
            )

        # 获取ELECTRON访问令牌
        electron_token = current_user.get('electron_token')
        if not electron_token:
            logger.warning(f"用户 {current_user.get('username')} 缺少ELECTRON访问令牌")
            raise HTTPException(
                status_code=401,
                detail="缺少电子超市平台访问令牌"
            )

        # 验证SKU参数
        if not request.sku.strip():
            raise HTTPException(
                status_code=400,
                detail="SKU编码不能为空"
            )

        # 执行单个商品状态查询
        result = await product_status_service.query_product_status(
            access_token=electron_token,
            client_ip=current_user.get('client_ip'),
            product_sku=request.sku.strip()
        )

        logger.info(f"单个商品状态查询完成 - 用户: {current_user.get('username')}, SKU: {request.sku}, 结果: {result.get('success')}")

        # 记录查询统计（异步，不影响响应）
        try:
            # 计算响应时间
            response_time = (time.time() - start_time) * 1000

            await analytics_service.record_query_log(
                module='product',
                user=current_user.get('username', 'unknown'),
                platform=current_user.get('platform', 'ELECTRON'),
                success=result.get('success', False),
                response_time=response_time,
                status_code=200 if result.get('success') else 500,
                error_message=result.get('message') if not result.get('success') else None,
                query_params={
                    'sku': request.sku,
                    'query_type': 'single_sku'
                },
                result_count=1 if result.get('success') and result.get('data') else 0
            )
        except Exception as e:
            logger.warning(f"记录单个商品状态查询统计失败: {str(e)}")

        return ProductStatusQueryResponse(
            success=result['success'],
            message=result['message'],
            data=result['data'],
            total=result.get('total'),
            permission_error=result.get('permission_error')
        )

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"单个商品状态查询API异常: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"查询过程中发生内部错误: {str(e)}"
        )


@router.post("/product/detail", response_model=ProductDetailResponse)
async def get_product_detail(
    request: ProductDetailRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取商品详情信息

    需要ELECTRON平台访问权限
    """
    start_time = time.time()

    try:
        logger.info(f"用户 {current_user.get('username')} 请求商品详情 - ID: {request.id}")

        # 检查用户是否有ELECTRON访问权限
        if not current_user.get('electron_access'):
            logger.warning(f"用户 {current_user.get('username')} 没有ELECTRON访问权限")
            raise HTTPException(
                status_code=403,
                detail="需要电子超市平台访问权限才能查看商品详情"
            )

        # 获取ELECTRON访问令牌
        electron_token = current_user.get('electron_token')
        if not electron_token:
            logger.warning(f"用户 {current_user.get('username')} 缺少ELECTRON访问令牌")
            raise HTTPException(
                status_code=401,
                detail="缺少电子超市平台访问令牌"
            )

        # 验证商品ID参数
        if not request.id.strip():
            raise HTTPException(
                status_code=400,
                detail="商品ID不能为空"
            )

        # 执行商品详情查询
        result = await product_status_service.get_product_detail(
            access_token=electron_token,
            client_ip=current_user.get('client_ip'),
            product_id=request.id.strip()
        )

        logger.info(f"商品详情查询完成 - 用户: {current_user.get('username')}, ID: {request.id}, 结果: {result.get('success')}")

        # 记录查询统计（异步，不影响响应）
        try:
            # 计算响应时间
            response_time = (time.time() - start_time) * 1000

            await analytics_service.record_query_log(
                module='product',
                user=current_user.get('username', 'unknown'),
                platform=current_user.get('platform', 'ELECTRON'),
                success=result.get('success', False),
                response_time=response_time,
                status_code=200 if result.get('success') else 500,
                error_message=result.get('message') if not result.get('success') else None,
                query_params={
                    'product_id': request.id,
                    'query_type': 'product_detail'
                },
                result_count=1 if result.get('success') and result.get('data') else 0
            )
        except Exception as e:
            logger.warning(f"记录商品详情查询统计失败: {str(e)}")

        return ProductDetailResponse(
            success=result['success'],
            message=result['message'],
            data=result['data'],
            permission_error=result.get('permission_error')
        )

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"商品详情查询API异常: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"查询过程中发生内部错误: {str(e)}"
        )

