"""
商品状态查询服务
负责与电子超市商品管理API的对接
"""

import aiohttp
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from app.core.config import settings
from app.core.logging import logger
from app.services.base_electron_service import BaseElectronService


class ProductStatusService(BaseElectronService):
    """商品状态查询服务类"""
    
    def __init__(self):
        super().__init__()
        # 商品状态查询API端点
        self.api_url = "https://eshop.eavic.com/api/commodity/supplier/product_management/list"
        # 审核状态查询API端点
        self.approval_api_url = "https://eshop.eavic.com/api/commodity/supplier/product_management/approval_list"
        # 审核详情API端点
        self.approval_detail_url = "https://eshop.eavic.com/api/commodity/supplier/product_management/approval_detail"
        # 商品详情API端点 - 主要端点（已审核商品详情）
        self.product_detail_url = "https://eshop.eavic.com/api/commodity/supplier/product_management/approved/basic_detail"
        # 商品详情API端点 - 备用端点（基础商品详情）
        self.product_detail_fallback_url = "https://eshop.eavic.com/api/commodity/supplier/product_management/basic_detail"
    
    async def query_product_status(
        self,
        access_token: str,
        client_ip: Optional[str] = None,
        product_name: Optional[str] = None,
        product_sku: Optional[str] = None,
        shelf_status: Optional[str] = None,
        delist_type: Optional[str] = None,
        approval_status: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        查询商品状态（基础功能）

        Args:
            access_token: ELECTRON平台访问令牌
            product_name: 商品名称（可选）
            product_sku: 商品SKU编码（可选）
            shelf_status: 上架状态（可选）- UP_SHELF/DOWN_SHELF
            delist_type: 下架类型（可选）- OPERATE_DELIST/SYSTEM_DELIST/OPERATE_DELIST_FOREVER/SUPPLIER_DELIST
            approval_status: 审核状态（可选）- APPROVED/DRAFT
        """
        return await self._query_products(
            access_token=access_token,
            client_ip=client_ip,
            product_name=product_name,
            product_sku=product_sku,
            shelf_status=shelf_status,
            delist_type=delist_type,
            approval_status=approval_status,
            query_type="status"
        )

    async def get_product_detail(
        self,
        access_token: str,
        client_ip: Optional[str] = None,
        product_id: str = None
    ) -> Dict[str, Any]:
        """
        获取商品详情信息

        Args:
            access_token: ELECTRON平台访问令牌
            client_ip: 客户端IP地址
            product_id: 商品ID

        Returns:
            包含商品详情的字典
        """
        try:
            logger.info(f"开始商品详情查询 - 商品ID: {product_id}")

            if not product_id or not product_id.strip():
                return {
                    'success': False,
                    'message': '商品ID不能为空',
                    'data': {}
                }

            # 构建请求体
            request_body = {
                "id": product_id.strip()
            }

            # 实现智能降级查询机制
            result = await self._query_product_detail_with_fallback(
                access_token=access_token,
                client_ip=client_ip,
                request_body=request_body
            )

            # 如果商品详情获取成功，则获取审核历史数据
            if result.get('success'):
                # 记录是否使用了降级查询
                fallback_used = result.get('fallback_used', False)
                if fallback_used:
                    logger.info("商品详情使用了降级查询，继续获取审核历史")

                approval_history = await self._get_approval_history(
                    access_token=access_token,
                    client_ip=client_ip,
                    product_id=product_id
                )

                # 将审核历史数据合并到商品详情响应中
                if 'data' in result and isinstance(result['data'], dict):
                    result['data']['approvalHistory'] = approval_history

                # 移除内部标记，避免传递给前端
                if 'fallback_used' in result:
                    del result['fallback_used']

            return result

        except Exception as e:
            logger.error(f"商品详情查询异常: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': f'查询过程中发生错误: {str(e)}',
                'data': {}
            }

    async def _query_product_detail_with_fallback(
        self,
        access_token: str,
        client_ip: Optional[str] = None,
        request_body: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        使用智能降级查询机制获取商品详情

        Args:
            access_token: ELECTRON平台访问令牌
            client_ip: 客户端IP地址
            request_body: 请求体

        Returns:
            包含商品详情的字典
        """
        try:
            # 首先尝试主要端点（已审核商品详情）
            logger.info(f"开始主要端点查询 - URL: {self.product_detail_url}")
            logger.debug(f"请求体: {request_body}")

            try:
                response_data = await self._post_request(
                    url=self.product_detail_url,
                    access_token=access_token,
                    client_ip=client_ip,
                    json_data=request_body,
                    timeout=30
                )

                # 处理主要端点响应
                result = await self._process_product_detail_response(response_data)

                if result.get('success'):
                    logger.info("主要端点查询成功")
                    return result
                else:
                    logger.warning(f"主要端点返回业务错误: {result.get('message')}")
                    # 如果是业务逻辑错误，不进行降级查询
                    return result

            except Exception as e:
                error_message = str(e)
                logger.warning(f"主要端点查询异常: {error_message}")

                # 检查是否为HTTP 500错误，只有500错误才触发降级查询
                if "500" in error_message or "Internal Server Error" in error_message:
                    logger.info("检测到HTTP 500错误，触发降级查询机制")

                    # 尝试备用端点（基础商品详情）
                    logger.info(f"开始降级端点查询 - URL: {self.product_detail_fallback_url}")

                    try:
                        fallback_response_data = await self._post_request(
                            url=self.product_detail_fallback_url,
                            access_token=access_token,
                            client_ip=client_ip,
                            json_data=request_body,
                            timeout=30
                        )

                        # 处理降级端点响应
                        fallback_result = await self._process_product_detail_response(fallback_response_data)

                        if fallback_result.get('success'):
                            logger.info("降级端点查询成功")
                            # 在响应中标记使用了降级查询
                            fallback_result['fallback_used'] = True
                            return fallback_result
                        else:
                            logger.error(f"降级端点也返回业务错误: {fallback_result.get('message')}")
                            return fallback_result

                    except Exception as fallback_error:
                        logger.error(f"降级端点查询也失败: {str(fallback_error)}")
                        # 返回降级查询的错误信息
                        return {
                            'success': False,
                            'message': f'主要端点和降级端点都查询失败。主要端点错误: {error_message}，降级端点错误: {str(fallback_error)}',
                            'data': {}
                        }
                else:
                    # 非500错误，不进行降级查询，直接抛出原始异常
                    logger.info(f"非500错误，不触发降级查询: {error_message}")
                    raise e

        except Exception as e:
            logger.error(f"商品详情查询过程中发生未预期错误: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': f'查询过程中发生错误: {str(e)}',
                'data': {}
            }

    async def _process_product_detail_response(
        self,
        response_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        处理商品详情API响应数据

        Args:
            response_data: API原始响应数据

        Returns:
            处理后的响应数据
        """
        try:
            # 检查响应状态
            if response_data.get("code") != "200":
                error_msg = response_data.get("msg", "未知错误")
                logger.warning(f"商品详情API返回错误: {error_msg}")

                # 检查是否为权限相关错误
                permission_keywords = ["没有权限", "权限不足", "未授权", "token失效", "登录失效", "会话过期", "无权限", "权限错误"]
                is_permission_error = any(keyword in error_msg for keyword in permission_keywords)

                return {
                    'success': False,
                    'message': f'查询失败: {error_msg}',
                    'data': {},
                    'permission_error': is_permission_error
                }

            # 提取数据部分
            detail_data = response_data.get("data", {})

            if not detail_data:
                logger.warning("商品详情API返回空数据")
                return {
                    'success': False,
                    'message': '未找到商品详情信息',
                    'data': {}
                }

            logger.info(f"商品详情查询成功，商品ID: {detail_data.get('skuDto', {}).get('id', 'unknown')}")

            # 处理商品详情数据
            processed_detail = self._process_product_detail_data(detail_data)

            return {
                'success': True,
                'message': '商品详情查询成功',
                'data': processed_detail
            }

        except Exception as e:
            logger.error(f"处理商品详情API响应数据时发生错误: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': f'数据处理错误: {str(e)}',
                'data': {}
            }

    def _process_product_detail_data(self, detail_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理商品详情数据，确保数据结构完整

        Args:
            detail_data: 原始商品详情数据

        Returns:
            处理后的商品详情数据
        """
        try:
            # 提取主要数据结构
            sku_dto = detail_data.get('skuDto', {})
            sku_prop_val_list = detail_data.get('skuPropValDtoList', [])
            edit_source = detail_data.get('editSource', '')
            price_increase_letter_require = detail_data.get('priceIncreaseLetterRequire', False)

            # 处理图片URL列表，确保为列表格式
            image_minor_url_list = sku_dto.get('imageMinorUrlList', [])
            if not isinstance(image_minor_url_list, list):
                image_minor_url_list = []

            quotation_url_list = sku_dto.get('quotationUrlList', [])
            if not isinstance(quotation_url_list, list):
                quotation_url_list = []

            product_eligibility_urls = sku_dto.get('productEligibilityUrls', [])
            if not isinstance(product_eligibility_urls, list):
                product_eligibility_urls = []

            # 构建完整的商品详情数据结构
            processed_data = {
                'skuDto': {
                    'id': sku_dto.get('id', ''),
                    'sku': sku_dto.get('sku', ''),
                    'name': sku_dto.get('name', ''),
                    'firstCategoryName': sku_dto.get('firstCategoryName', ''),
                    'categoryName': sku_dto.get('categoryName', ''),
                    'secondCategoryName': sku_dto.get('secondCategoryName', ''),
                    'businessTypeName': sku_dto.get('businessTypeName', ''),
                    'wordName': sku_dto.get('wordName', ''),
                    'brandName': sku_dto.get('brandName', ''),
                    'stock': sku_dto.get('stock'),
                    'shipmentDate': sku_dto.get('shipmentDate'),
                    'imagePathUrl': sku_dto.get('imagePathUrl', ''),
                    'imageMinorUrlList': image_minor_url_list,
                    'quotationUrlList': quotation_url_list,
                    'productEligibilityUrls': product_eligibility_urls,
                    'mallPrice': sku_dto.get('mallPrice', 0),
                    'price': sku_dto.get('price', 0),
                    'costPrice': sku_dto.get('costPrice', 0),
                    'profitRate': sku_dto.get('profitRate', '0'),
                    'priceVoucherType': sku_dto.get('priceVoucherType', ''),
                    'bidResultVoucherNum': sku_dto.get('bidResultVoucherNum', ''),
                    'qualitiesType': sku_dto.get('qualitiesType', ''),
                    'manufacturer': sku_dto.get('manufacturer', ''),
                    'manufacturerCode': sku_dto.get('manufacturerCode', ''),
                    'useDesc': sku_dto.get('useDesc', ''),
                    'approvalType': sku_dto.get('approvalType', ''),
                    'approvalStatus': sku_dto.get('approvalStatus', ''),
                    'delistType': sku_dto.get('delistType', ''),
                    'shelfStatus': sku_dto.get('shelfStatus', '')
                },
                'skuPropValDtoList': sku_prop_val_list,
                'editSource': edit_source,
                'priceIncreaseLetterRequire': price_increase_letter_require
            }

            logger.debug(f"商品详情数据处理完成，商品名称: {processed_data['skuDto']['name']}")
            return processed_data

        except Exception as e:
            logger.error(f"处理商品详情数据时发生错误: {str(e)}", exc_info=True)
            # 返回基本结构，避免前端错误
            return {
                'skuDto': {},
                'skuPropValDtoList': [],
                'editSource': '',
                'priceIncreaseLetterRequire': False
            }

    async def _get_approval_history(
        self,
        access_token: str,
        client_ip: Optional[str] = None,
        product_id: str = None
    ) -> List[Dict[str, Any]]:
        """
        获取商品审核历史

        Args:
            access_token: ELECTRON平台访问令牌
            client_ip: 客户端IP地址
            product_id: 商品ID

        Returns:
            审核历史列表
        """
        try:
            logger.info(f"开始获取商品审核历史 - 商品ID: {product_id}")

            if not product_id or not product_id.strip():
                logger.warning("商品ID为空，跳过审核历史查询")
                return []

            # 构建请求体
            request_body = {
                "id": product_id.strip()
            }

            logger.debug(f"发送审核历史API请求到: {self.approval_detail_url}")
            logger.debug(f"请求体: {request_body}")

            # 使用基类的统一请求方法
            response_data = await self._post_request(
                url=self.approval_detail_url,
                access_token=access_token,
                client_ip=client_ip,
                json_data=request_body,
                timeout=15  # 审核历史查询设置较短超时时间
            )

            # 处理API响应
            return self._process_approval_history_response(response_data)

        except Exception as e:
            logger.warning(f"审核历史查询失败，但不影响商品详情显示: {str(e)}")
            # 审核历史查询失败不影响商品详情的正常显示
            return []

    def _process_approval_history_response(
        self,
        response_data: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        处理审核历史API响应数据

        Args:
            response_data: API原始响应数据

        Returns:
            处理后的审核历史列表
        """
        try:
            # 检查响应状态
            if response_data.get("code") != "200":
                error_msg = response_data.get("msg", "未知错误")
                logger.warning(f"审核历史API返回错误: {error_msg}")
                return []

            # 提取数据部分
            history_data = response_data.get("data", [])

            if not isinstance(history_data, list):
                logger.warning("审核历史API返回的数据格式不正确")
                return []

            if not history_data:
                logger.info("商品无审核历史记录")
                return []

            logger.info(f"审核历史查询成功，共 {len(history_data)} 条记录")

            # 处理审核历史数据，确保数据结构完整
            processed_history = []
            for item in history_data:
                if isinstance(item, dict):
                    processed_item = {
                        'approvalPhase': item.get('approvalPhase', ''),
                        'approvalStatus': item.get('approvalStatus', ''),
                        'approvalContent': item.get('approvalContent', ''),
                        'userName': item.get('userName', ''),
                        'createTime': item.get('createTime', '')
                    }
                    processed_history.append(processed_item)

            return processed_history

        except Exception as e:
            logger.error(f"处理审核历史API响应数据时发生错误: {str(e)}", exc_info=True)
            return []



    async def query_product_integrated(
        self,
        access_token: str,
        client_ip: Optional[str] = None,
        product_name: Optional[str] = None,
        product_sku: Optional[str] = None,
        shelf_status: Optional[str] = None,
        delist_type: Optional[str] = None,
        approval_status: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        智能整合查询商品状态和审核状态

        执行两阶段查询策略：
        1. 第一阶段：商品状态批量查询
        2. 第二阶段：审核状态补充查询
        3. 第三阶段：数据整合与排序

        Args:
            access_token: ELECTRON平台访问令牌
            product_name: 商品名称（可选）
            product_sku: 商品SKU编码（可选）
            shelf_status: 上架状态（可选）
            delist_type: 下架类型（可选）
            approval_status: 审核状态（可选）

        Returns:
            包含完整11字段数据的查询结果
        """
        try:
            logger.info("开始智能整合查询")

            # 验证查询条件
            if not product_name and not product_sku:
                return {
                    'success': False,
                    'message': '请输入商品名称或SKU编码',
                    'data': []
                }

            # 解析并记录用户输入的所有SKU
            input_skus = self._parse_input_skus(product_sku) if product_sku else []
            logger.info(f"用户输入SKU列表: {input_skus} (共{len(input_skus)}个)")

            # 判断查询类型：如果只有商品名称没有SKU，使用单次查询
            if product_name and not product_sku:
                logger.info("检测到仅商品名称查询，使用单次查询模式")
                return await self._query_by_name_only(
                    access_token=access_token,
                    client_ip=client_ip,
                    product_name=product_name,
                    shelf_status=shelf_status,
                    delist_type=delist_type,
                    approval_status=approval_status
                )

            # 第一阶段：商品状态查询
            logger.debug("第一阶段：执行商品状态查询")
            status_result = await self._query_products(
                access_token=access_token,
                client_ip=client_ip,
                product_name=product_name,
                product_sku=product_sku,
                shelf_status=shelf_status,
                delist_type=delist_type,
                approval_status=approval_status,
                query_type="status"
            )

            if not status_result.get('success'):
                logger.warning("第一阶段商品状态查询失败")
                return status_result

            status_data = status_result.get('data', [])
            found_status_skus = [item.get('sku', '').strip() for item in status_data if item.get('sku', '').strip()]
            logger.info(f"第一阶段查询完成，获得 {len(status_data)} 条状态数据，找到SKU: {found_status_skus}")

            # 第二阶段：审核状态补充查询（查询未找到的SKU + 符合条件的下架商品）
            logger.debug("第二阶段：执行审核状态补充查询")

            # 计算需要进行第二阶段查询的SKU（去重处理）
            remaining_skus = []
            downshelf_skus = []

            if input_skus:
                # 获取所有未在第一阶段找到的SKU（包含重复）
                all_remaining_skus = [sku for sku in input_skus if sku not in found_status_skus]
                # 去重用于API查询
                remaining_skus = self._get_unique_skus_for_query(all_remaining_skus)
                logger.info(f"第一阶段未找到状态的SKU: {all_remaining_skus} (共{len(all_remaining_skus)}个)")
                logger.info(f"第二阶段查询去重SKU: {remaining_skus} (共{len(remaining_skus)}个)")

            # 检查第一阶段查询结果中的下架商品，筛选需要补充审核信息的商品
            for product in status_data:
                sku = product.get('sku', '').strip()
                shelf_status = product.get('shelfStatus', '')
                delist_type = product.get('delistType', '')

                # 检查是否为下架状态且非永久下架的商品
                if (shelf_status == 'DOWN_SHELF' and
                    delist_type != 'OPERATE_DELIST_FOREVER' and
                    sku and sku not in remaining_skus):
                    downshelf_skus.append(sku)
                    logger.debug(f"发现需要补充审核信息的下架商品: {sku} (状态: {shelf_status}, 下架类型: {delist_type})")

            # 合并未找到的SKU和符合条件的下架商品SKU
            all_second_stage_skus = remaining_skus + downshelf_skus
            if downshelf_skus:
                logger.info(f"发现 {len(downshelf_skus)} 个符合条件的下架商品需要补充审核信息: {downshelf_skus}")
            logger.info(f"第二阶段总查询SKU: {all_second_stage_skus} (未找到: {len(remaining_skus)}个, 下架商品: {len(downshelf_skus)}个)")

            approval_result = {'success': True, 'data': []}
            if all_second_stage_skus:
                # 将所有需要第二阶段查询的SKU重新组合为查询字符串
                second_stage_sku_string = ' '.join(all_second_stage_skus)
                logger.debug(f"第二阶段查询SKU字符串: {second_stage_sku_string}")

                approval_result = await self._query_products(
                    access_token=access_token,
                    client_ip=client_ip,
                    product_name=product_name,
                    product_sku=second_stage_sku_string,
                    query_type="approval"
                )
            else:
                logger.info("无需进行第二阶段查询：所有SKU在第一阶段都已找到状态且无符合条件的下架商品")

            approval_data = []
            found_approval_skus = []
            if approval_result.get('success'):
                approval_data = approval_result.get('data', [])
                found_approval_skus = [item.get('sku', '').strip() for item in approval_data if item.get('sku', '').strip()]
                logger.info(f"第二阶段查询完成，获得 {len(approval_data)} 条审核数据，找到SKU: {found_approval_skus}")
            else:
                logger.warning("第二阶段审核状态查询失败，将使用空审核数据")

            # 第三阶段：数据整合与排序（包含无数据SKU处理）
            logger.debug("第三阶段：执行数据整合与排序")
            integrated_data = self._integrate_product_data(
                status_data,
                approval_data,
                product_sku,
                input_skus,
                found_status_skus,
                found_approval_skus
            )

            # 计算统计信息
            total_count = len(integrated_data)
            data_count = len([item for item in integrated_data if item.get('name') != '未找到商品信息'])
            no_data_count = total_count - data_count

            logger.info(f"智能整合查询完成，最终输出 {total_count} 条记录（有数据: {data_count} 条，无数据: {no_data_count} 条）")

            # 生成详细的返回消息
            if no_data_count > 0:
                message = f'智能整合查询完成，找到数据: {data_count} 条，无数据SKU: {no_data_count} 条，总计: {total_count} 条'
            else:
                message = f'智能整合查询成功，共找到 {total_count} 条商品记录'

            return {
                'success': True,
                'message': message,
                'data': integrated_data,
                'total': str(total_count),
                'statistics': {
                    'total_count': total_count,
                    'data_count': data_count,
                    'no_data_count': no_data_count,
                    'input_sku_count': len(input_skus) if input_skus else 0
                }
            }

        except Exception as e:
            logger.error(f"智能整合查询失败: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': f'智能整合查询失败: {str(e)}',
                'data': [],
                'total': '0'
            }

    async def _query_products(
        self,
        access_token: str,
        client_ip: Optional[str] = None,
        product_name: Optional[str] = None,
        product_sku: Optional[str] = None,
        shelf_status: Optional[str] = None,
        delist_type: Optional[str] = None,
        approval_status: Optional[str] = None,
        query_type: str = "status"
    ) -> Dict[str, Any]:
        """
        查询商品（通用方法）

        Args:
            access_token: ELECTRON平台访问令牌
            product_name: 商品名称（可选）
            product_sku: 商品SKU编码（可选，支持多个用空格分隔）
            shelf_status: 上架状态（可选）
            delist_type: 下架类型（可选）
            approval_status: 审核状态（可选）
            query_type: 查询类型（status=状态查询, approval=审核查询）

        Returns:
            包含查询结果的字典
        """
        try:
            query_desc = "商品审核状态" if query_type == "approval" else "商品状态"
            logger.debug(f"开始{query_desc}查询 - 名称: {product_name}, SKU: {product_sku}")

            # 构建请求体，获取SKU顺序信息
            request_body, original_sku_order = self._build_request_body(
                product_name, product_sku, shelf_status, delist_type, approval_status, query_type
            )

            # 根据查询类型选择API端点
            if query_type == "approval":
                api_url = self.approval_api_url
            else:
                api_url = self.api_url

            logger.debug(f"请求URL: {api_url}")
            logger.debug(f"请求体: {request_body}")

            # 使用基类的统一请求方法
            response_data = await self._post_request(
                url=api_url,
                access_token=access_token,
                client_ip=client_ip,
                json_data=request_body
            )

            # 处理API响应，如果是审核查询且有驳回商品，获取审核详情
            return await self._process_api_response(
                response_data,
                access_token,
                client_ip,
                query_type,
                original_sku_order
            )
                        
        except asyncio.TimeoutError:
            logger.error("商品状态查询请求超时")
            return {
                'success': False,
                'message': '查询请求超时，请稍后重试',
                'data': []
            }
        except Exception as e:
            logger.error(f"商品状态查询异常: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': f'查询过程中发生错误: {str(e)}',
                'data': []
            }
    
    def _build_request_body(
        self,
        product_name: Optional[str],
        product_sku: Optional[str],
        shelf_status: Optional[str] = None,
        delist_type: Optional[str] = None,
        approval_status: Optional[str] = None,
        query_type: str = "status"
    ) -> Tuple[Dict[str, Any], List[str]]:
        """
        构建API请求体

        Args:
            product_name: 商品名称
            product_sku: 商品SKU编码
            shelf_status: 上架状态
            delist_type: 下架类型
            approval_status: 审核状态
            query_type: 查询类型（status=状态查询, approval=审核查询）

        Returns:
            API请求体字典
        """
        # 处理SKU编码（清理和格式化，保持输入顺序）
        processed_sku = None
        original_sku_order = []  # 保存原始SKU输入顺序
        if product_sku:
            # 移除多余的空白字符，用空格分隔多个SKU，保持原始顺序
            sku_list = [sku.strip() for sku in product_sku.replace('\n', ' ').split() if sku.strip()]
            if sku_list:
                processed_sku = ' '.join(sku_list)
                original_sku_order = sku_list.copy()  # 保存原始顺序

        # 构建请求体（按照API文档要求）
        if query_type == "approval":
            # 审核查询的请求体结构
            request_body = {
                "name": product_name if product_name else None,
                "sku": processed_sku if processed_sku else None,
                "categoryIdList": None,
                "approvalStatus": None,  # 查询所有审核状态
                "brandIdList": [],       # 空数组
                "createTimeStart": None,
                "createTimeEnd": None,
                "updateTimeStart": None,
                "updateTimeEnd": None,
                "current": 1,
                "limit": 100
            }
        else:
            # 状态查询的请求体结构
            request_body = {
                "name": product_name if product_name else None,
                "sku": processed_sku if processed_sku else None,
                "categoryIdList": None,
                "shelfStatus": shelf_status if shelf_status else None,
                "delistType": delist_type if delist_type else None,
                "brandIdList": None,
                "approvalStatus": approval_status if approval_status else None,
                "createTimeStart": None,
                "createTimeEnd": None,
                "updateTimeStart": None,
                "updateTimeEnd": None,
                "current": 1,
                "limit": 100
            }

        return request_body, original_sku_order
    
    async def _process_api_response(
        self,
        response_data: Dict[str, Any],
        access_token: str = None,
        client_ip: Optional[str] = None,
        query_type: str = "status",
        original_sku_order: List[str] = None
    ) -> Dict[str, Any]:
        """
        处理API响应数据
        
        Args:
            response_data: API原始响应数据
            
        Returns:
            处理后的响应数据
        """
        try:
            # 检查响应状态
            if response_data.get("code") != "200":
                error_msg = response_data.get("msg", "未知错误")
                logger.warning(f"API返回错误: {error_msg}")

                # 检查是否为权限相关错误
                permission_keywords = ["没有权限", "权限不足", "未授权", "token失效", "登录失效", "会话过期", "无权限", "权限错误"]
                is_permission_error = any(keyword in error_msg for keyword in permission_keywords)

                return {
                    'success': False,
                    'message': f'查询失败: {error_msg}',
                    'data': [],
                    'permission_error': is_permission_error  # 标记权限错误
                }
            
            # 提取数据部分
            data_section = response_data.get("data", {})
            product_list = data_section.get("list", [])
            total_count = data_section.get("total", "0")
            
            logger.info(f"查询成功，共找到 {len(product_list)} 条商品记录，总计 {total_count} 条")
            
            # 处理商品数据
            processed_products = []
            for product in product_list:
                processed_product = self._process_product_item(product)

                # 如果是审核查询且商品状态为REJECT，获取审核详情
                if (query_type == "approval" and
                    product.get('approvalStatus') == 'REJECT' and
                    access_token):

                    approval_detail = await self._get_approval_detail(
                        access_token, product.get('id'), client_ip
                    )
                    if approval_detail:
                        processed_product.update(approval_detail)

                processed_products.append(processed_product)

            # 如果有原始SKU顺序信息，按照输入顺序排序结果
            if original_sku_order and len(original_sku_order) > 1:
                processed_products = self._sort_products_by_sku_order(processed_products, original_sku_order)
                logger.debug(f"已按照输入SKU顺序排序结果: {[p.get('sku', '') for p in processed_products]}")

            query_desc = "审核状态" if query_type == "approval" else "商品状态"
            return {
                'success': True,
                'message': f'{query_desc}查询成功，找到 {len(processed_products)} 条商品记录',
                'data': processed_products,
                'total': total_count
            }
            
        except Exception as e:
            logger.error(f"处理API响应数据时发生错误: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': f'数据处理错误: {str(e)}',
                'data': []
            }

    def _sort_products_by_sku_order(self, products: List[Dict[str, Any]], original_sku_order: List[str]) -> List[Dict[str, Any]]:
        """
        按照原始SKU输入顺序对商品结果进行排序

        Args:
            products: 商品数据列表
            original_sku_order: 原始SKU输入顺序

        Returns:
            按照输入顺序排序后的商品列表
        """
        if not original_sku_order or not products:
            return products

        # 创建SKU到索引的映射
        sku_order_map = {sku.strip(): index for index, sku in enumerate(original_sku_order)}

        # 为每个商品添加排序权重
        products_with_order = []
        for product in products:
            product_sku = product.get('sku', '').strip()
            # 如果SKU在原始顺序中，使用其索引；否则使用一个大的数值排在最后
            order_weight = sku_order_map.get(product_sku, len(original_sku_order) + 1000)
            products_with_order.append((order_weight, product))

        # 按照权重排序
        products_with_order.sort(key=lambda x: x[0])

        # 返回排序后的商品列表
        sorted_products = [product for _, product in products_with_order]

        logger.debug(f"SKU排序完成 - 原始顺序: {original_sku_order}, 结果顺序: {[p.get('sku', '') for p in sorted_products]}")

        return sorted_products

    async def _get_approval_detail(
        self,
        access_token: str,
        product_id: str,
        client_ip: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        获取商品审核详情

        Args:
            access_token: ELECTRON平台访问令牌
            product_id: 商品ID
            client_ip: 客户端IP地址

        Returns:
            审核详情数据或None
        """
        try:
            logger.debug(f"获取商品审核详情 - 商品ID: {product_id}")

            # 构建审核详情请求体
            request_body = {"id": product_id}

            # 使用基类的统一请求方法
            response_data = await self._post_request(
                url=self.approval_detail_url,
                access_token=access_token,
                client_ip=client_ip,
                json_data=request_body,
                timeout=15
            )

            logger.debug(f"审核详情API响应: {response_data}")

            if response_data.get("code") == "200":
                detail_list = response_data.get("data", [])
                if detail_list:
                    # 取最新的审核记录
                    latest_detail = detail_list[0]
                    return {
                        'approvalContent': latest_detail.get('approvalContent', ''),
                        'userName': latest_detail.get('userName', ''),
                        'approvalPhase': latest_detail.get('approvalPhase', ''),
                        'approvalCreateTime': latest_detail.get('createTime', '')
                    }
            else:
                logger.warning(f"审核详情API响应代码异常: {response_data.get('code')}")

        except Exception as e:
            logger.error(f"获取审核详情异常: {str(e)}")

        return None

    def _process_product_item(self, product: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理单个商品数据项

        Args:
            product: 原始商品数据

        Returns:
            处理后的商品数据
        """
        return {
            'id': product.get('id', ''),
            'name': product.get('name', ''),
            'sku': product.get('sku', ''),
            'categoryFullName': product.get('categoryFullName', ''),
            'price': product.get('price', 0),
            'approvalType': product.get('approvalType', ''),
            'approvalStatus': product.get('approvalStatus', ''),
            'shelfStatus': product.get('shelfStatus', ''),
            'delistType': product.get('delistType', ''),
            'delistReason': product.get('delistReason', ''),
            'createTime': product.get('createTime', ''),
            'updateTime': product.get('updateTime', ''),
            # 审核详情字段（如果有的话）
            'approvalContent': '',
            'userName': '',
            'approvalPhase': '',
            'approvalCreateTime': ''
        }

    def _parse_input_skus(self, sku_input: str) -> List[str]:
        """
        解析用户输入的SKU字符串，提取所有有效的SKU

        Args:
            sku_input: 用户输入的SKU字符串（支持空格、逗号、换行符分隔）

        Returns:
            解析后的SKU列表，保持输入顺序，保留重复SKU
        """
        if not sku_input or not sku_input.strip():
            return []

        # 支持多种分隔符：空格、逗号、换行符、制表符
        import re
        sku_list = re.split(r'[,\s\n\t]+', sku_input.strip())

        # 过滤空字符串和无效SKU，保留重复SKU但保持顺序
        valid_skus = []
        for sku in sku_list:
            sku = sku.strip()
            if sku:
                # 简单的SKU格式验证（可根据实际需求调整）
                if len(sku) > 0 and not sku.isspace():
                    valid_skus.append(sku)

        return valid_skus

    def _get_unique_skus_for_query(self, sku_list: List[str]) -> List[str]:
        """
        从SKU列表中获取去重的SKU，用于API查询

        Args:
            sku_list: 包含重复SKU的列表

        Returns:
            去重后的SKU列表，保持首次出现的顺序
        """
        if not sku_list:
            return []

        unique_skus = []
        seen_skus = set()
        for sku in sku_list:
            if sku not in seen_skus:
                unique_skus.append(sku)
                seen_skus.add(sku)

        return unique_skus

    def _create_default_sku_record(self, sku: str) -> Dict[str, Any]:
        """
        为无数据的SKU创建默认记录

        Args:
            sku: SKU编码

        Returns:
            默认的商品数据记录
        """
        return {
            'id': '',
            'sku': sku,
            'name': '未找到商品信息',
            'categoryFullName': '',
            'price': '0',
            'shelfStatus': '',
            'delistType': '',
            'delistReason': '',
            'approvalStatus': '',
            'approvalContent': '',
            'userName': '',
            'updateTime': ''
        }

    def _integrate_product_data(
        self,
        status_data: List[Dict[str, Any]],
        approval_data: List[Dict[str, Any]],
        original_sku_input: Optional[str] = None,
        input_skus: Optional[List[str]] = None,
        found_status_skus: Optional[List[str]] = None,
        found_approval_skus: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """
        整合商品状态数据和审核状态数据，确保所有输入SKU都包含在结果中

        Args:
            status_data: 商品状态查询结果
            approval_data: 审核状态查询结果
            original_sku_input: 原始SKU输入字符串，用于保持顺序
            input_skus: 用户输入的所有SKU列表
            found_status_skus: 在状态查询中找到的SKU列表
            found_approval_skus: 在审核查询中找到的SKU列表

        Returns:
            整合后的完整数据列表，包含所有输入SKU，按输入顺序排序
        """
        try:
            logger.debug(f"开始数据整合 - 状态数据: {len(status_data)} 条, 审核数据: {len(approval_data)} 条")

            # 如果没有输入SKU列表，使用原有逻辑
            if not input_skus:
                input_skus = self._parse_input_skus(original_sku_input) if original_sku_input else []

            logger.debug(f"输入SKU列表（包含重复）: {input_skus}")

            # 创建状态数据的SKU映射
            status_map = {}
            for status_item in status_data:
                sku = status_item.get('sku', '').strip()
                if sku:
                    status_map[sku] = status_item

            # 创建审核数据的SKU映射
            approval_map = {}
            for approval_item in approval_data:
                sku = approval_item.get('sku', '').strip()
                if sku:
                    approval_map[sku] = approval_item

            logger.debug(f"创建状态数据映射，包含 {len(status_map)} 个SKU")
            logger.debug(f"创建审核数据映射，包含 {len(approval_map)} 个SKU")

            # 按照用户输入顺序处理每个SKU（包括重复的）
            integrated_data = []

            for input_sku in input_skus:
                sku = input_sku.strip()
                logger.debug(f"处理输入SKU: {sku}")

                # 检查是否有状态数据
                if sku in status_map:
                    # 基于状态数据创建记录
                    status_item = status_map[sku]
                    integrated_item = {
                        'id': status_item.get('id', ''),
                        'sku': sku,
                        'name': status_item.get('name', ''),
                        'categoryFullName': status_item.get('categoryFullName', ''),
                        'price': status_item.get('price', 0),
                        'shelfStatus': status_item.get('shelfStatus', ''),
                        'delistType': status_item.get('delistType', ''),
                        'delistReason': status_item.get('delistReason', ''),
                        'updateTime': status_item.get('updateTime', ''),
                        # 默认审核相关字段
                        'approvalStatus': '',
                        'approvalContent': '',
                        'userName': ''
                    }

                    # 如果有对应的审核数据，补充审核信息
                    if sku in approval_map:
                        approval_item = approval_map[sku]
                        integrated_item.update({
                            'approvalStatus': approval_item.get('approvalStatus', ''),
                            'approvalContent': approval_item.get('approvalContent', ''),
                            'userName': approval_item.get('userName', '')
                        })
                        logger.debug(f"SKU {sku} 已补充审核信息")

                    logger.debug(f"SKU {sku} 使用状态数据创建记录")

                elif sku in approval_map:
                    # 仅基于审核数据创建记录
                    approval_item = approval_map[sku]
                    integrated_item = {
                        'id': approval_item.get('id', ''),
                        'sku': sku,
                        'name': approval_item.get('name', ''),
                        'categoryFullName': approval_item.get('categoryFullName', ''),
                        'price': approval_item.get('price', 0),
                        # 默认状态相关字段（因为没有状态数据）
                        'shelfStatus': '',
                        'delistType': '',
                        'delistReason': '',
                        'updateTime': approval_item.get('updateTime', ''),
                        # 审核相关字段
                        'approvalStatus': approval_item.get('approvalStatus', ''),
                        'approvalContent': approval_item.get('approvalContent', ''),
                        'userName': approval_item.get('userName', '')
                    }

                    logger.debug(f"SKU {sku} 使用审核数据创建记录")

                else:
                    # 无数据SKU，创建默认记录
                    integrated_item = self._create_default_sku_record(sku)
                    logger.debug(f"SKU {sku} 无数据，已创建默认记录")

                integrated_data.append(integrated_item)

            # 统计结果
            data_count = len([item for item in integrated_data if item.get('name') != '未找到商品信息'])
            no_data_count = len(integrated_data) - data_count

            logger.debug(f"数据整合完成 - 状态数据: {len(status_data)} 条, 审核数据: {len(approval_data)} 条, 无数据SKU: {no_data_count} 条, 整合结果: {len(integrated_data)} 条")

            if no_data_count > 0:
                no_data_skus = [item['sku'] for item in integrated_data if item.get('name') == '未找到商品信息']
                logger.info(f"无数据SKU列表: {no_data_skus}")

            # 数据已经按照输入顺序处理，无需额外排序
            logger.info(f"数据整合完成，输出 {len(integrated_data)} 条完整记录（包含重复SKU）")
            logger.debug(f"最终SKU顺序: {[item['sku'] for item in integrated_data]}")
            return integrated_data

        except Exception as e:
            logger.error(f"数据整合失败: {str(e)}", exc_info=True)
            # 如果整合失败，至少返回状态数据
            return status_data

    def _sort_by_original_sku_order(
        self,
        products: List[Dict[str, Any]],
        original_sku_input: str
    ) -> List[Dict[str, Any]]:
        """
        根据原始SKU输入字符串对商品数据进行排序

        Args:
            products: 商品数据列表
            original_sku_input: 原始SKU输入字符串（可能包含多个SKU，用空格或换行分隔）

        Returns:
            按照输入顺序排序后的商品列表
        """
        try:
            if not original_sku_input or not original_sku_input.strip():
                return products

            # 解析原始SKU输入，提取SKU列表并保持顺序
            sku_list = [sku.strip() for sku in original_sku_input.replace('\n', ' ').split() if sku.strip()]

            if not sku_list:
                return products

            logger.debug(f"解析原始SKU输入顺序: {sku_list}")

            # 调用现有的排序方法
            sorted_products = self._sort_products_by_sku_order(products, sku_list)

            logger.debug(f"按原始输入顺序排序完成 - 输入: {sku_list}, 输出: {[p.get('sku', '') for p in sorted_products]}")

            return sorted_products

        except Exception as e:
            logger.error(f"按原始SKU顺序排序失败: {str(e)}", exc_info=True)
            # 如果排序失败，返回原始数据
            return products

    async def _query_by_name_only(
        self,
        access_token: str,
        client_ip: Optional[str] = None,
        product_name: str = None,
        shelf_status: Optional[str] = None,
        delist_type: Optional[str] = None,
        approval_status: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        仅使用商品名称进行单次查询（不使用智能分阶段查询）

        Args:
            access_token: ELECTRON平台访问令牌
            product_name: 商品名称
            shelf_status: 上架状态（可选）
            delist_type: 下架类型（可选）
            approval_status: 审核状态（可选）

        Returns:
            包含查询结果的字典
        """
        try:
            logger.info(f"开始商品名称单次查询: {product_name}")

            # 执行商品状态查询
            status_result = await self._query_products(
                access_token=access_token,
                client_ip=client_ip,
                product_name=product_name,
                product_sku=None,  # 明确设置为None
                shelf_status=shelf_status,
                delist_type=delist_type,
                approval_status=approval_status,
                query_type="status"
            )

            if not status_result.get('success'):
                logger.warning("商品名称查询失败")
                return status_result

            status_data = status_result.get('data', [])
            logger.info(f"商品名称查询成功，找到 {len(status_data)} 条记录")

            # 对于商品名称查询，直接返回状态查询结果，不进行审核状态补充
            # 因为商品名称查询通常返回多个商品，审核状态补充意义不大
            return {
                'success': True,
                'message': f'商品名称查询成功，找到 {len(status_data)} 条商品记录',
                'data': status_data,
                'total': str(len(status_data))
            }

        except Exception as e:
            logger.error(f"商品名称查询异常: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': f'商品名称查询过程中发生错误: {str(e)}',
                'data': []
            }


# 创建全局实例
product_status_service = ProductStatusService()
