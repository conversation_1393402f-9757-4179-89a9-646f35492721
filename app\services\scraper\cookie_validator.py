"""
Cookie验证器模块
提供统一的Cookie有效性检测和失效判断逻辑
"""
import aiohttp
from typing import Dict, List, Optional
from app.core.logging import logger


class CookieValidator:
    """Cookie验证器类"""
    
    def __init__(self):
        # 常见的Cookie失效响应特征
        self.invalid_status_codes = [401, 403, 302]  # 常见的认证失败状态码
        self.invalid_keywords = [
            "登录", "login", "sign in", "authenticate",
            "unauthorized", "forbidden", "access denied",
            "请先登录", "需要登录", "session expired"
        ]
        
    async def validate_cookie_by_response(self, 
                                        test_url: str, 
                                        cookies: Dict[str, str],
                                        platform_name: str = "未知平台") -> bool:
        """
        通过HTTP响应验证Cookie有效性
        
        Args:
            test_url: 测试URL
            cookies: Cookie字典
            platform_name: 平台名称（用于日志）
            
        Returns:
            bool: Cookie是否有效
        """
        try:
            timeout = aiohttp.ClientTimeout(total=10)
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(
                    test_url, 
                    cookies=cookies,
                    allow_redirects=False  # 不自动跟随重定向
                ) as response:
                    
                    # 检查状态码
                    if response.status in self.invalid_status_codes:
                        logger.warning(f"{platform_name}平台Cookie失效 - HTTP {response.status}")
                        return False
                    
                    # 检查重定向到登录页面
                    if response.status in [301, 302, 303, 307, 308]:
                        location = response.headers.get('Location', '')
                        if any(keyword in location.lower() for keyword in ['login', 'signin', 'auth']):
                            logger.warning(f"{platform_name}平台Cookie失效 - 重定向到登录页面: {location}")
                            return False
                    
                    # 检查响应内容
                    if response.status == 200:
                        content = await response.text()
                        content_lower = content.lower()
                        
                        # 检查是否包含登录相关关键词
                        for keyword in self.invalid_keywords:
                            if keyword.lower() in content_lower:
                                logger.warning(f"{platform_name}平台Cookie失效 - 页面包含关键词: {keyword}")
                                return False
                        
                        logger.debug(f"{platform_name}平台Cookie验证通过")
                        return True
                    
                    # 其他状态码
                    logger.warning(f"{platform_name}平台Cookie验证异常 - HTTP {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"{platform_name}平台Cookie验证异常: {str(e)}")
            return False
    
    def detect_cookie_expiry_from_error(self, error_message: str, response_status: int = None) -> bool:
        """
        从错误信息中检测Cookie是否过期
        
        Args:
            error_message: 错误信息
            response_status: HTTP状态码
            
        Returns:
            bool: 是否为Cookie过期错误
        """
        if response_status and response_status in self.invalid_status_codes:
            return True
            
        error_lower = error_message.lower()
        expiry_keywords = [
            "cookie", "session", "expired", "invalid", "unauthorized",
            "forbidden", "access denied", "authentication", "login required"
        ]
        
        return any(keyword in error_lower for keyword in expiry_keywords)
    
    def should_retry_with_fresh_cookies(self, 
                                      failed_results: List[Dict], 
                                      total_count: int,
                                      failure_threshold: float = 0.3) -> bool:
        """
        判断是否应该刷新Cookie并重试
        
        Args:
            failed_results: 失败的结果列表
            total_count: 总数量
            failure_threshold: 失败率阈值（默认30%）
            
        Returns:
            bool: 是否应该重试
        """
        if not failed_results or total_count == 0:
            return False
            
        failure_rate = len(failed_results) / total_count
        
        # 如果失败率超过阈值，检查是否为Cookie相关错误
        if failure_rate >= failure_threshold:
            cookie_related_failures = 0
            for result in failed_results:
                error_msg = result.get('error', '')
                if self.detect_cookie_expiry_from_error(error_msg):
                    cookie_related_failures += 1
            
            # 如果大部分失败都与Cookie相关，建议重试
            cookie_failure_rate = cookie_related_failures / len(failed_results)
            if cookie_failure_rate >= 0.5:  # 50%的失败与Cookie相关
                logger.info(f"检测到高Cookie相关失败率: {cookie_failure_rate:.2%}，建议刷新Cookie重试")
                return True
        
        return False


# 创建全局实例
cookie_validator = CookieValidator()
