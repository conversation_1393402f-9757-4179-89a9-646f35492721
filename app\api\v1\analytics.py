"""
分析统计API路由 - 重构版
提供查询日志记录、趋势分析和模块统计接口
支持SQLite数据库存储和高级数据分析功能
"""

from fastapi import APIRouter, HTTPException, Depends, Request
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from app.core.auth import get_current_user
from app.services.analytics_service import analytics_service
from app.core.logging import logger


router = APIRouter()


class QueryLogRequest(BaseModel):
    """查询日志记录请求模型"""
    module: str = Field(..., description="模块名称")
    success: bool = Field(True, description="查询是否成功")
    response_time: Optional[float] = Field(None, description="响应时间(毫秒)")
    status_code: Optional[int] = Field(None, description="HTTP状态码")
    error_message: Optional[str] = Field(None, description="错误信息")
    query_params: Optional[Dict[str, Any]] = Field(None, description="查询参数")
    result_count: Optional[int] = Field(None, description="返回结果数量")


class QueryLogResponse(BaseModel):
    """查询日志记录响应模型"""
    success: bool = Field(..., description="记录是否成功")
    message: str = Field(..., description="响应消息")
    log_id: Optional[str] = Field(None, description="日志ID")
    record_id: Optional[int] = Field(None, description="数据库记录ID")


class HourlyTrendsResponse(BaseModel):
    """24小时趋势响应模型"""
    success: bool = Field(..., description="查询是否成功")
    message: Optional[str] = Field(None, description="错误消息")
    data: Dict[str, Any] = Field(..., description="趋势数据")


class WeeklyStatsResponse(BaseModel):
    """本周模块统计响应模型"""
    success: bool = Field(..., description="查询是否成功")
    message: Optional[str] = Field(None, description="错误消息")
    data: Dict[str, Any] = Field(..., description="统计数据")


class RealtimeDashboardResponse(BaseModel):
    """实时仪表板数据响应模型"""
    success: bool = Field(..., description="查询是否成功")
    message: Optional[str] = Field(None, description="错误消息")
    data: Dict[str, Any] = Field(..., description="实时仪表板数据")


class ModuleDetailLogsResponse(BaseModel):
    """模块详细日志响应模型"""
    success: bool = Field(..., description="查询是否成功")
    message: Optional[str] = Field(None, description="错误消息")
    data: Dict[str, Any] = Field(..., description="详细日志数据")


class ModuleSummaryStatsResponse(BaseModel):
    """模块汇总统计响应模型"""
    success: bool = Field(..., description="查询是否成功")
    message: Optional[str] = Field(None, description="错误消息")
    data: Dict[str, Any] = Field(..., description="汇总统计数据")


@router.post("/query-log", response_model=QueryLogResponse)
async def record_query_log(
    request: QueryLogRequest,
    http_request: Request,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    记录查询操作日志

    用于记录各个数据采集模块的查询操作，支持统计分析
    """
    try:
        # 获取用户信息
        username = current_user.get('username', 'unknown')
        platform = current_user.get('platform', 'ELECTRON')

        # 获取客户端信息
        client_ip = http_request.client.host if http_request.client else None
        user_agent = http_request.headers.get('user-agent')

        logger.info(f"记录查询日志请求 - 用户: {username}, 模块: {request.module}")

        # 调用服务记录日志
        result = await analytics_service.record_query_log(
            module=request.module,
            user=username,
            platform=platform,
            success=request.success,
            response_time=request.response_time,
            status_code=request.status_code,
            error_message=request.error_message,
            client_ip=client_ip,
            user_agent=user_agent,
            query_params=request.query_params,
            result_count=request.result_count
        )

        return QueryLogResponse(
            success=result['success'],
            message=result['message'],
            log_id=result.get('log_id'),
            record_id=result.get('record_id')
        )

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"记录查询日志API异常: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"记录查询日志失败: {str(e)}"
        )


@router.get("/hourly-trends", response_model=HourlyTrendsResponse)
async def get_hourly_trends(hours: int = 24):
    """
    获取24小时查询趋势数据

    Args:
        hours: 小时数，默认24小时，最大168小时（7天）

    Returns:
        包含小时趋势数据的响应

    Note:
        此接口无需认证，可直接访问
    """
    try:
        # 验证参数
        if hours < 1 or hours > 168:
            raise HTTPException(
                status_code=400,
                detail="小时数必须在1-168之间"
            )

        logger.info(f"获取{hours}小时趋势数据（无认证访问）")

        # 获取趋势数据
        result = await analytics_service.get_hourly_trends(hours=hours)

        return HourlyTrendsResponse(
            success=result['success'],
            message=result.get('message'),
            data=result['data']
        )

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"获取小时趋势API异常: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取趋势数据失败: {str(e)}"
        )


@router.get("/weekly-module-stats", response_model=WeeklyStatsResponse)
async def get_weekly_module_stats():
    """
    获取本周模块调用统计

    Returns:
        包含4个数据采集模块本周调用次数的统计数据

    Note:
        此接口无需认证，可直接访问
    """
    try:
        logger.info("获取本周模块统计（无认证访问）")

        # 获取模块统计数据
        result = await analytics_service.get_weekly_module_stats()

        return WeeklyStatsResponse(
            success=result['success'],
            message=result.get('message'),
            data=result['data']
        )

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"获取模块统计API异常: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取模块统计失败: {str(e)}"
        )


@router.get("/realtime-dashboard", response_model=RealtimeDashboardResponse)
async def get_realtime_dashboard_data():
    """
    获取实时仪表板数据

    Returns:
        包含趋势、模块统计、API状态和系统健康状态的综合数据

    Note:
        此接口无需认证，可直接访问，包含实时API状态检查
    """
    try:
        logger.debug("获取实时仪表板数据（无认证访问）")

        # 获取实时仪表板数据
        result = await analytics_service.get_realtime_dashboard_data()

        return RealtimeDashboardResponse(
            success=result['success'],
            message=result.get('message'),
            data=result['data']
        )

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"获取实时仪表板数据API异常: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取实时仪表板数据失败: {str(e)}"
        )


@router.get("/module-detail-logs/{module}", response_model=ModuleDetailLogsResponse)
async def get_module_detail_logs(
    module: str,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    status_filter: Optional[str] = None,
    search_term: Optional[str] = None,
    page: int = 1,
    page_size: int = 50
):
    """
    获取模块详细日志记录

    Args:
        module: 模块名称 (product, xqpz, xqd, brand, scraper)
        start_date: 开始日期 (YYYY-MM-DD)，默认最近7天
        end_date: 结束日期 (YYYY-MM-DD)，默认今天
        status_filter: 状态筛选 ('success', 'failed', 'all')
        search_term: 搜索关键词（用户、IP、错误信息、SKU等）
        page: 页码，默认1
        page_size: 每页大小，默认50，最大100

    Returns:
        包含详细日志记录的分页数据

    Note:
        此接口无需认证，可直接访问
    """
    try:
        # 验证模块名称
        valid_modules = ['product', 'xqpz', 'xqd', 'brand', 'scraper']
        if module not in valid_modules:
            raise HTTPException(
                status_code=400,
                detail=f"无效的模块名称，支持的模块: {', '.join(valid_modules)}"
            )

        # 验证分页参数
        if page < 1:
            raise HTTPException(status_code=400, detail="页码必须大于0")
        if page_size < 1 or page_size > 100:
            raise HTTPException(status_code=400, detail="每页大小必须在1-100之间")

        # 验证状态筛选
        if status_filter and status_filter not in ['success', 'failed', 'all']:
            raise HTTPException(
                status_code=400,
                detail="状态筛选必须是 'success', 'failed' 或 'all'"
            )

        logger.info(f"获取模块详细日志 - 模块: {module}, 页码: {page}, 状态: {status_filter}")

        # 获取详细日志数据
        result = await analytics_service.get_module_detail_logs(
            module=module,
            start_date=start_date,
            end_date=end_date,
            status_filter=status_filter,
            search_term=search_term,
            page=page,
            page_size=page_size
        )

        return ModuleDetailLogsResponse(
            success=result['success'],
            message=result.get('message'),
            data=result['data']
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取模块详细日志API异常: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取模块详细日志失败: {str(e)}"
        )


@router.get("/module-summary-stats/{module}", response_model=ModuleSummaryStatsResponse)
async def get_module_summary_stats(
    module: str,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None
):
    """
    获取模块汇总统计信息

    Args:
        module: 模块名称 (product, xqpz, xqd, brand, scraper)
        start_date: 开始日期 (YYYY-MM-DD)，默认最近7天
        end_date: 结束日期 (YYYY-MM-DD)，默认今天

    Returns:
        包含模块汇总统计、趋势分析和错误分析的数据

    Note:
        此接口无需认证，可直接访问
    """
    try:
        # 验证模块名称
        valid_modules = ['product', 'xqpz', 'xqd', 'brand', 'scraper']
        if module not in valid_modules:
            raise HTTPException(
                status_code=400,
                detail=f"无效的模块名称，支持的模块: {', '.join(valid_modules)}"
            )

        logger.info(f"获取模块汇总统计 - 模块: {module}, 时间范围: {start_date} 到 {end_date}")

        # 获取汇总统计数据
        result = await analytics_service.get_module_summary_stats(
            module=module,
            start_date=start_date,
            end_date=end_date
        )

        return ModuleSummaryStatsResponse(
            success=result['success'],
            message=result.get('message'),
            data=result['data']
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取模块汇总统计API异常: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取模块汇总统计失败: {str(e)}"
        )


@router.post("/create-test-data")
async def create_test_data():
    """
    创建测试数据，用于验证响应时间和错误信息显示

    Note:
        此接口仅用于开发测试，生产环境应移除
    """
    try:
        logger.info("创建分析统计测试数据")

        result = await analytics_service.create_test_data()

        return {
            'success': result['success'],
            'message': result['message'],
            'count': result['count']
        }

    except Exception as e:
        logger.error(f"创建测试数据API异常: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"创建测试数据失败: {str(e)}"
        )

