/**
 * 分页管理器
 * 统一的分页功能实现
 */
class PaginationManager {
    constructor(options = {}) {
        this.moduleId = options.moduleId || 'default';
        this.pageSize = options.pageSize || 20;
        this.currentPage = 1;
        this.totalItems = 0;
        this.totalPages = 0;
        this.data = [];
        this.filteredData = [];
        this.renderCallback = options.renderCallback || null;
        this.storageKey = `${this.moduleId}-pagination`;
        
        // 从本地存储恢复设置
        this.loadSettings();
    }

    /**
     * 设置数据
     * @param {Array} data - 原始数据
     * @param {Array} filteredData - 筛选后的数据
     */
    setData(data, filteredData = null) {
        this.data = data || [];
        this.filteredData = filteredData || data || [];
        this.updatePagination();
    }

    /**
     * 更新分页信息
     */
    updatePagination() {
        this.totalItems = this.filteredData.length;
        this.totalPages = Math.ceil(this.totalItems / this.pageSize);
        
        // 确保当前页不超过总页数
        if (this.currentPage > this.totalPages && this.totalPages > 0) {
            this.currentPage = this.totalPages;
        } else if (this.currentPage < 1) {
            this.currentPage = 1;
        }
    }

    /**
     * 获取当前页数据
     * @returns {Array} 当前页的数据
     */
    getCurrentPageData() {
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        return this.filteredData.slice(startIndex, endIndex);
    }

    /**
     * 跳转到指定页
     * @param {number} page - 页码
     */
    goToPage(page) {
        if (page < 1 || page > this.totalPages) {
            console.warn(`[${this.moduleId}] 无效的页码: ${page}`);
            return false;
        }

        this.currentPage = page;
        this.saveSettings();
        
        if (this.renderCallback) {
            this.renderCallback();
        }
        return true;
    }

    /**
     * 上一页
     */
    previousPage() {
        return this.goToPage(this.currentPage - 1);
    }

    /**
     * 下一页
     */
    nextPage() {
        return this.goToPage(this.currentPage + 1);
    }

    /**
     * 改变每页显示数量
     * @param {number} newPageSize - 新的每页数量
     */
    changePageSize(newPageSize) {
        const validSizes = [10, 20, 50, 80, 100];
        if (!validSizes.includes(newPageSize)) {
            console.warn(`[${this.moduleId}] 无效的页面大小: ${newPageSize}`);
            return false;
        }

        this.pageSize = newPageSize;
        this.currentPage = 1; // 重置到第一页
        this.updatePagination();
        this.saveSettings();
        
        if (this.renderCallback) {
            this.renderCallback();
        }
        return true;
    }

    /**
     * 生成分页控件HTML
     * @returns {string} 分页控件HTML
     */
    generatePaginationHTML() {
        if (this.totalItems === 0) {
            return '';
        }

        const startItem = (this.currentPage - 1) * this.pageSize + 1;
        const endItem = Math.min(this.currentPage * this.pageSize, this.totalItems);

        let paginationHTML = `
            <div class="pagination-container">
                <div class="pagination-info">
                    <div class="pagination-status">
                        第${this.currentPage}页，共${this.totalPages}页，总计${this.totalItems}条记录
                    </div>
                    <div class="pagination-size-selector">
                        <span>每页显示</span>
                        <select class="form-select form-select-sm" onchange="paginationManager_${this.moduleId}.changePageSize(parseInt(this.value))">
                            <option value="10" ${this.pageSize === 10 ? 'selected' : ''}>10条</option>
                            <option value="20" ${this.pageSize === 20 ? 'selected' : ''}>20条</option>
                            <option value="50" ${this.pageSize === 50 ? 'selected' : ''}>50条</option>
                            <option value="80" ${this.pageSize === 80 ? 'selected' : ''}>80条</option>
                            <option value="100" ${this.pageSize === 100 ? 'selected' : ''}>100条</option>
                        </select>
                    </div>
                </div>
                <div class="pagination-controls">
                    <nav aria-label="分页导航">
                        <ul class="pagination pagination-sm mb-0">
        `;

        // 上一页按钮
        if (this.currentPage > 1) {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="paginationManager_${this.moduleId}.previousPage(); return false;">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M15 6l-6 6l6 6"></path>
                        </svg>
                        上一页
                    </a>
                </li>
            `;
        } else {
            paginationHTML += `
                <li class="page-item disabled">
                    <span class="page-link">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M15 6l-6 6l6 6"></path>
                        </svg>
                        上一页
                    </span>
                </li>
            `;
        }

        // 页码按钮
        const maxVisiblePages = 5;
        let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(this.totalPages, startPage + maxVisiblePages - 1);

        // 调整起始页
        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        // 第一页
        if (startPage > 1) {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="paginationManager_${this.moduleId}.goToPage(1); return false;">1</a>
                </li>
            `;
            if (startPage > 2) {
                paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }

        // 页码范围
        for (let i = startPage; i <= endPage; i++) {
            if (i === this.currentPage) {
                paginationHTML += `
                    <li class="page-item active">
                        <span class="page-link">${i}</span>
                    </li>
                `;
            } else {
                paginationHTML += `
                    <li class="page-item">
                        <a class="page-link" href="#" onclick="paginationManager_${this.moduleId}.goToPage(${i}); return false;">${i}</a>
                    </li>
                `;
            }
        }

        // 最后一页
        if (endPage < this.totalPages) {
            if (endPage < this.totalPages - 1) {
                paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="paginationManager_${this.moduleId}.goToPage(${this.totalPages}); return false;">${this.totalPages}</a>
                </li>
            `;
        }

        // 下一页按钮
        if (this.currentPage < this.totalPages) {
            paginationHTML += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="paginationManager_${this.moduleId}.nextPage(); return false;">
                        下一页
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M9 6l6 6l-6 6"></path>
                        </svg>
                    </a>
                </li>
            `;
        } else {
            paginationHTML += `
                <li class="page-item disabled">
                    <span class="page-link">
                        下一页
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M9 6l6 6l-6 6"></path>
                        </svg>
                    </span>
                </li>
            `;
        }

        paginationHTML += `
                        </ul>
                    </nav>
                </div>
            </div>
        `;

        return paginationHTML;
    }

    /**
     * 保存设置到本地存储
     */
    saveSettings() {
        const settings = {
            pageSize: this.pageSize,
            currentPage: this.currentPage
        };
        localStorage.setItem(this.storageKey, JSON.stringify(settings));
    }

    /**
     * 从本地存储加载设置
     */
    loadSettings() {
        try {
            const saved = localStorage.getItem(this.storageKey);
            if (saved) {
                const settings = JSON.parse(saved);
                this.pageSize = settings.pageSize || 20;
                // 不恢复currentPage，每次都从第1页开始
            }
        } catch (error) {
            console.warn(`[${this.moduleId}] 加载分页设置失败:`, error);
        }
    }

    /**
     * 获取所有数据（用于导出）
     * @returns {Array} 所有筛选后的数据
     */
    getAllData() {
        return this.filteredData;
    }

    /**
     * 获取分页信息
     * @returns {Object} 分页信息
     */
    getInfo() {
        return {
            currentPage: this.currentPage,
            pageSize: this.pageSize,
            totalPages: this.totalPages,
            totalItems: this.totalItems,
            startItem: (this.currentPage - 1) * this.pageSize + 1,
            endItem: Math.min(this.currentPage * this.pageSize, this.totalItems)
        };
    }
}

// 导出到全局作用域
window.PaginationManager = PaginationManager;
