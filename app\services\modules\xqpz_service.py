"""
需求凭证查询服务
负责与电子超市需求凭证API的对接
支持单个和批量查询功能
"""

import aiohttp
import asyncio
import re
from typing import Dict, List, Any, Optional, Union
from app.core.config import settings
from app.core.logging import logger
from app.services.base_electron_service import BaseElectronService


class XqpzService(BaseElectronService):
    """需求凭证查询服务类"""
    
    def __init__(self):
        super().__init__()
        # 需求凭证查询API端点
        self.api_url = "https://eshop.eavic.com/api/inquiry/supplier/answer_sheet_detail_voucher/list"
    
    async def query_voucher_list(
        self,
        access_token: str,
        client_ip: Optional[str] = None,
        ask_sheet_code: Optional[str] = None,
        sku: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        查询需求凭证列表
        
        Args:
            access_token: ELECTRON平台访问令牌
            ask_sheet_code: 比价单号（可选）
            sku: 商品SKU编码（可选）
            
        Returns:
            包含查询结果的字典
        """
        try:
            logger.info(f"开始需求凭证查询 - 比价单号: {ask_sheet_code}, SKU: {sku}")
            
            # 构建请求体
            request_body = {
                "voucherCode": None,
                "answerSheetCode": None,
                "askSheetCode": ask_sheet_code,
                "answerVoucherStatus": None,
                "sku": sku,
                "purchaserNameList": [],
                "answerBeginTimeStart": None,
                "answerBeginTimeEnd": None,
                "answerEndTimeStart": None,
                "answerEndTimeEnd": None,
                "limit": 100,
                "current": 1
            }
            
            logger.debug(f"发送API请求到: {self.api_url}")
            logger.debug(f"请求体: {request_body}")

            # 使用基类的统一请求方法
            response_data = await self._post_request(
                url=self.api_url,
                access_token=access_token,
                client_ip=client_ip,
                json_data=request_body
            )

            # 处理API响应
            return await self._process_api_response(response_data)

        except Exception as e:
            logger.error(f"需求凭证查询异常: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': f'查询过程中发生错误: {str(e)}',
                'data': []
            }

    def _parse_input_values(self, input_str: str) -> List[str]:
        """
        解析输入字符串，支持多种分隔符

        Args:
            input_str: 输入字符串

        Returns:
            解析后的值列表
        """
        if not input_str or not input_str.strip():
            return []

        # 支持的分隔符：空格、换行符、制表符
        # 使用正则表达式分割，同时去除空白字符
        values = re.split(r'[\s\n\t]+', input_str.strip())

        # 过滤空值并去除前后空白
        cleaned_values = [value.strip() for value in values if value.strip()]

        logger.debug(f"解析输入值: 原始='{input_str}' -> 解析结果={cleaned_values}")
        return cleaned_values

    async def query_voucher_batch(
        self,
        access_token: str,
        client_ip: Optional[str] = None,
        ask_sheet_codes: Optional[List[str]] = None,
        skus: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        批量查询需求凭证列表

        Args:
            access_token: ELECTRON平台访问令牌
            client_ip: 客户端IP地址
            ask_sheet_codes: 比价单号列表（可选）
            skus: 商品SKU编码列表（可选）

        Returns:
            包含批量查询结果的字典
        """
        try:
            logger.info(f"开始批量需求凭证查询 - 比价单号数量: {len(ask_sheet_codes) if ask_sheet_codes else 0}, SKU数量: {len(skus) if skus else 0}")

            # 验证输入参数
            if not ask_sheet_codes and not skus:
                return {
                    'success': False,
                    'message': '请提供比价单号或商品SKU编码',
                    'data': []
                }

            # 准备查询任务列表
            query_tasks = []
            query_info = []  # 用于记录查询信息，保持顺序

            # 处理比价单号查询
            if ask_sheet_codes:
                for ask_sheet_code in ask_sheet_codes:
                    if ask_sheet_code.strip():
                        query_tasks.append(
                            self.query_voucher_list(
                                access_token=access_token,
                                client_ip=client_ip,
                                ask_sheet_code=ask_sheet_code.strip(),
                                sku=None
                            )
                        )
                        query_info.append({
                            'type': 'ask_sheet_code',
                            'value': ask_sheet_code.strip(),
                            'index': len(query_info)
                        })

            # 处理SKU查询
            if skus:
                for sku in skus:
                    if sku.strip():
                        query_tasks.append(
                            self.query_voucher_list(
                                access_token=access_token,
                                client_ip=client_ip,
                                ask_sheet_code=None,
                                sku=sku.strip()
                            )
                        )
                        query_info.append({
                            'type': 'sku',
                            'value': sku.strip(),
                            'index': len(query_info)
                        })

            if not query_tasks:
                return {
                    'success': False,
                    'message': '没有有效的查询参数',
                    'data': []
                }

            logger.info(f"准备执行 {len(query_tasks)} 个并发查询任务")

            # 执行并发查询
            results = await asyncio.gather(*query_tasks, return_exceptions=True)

            # 处理查询结果
            all_data = []
            success_count = 0
            error_count = 0
            error_messages = []

            for i, (result, info) in enumerate(zip(results, query_info)):
                try:
                    if isinstance(result, Exception):
                        # 处理异常情况
                        error_msg = f"{info['type']}='{info['value']}': {str(result)}"
                        error_messages.append(error_msg)
                        error_count += 1
                        logger.error(f"查询任务 {i+1} 异常: {error_msg}")
                    elif isinstance(result, dict):
                        if result.get('success', False):
                            # 成功的查询结果
                            data = result.get('data', [])
                            if data:
                                # 为每条记录添加查询来源信息
                                for record in data:
                                    record['_query_source'] = {
                                        'type': info['type'],
                                        'value': info['value'],
                                        'index': info['index']
                                    }
                                all_data.extend(data)
                                success_count += 1
                                logger.debug(f"查询任务 {i+1} 成功: {info['type']}='{info['value']}' 返回 {len(data)} 条记录")
                            else:
                                success_count += 1
                                logger.debug(f"查询任务 {i+1} 成功但无数据: {info['type']}='{info['value']}'")
                        else:
                            # 查询失败
                            error_msg = f"{info['type']}='{info['value']}': {result.get('message', '查询失败')}"
                            error_messages.append(error_msg)
                            error_count += 1
                            logger.warning(f"查询任务 {i+1} 失败: {error_msg}")
                    else:
                        # 意外的结果格式
                        error_msg = f"{info['type']}='{info['value']}': 意外的响应格式"
                        error_messages.append(error_msg)
                        error_count += 1
                        logger.error(f"查询任务 {i+1} 返回意外格式: {result}")

                except Exception as e:
                    error_msg = f"{info['type']}='{info['value']}': 处理结果时发生错误: {str(e)}"
                    error_messages.append(error_msg)
                    error_count += 1
                    logger.error(f"处理查询任务 {i+1} 结果时异常: {str(e)}", exc_info=True)

            # 生成汇总消息
            total_queries = len(query_tasks)
            if success_count == total_queries:
                message = f"批量查询完成，共查询 {total_queries} 个条件，全部成功，找到 {len(all_data)} 条需求凭证记录"
            elif success_count > 0:
                message = f"批量查询完成，共查询 {total_queries} 个条件，成功 {success_count} 个，失败 {error_count} 个，找到 {len(all_data)} 条需求凭证记录"
                if error_messages:
                    message += f"。失败详情: {'; '.join(error_messages[:3])}"
                    if len(error_messages) > 3:
                        message += f" 等 {len(error_messages)} 个错误"
            else:
                message = f"批量查询失败，共查询 {total_queries} 个条件，全部失败"
                if error_messages:
                    message += f"。错误详情: {'; '.join(error_messages[:3])}"
                    if len(error_messages) > 3:
                        message += f" 等 {len(error_messages)} 个错误"

            logger.info(f"批量查询完成 - 总数: {total_queries}, 成功: {success_count}, 失败: {error_count}, 结果: {len(all_data)} 条记录")

            return {
                'success': success_count > 0,  # 只要有一个成功就算成功
                'message': message,
                'data': all_data,
                'total': len(all_data),
                'batch_summary': {
                    'total_queries': total_queries,
                    'success_count': success_count,
                    'error_count': error_count,
                    'result_count': len(all_data)
                }
            }

        except Exception as e:
            logger.error(f"批量需求凭证查询异常: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': f'批量查询过程中发生错误: {str(e)}',
                'data': []
            }

    async def query_voucher_batch_from_input(
        self,
        access_token: str,
        client_ip: Optional[str] = None,
        ask_sheet_code_input: Optional[str] = None,
        sku_input: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        从输入字符串批量查询需求凭证列表

        Args:
            access_token: ELECTRON平台访问令牌
            client_ip: 客户端IP地址
            ask_sheet_code_input: 比价单号输入字符串（支持多种分隔符）
            sku_input: 商品SKU编码输入字符串（支持多种分隔符）

        Returns:
            包含批量查询结果的字典
        """
        try:
            # 解析输入字符串
            ask_sheet_codes = self._parse_input_values(ask_sheet_code_input) if ask_sheet_code_input else None
            skus = self._parse_input_values(sku_input) if sku_input else None

            # 检查是否为批量查询
            is_batch_query = (
                (ask_sheet_codes and len(ask_sheet_codes) > 1) or
                (skus and len(skus) > 1) or
                (ask_sheet_codes and skus)  # 同时有比价单号和SKU也算批量
            )

            if is_batch_query:
                logger.info(f"检测到批量查询模式 - 比价单号: {len(ask_sheet_codes) if ask_sheet_codes else 0}, SKU: {len(skus) if skus else 0}")
                return await self.query_voucher_batch(
                    access_token=access_token,
                    client_ip=client_ip,
                    ask_sheet_codes=ask_sheet_codes,
                    skus=skus
                )
            else:
                # 单个查询模式
                ask_sheet_code = ask_sheet_codes[0] if ask_sheet_codes else None
                sku = skus[0] if skus else None

                logger.info(f"使用单个查询模式 - 比价单号: {ask_sheet_code}, SKU: {sku}")
                return await self.query_voucher_list(
                    access_token=access_token,
                    client_ip=client_ip,
                    ask_sheet_code=ask_sheet_code,
                    sku=sku
                )

        except Exception as e:
            logger.error(f"批量查询输入处理异常: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': f'查询输入处理过程中发生错误: {str(e)}',
                'data': []
            }
    
    async def _process_api_response(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理API响应数据
        
        Args:
            response_data: API响应的原始数据
            
        Returns:
            处理后的响应数据
        """
        try:
            # 检查响应状态码
            if response_data.get("code") != "200":
                error_msg = response_data.get("msg", "未知错误")
                logger.warning(f"API返回错误: {error_msg}")

                # 检查是否为权限相关错误
                permission_keywords = ["没有权限", "权限不足", "未授权", "token失效", "登录失效", "会话过期", "无权限", "权限错误"]
                is_permission_error = any(keyword in error_msg for keyword in permission_keywords)

                return {
                    'success': False,
                    'message': f'查询失败: {error_msg}',
                    'data': [],
                    'permission_error': is_permission_error  # 标记权限错误
                }
            
            # 提取数据部分
            data_section = response_data.get("data", {})
            voucher_list = data_section.get("list", [])
            total_count = data_section.get("total", "0")
            
            logger.info(f"查询成功，共找到 {len(voucher_list)} 条需求凭证记录，总计 {total_count} 条")
            
            # 处理凭证数据
            processed_vouchers = []
            for voucher in voucher_list:
                processed_voucher = self._process_voucher_item(voucher)
                processed_vouchers.append(processed_voucher)
            
            return {
                'success': True,
                'message': f'查询成功，找到 {len(processed_vouchers)} 条记录',
                'data': processed_vouchers,
                'total': int(total_count) if total_count.isdigit() else len(processed_vouchers)
            }
            
        except Exception as e:
            logger.error(f"处理API响应异常: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': f'数据处理错误: {str(e)}',
                'data': []
            }
    
    def _process_voucher_item(self, voucher: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理单个凭证项目数据
        
        Args:
            voucher: 原始凭证数据
            
        Returns:
            处理后的凭证数据
        """
        try:
            return {
                'id': voucher.get('id', ''),
                'voucherCode': voucher.get('voucherCode', ''),
                'askSheetCode': voucher.get('askSheetCode', ''),
                'prodName': voucher.get('prodName', ''),
                'sku': voucher.get('sku', ''),
                'unit': voucher.get('unit', ''),
                'answerPrice': voucher.get('answerPrice', 0),
                'answerVoucherStatus': voucher.get('answerVoucherStatus', ''),
                'purchaserName': voucher.get('purchaserName', ''),
                'askSheetUser': voucher.get('askSheetUser', ''),
                'askSheetUserMobile': voucher.get('askSheetUserMobile', '')
            }
        except Exception as e:
            logger.warning(f"处理凭证项目数据异常: {str(e)}")
            return {
                'id': '',
                'voucherCode': '',
                'askSheetCode': '',
                'prodName': '',
                'sku': '',
                'unit': '',
                'answerPrice': 0,
                'answerVoucherStatus': '',
                'purchaserName': '',
                'askSheetUser': '',
                'askSheetUserMobile': ''
            }


# 创建全局服务实例
xqpz_service = XqpzService()
