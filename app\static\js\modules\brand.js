/**
 * 品牌库采集模块 JavaScript
 * 使用模块化设计模式，提供完整的数据采集、筛选、分页、导出功能
 */

// 品牌库模块对象
const BrandModule = {
    // 当前查询模式：'brand' 或 'approval'
    currentMode: 'brand',

    // 模块初始化
    init() {
        console.log('品牌库采集模块已加载');

        // 检查ELECTRON访问权限
        this.checkElectronAccess();

        // 绑定表单提交事件
        this.bindFormEvents();

        // 绑定模式切换事件
        this.bindModeToggleEvents();

        // 加载保存的查询条件
        this.loadQueryConditionsFromStorage();

        // 初始化数据筛选器
        this.initializeDataFilter();

        // 初始化UI状态
        this.updateModeUI();

        // 初始化国际分类组件
        this.classificationComponent.init();

        // 初始化标志
        this.isUpdatingFromCrop = false;
        this.originalLogoFile = null;

        // 上传状态管理
        this.uploadStatus = {
            logo: {
                status: 'ready', // ready, uploading, success, error
                fileId: null,
                fileUrl: null,
                file: null
            },
            trademark: {
                status: 'ready',
                fileId: null,
                fileUrl: null,
                file: null
            }
        };

        // 表单验证状态
        this.formValidation = {
            basicInfo: false,
            logoUploaded: false,
            trademarkUploaded: false
        };

        // 初始化新增品牌功能
        this.initAddBrandModal();
    },

    // 检查ELECTRON访问权限
    checkElectronAccess() {
        if (typeof authManager === 'undefined' || !authManager.userInfo) {
            return;
        }

        const loginPrompt = document.getElementById('login-prompt');
        const electronAccessDenied = document.getElementById('electron-access-denied');
        const mainContent = document.getElementById('main-content');

        if (authManager.token && authManager.userInfo.local_username) {
            // 用户已登录，检查ELECTRON权限
            if (authManager.userInfo.electron_access) {
                // 有ELECTRON权限，显示主要内容
                if (loginPrompt) loginPrompt.style.display = 'none';
                if (electronAccessDenied) electronAccessDenied.style.display = 'none';
                if (mainContent) mainContent.style.display = 'block';
            } else {
                // 没有ELECTRON权限，显示权限不足提示
                if (loginPrompt) loginPrompt.style.display = 'none';
                if (electronAccessDenied) electronAccessDenied.style.display = 'block';
                if (mainContent) mainContent.style.display = 'none';
            }
        } else {
            // 用户未登录，显示登录提示
            if (loginPrompt) loginPrompt.style.display = 'block';
            if (electronAccessDenied) electronAccessDenied.style.display = 'none';
            if (mainContent) mainContent.style.display = 'none';
        }
    },

    // 绑定表单事件
    bindFormEvents() {
        const form = document.getElementById('brand-form');
        if (form) {
            form.addEventListener('submit', this.handleFormSubmit.bind(this));
        }

        // 重置按钮事件
        const resetBtn = document.getElementById('brand-reset-btn');
        if (resetBtn) {
            resetBtn.addEventListener('click', this.handleFormReset.bind(this));
        }
    },

    // 绑定模式切换事件
    bindModeToggleEvents() {
        const brandModeBtn = document.getElementById('query-type-status');
        const approvalModeBtn = document.getElementById('query-type-approval');

        console.log('绑定模式切换事件:', {
            brandModeBtn: !!brandModeBtn,
            approvalModeBtn: !!approvalModeBtn
        });

        if (brandModeBtn) {
            brandModeBtn.addEventListener('change', (e) => {
                console.log('品牌查询模式被选中:', e.target.checked);
                if (e.target.checked) {
                    this.switchToMode('brand');
                }
            });
        } else {
            console.error('未找到品牌查询模式按钮');
        }

        if (approvalModeBtn) {
            approvalModeBtn.addEventListener('change', (e) => {
                console.log('审核查询模式被选中:', e.target.checked);
                if (e.target.checked) {
                    this.switchToMode('approval');
                }
            });
        } else {
            console.error('未找到审核查询模式按钮');
        }
    },

    // 切换查询模式
    switchToMode(mode) {
        console.log(`切换查询模式: ${this.currentMode} -> ${mode}`);
        this.currentMode = mode;

        // 保存通用字段值
        const commonName = this.getCommonNameValue();
        console.log('保存的通用字段值:', commonName);

        // 更新UI显示
        this.updateModeUI();

        // 清空表单
        const form = document.getElementById('brand-form');
        if (form) {
            form.reset();
            console.log('表单已重置');
        }

        // 恢复通用字段值
        this.setCommonNameValue(commonName);
        console.log('恢复通用字段值:', commonName);

        // 隐藏结果
        this.hideResults();

        console.log(`成功切换到${mode === 'brand' ? '品牌查询' : '审核查询'}模式`);
    },

    // 更新模式UI
    updateModeUI() {
        const cardTitle = document.getElementById('query-card-title');
        const submitBtnText = document.getElementById('submit-btn-text');
        const brandFields = document.getElementById('brand-mode-fields');
        const approvalFields = document.getElementById('approval-mode-fields');

        console.log('更新模式UI:', this.currentMode);
        console.log('找到的元素:', {
            cardTitle: !!cardTitle,
            submitBtnText: !!submitBtnText,
            brandFields: !!brandFields,
            approvalFields: !!approvalFields
        });

        if (this.currentMode === 'brand') {
            if (cardTitle) cardTitle.textContent = '品牌库查询条件';
            if (submitBtnText) submitBtnText.textContent = '查询品牌库';
            if (brandFields) {
                brandFields.style.display = 'block';
                console.log('显示品牌查询字段');
            }
            if (approvalFields) {
                approvalFields.style.display = 'none';
                console.log('隐藏审核查询字段');
            }
        } else {
            if (cardTitle) cardTitle.textContent = '品牌审核查询条件';
            if (submitBtnText) submitBtnText.textContent = '查询审核列表';
            if (brandFields) {
                brandFields.style.display = 'none';
                console.log('隐藏品牌查询字段');
            }
            if (approvalFields) {
                approvalFields.style.display = 'block';
                console.log('显示审核查询字段');
            }
        }
    },

    // 获取通用品牌名称字段值
    getCommonNameValue() {
        if (this.currentMode === 'brand') {
            const nameInput = document.querySelector('input[name="name"]');
            return nameInput ? nameInput.value : '';
        } else {
            const nameInput = document.querySelector('input[name="approval_name"]');
            return nameInput ? nameInput.value : '';
        }
    },

    // 设置通用品牌名称字段值
    setCommonNameValue(value) {
        if (this.currentMode === 'brand') {
            const nameInput = document.querySelector('input[name="name"]');
            if (nameInput) nameInput.value = value;
        } else {
            const nameInput = document.querySelector('input[name="approval_name"]');
            if (nameInput) nameInput.value = value;
        }
    },

    // 初始化数据筛选器
    initializeDataFilter() {
        if (!dataFilter) {
            dataFilter = new DataFilter('data-filter-container', {
                storageKey: 'brand-filter',
                useTabSeparatedCopy: true, // 使用制表符分隔的复制格式，适合Excel粘贴
                onFilterChange: (filteredData) => {
                    this.displayFilteredResults(filteredData);
                }
            });
        }
    },

    // 处理表单提交
    async handleFormSubmit(event) {
        event.preventDefault();

        // 检查ELECTRON权限
        if (!this.checkElectronAccessForQuery()) {
            return;
        }

        const formData = new FormData(event.target);
        let queryParams;
        let apiEndpoint;

        if (this.currentMode === 'brand') {
            // 品牌查询模式
            queryParams = {
                code: formData.get('code') || null,
                name: formData.get('name') || null,
                status: formData.get('status') || null,
                current: 1,
                limit: 20
            };
            apiEndpoint = '/api/v1/data/brand';

            // 验证至少填写品牌编号或品牌名称中的一个
            const hasCode = queryParams.code && queryParams.code.trim() !== '';
            const hasName = queryParams.name && queryParams.name.trim() !== '';

            if (!hasCode && !hasName) {
                authManager.showNotification('请至少填写"品牌编号"或"品牌名称"中的一个字段', 'warning');
                return;
            }
        } else {
            // 审核查询模式
            queryParams = {
                name: formData.get('approval_name') || null,
                approval_status: formData.get('approval_status') || null,
                current: 1,
                limit: 20
            };
            apiEndpoint = '/api/v1/data/brand/approval';

        }

        // 保存查询条件到本地存储
        this.saveQueryConditionsToStorage({
            code: queryParams.code,
            name: queryParams.name,
            status: queryParams.status
        });

        // 使用按钮管理器包装查询操作
        const queryButton = document.getElementById('brand-submit-btn');
        const loadingText = this.currentMode === 'brand' ? '查询品牌库中...' : '查询审核列表中...';

        try {
            await buttonManager.wrapAsync(queryButton, async () => {
                // 执行查询
                await this.executeQuery(queryParams, apiEndpoint);
            }, loadingText);

        } catch (error) {
            console.error('品牌库查询错误:', error);
            authManager.showNotification('查询过程中发生错误', 'error');
        }
    },

    // 处理表单重置
    handleFormReset() {
        const form = document.getElementById('brand-form');
        if (form) {
            form.reset();
        }

        // 清除本地存储的查询条件
        this.clearQueryConditionsFromStorage();

        // 隐藏结果容器
        const resultsContainer = document.getElementById('results-container');
        const filterContainer = document.getElementById('data-filter-container');
        
        if (resultsContainer) resultsContainer.style.display = 'none';
        if (filterContainer) filterContainer.style.display = 'none';

        authManager.showNotification('查询条件已重置', 'info');
    },

    // 检查ELECTRON访问权限（用于查询）
    checkElectronAccessForQuery() {
        if (typeof authManager === 'undefined' || !authManager.userInfo) {
            authManager.showNotification('请先登录', 'warning');
            return false;
        }

        if (!authManager.userInfo.electron_access) {
            authManager.showNotification('需要电子超市平台权限才能进行品牌库查询', 'error');
            return false;
        }

        return true;
    },

    // 执行查询
    async executeQuery(queryParams, apiEndpoint) {
        try {
            const response = await fetch(apiEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authManager.token}`
                },
                body: JSON.stringify(queryParams)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.success) {
                this.displayResults(result.data, result.total);
                authManager.showNotification(result.message, 'success');

                // 记录查询统计
                if (typeof dashboardStats !== 'undefined') {
                    dashboardStats.recordQuery('brand', true);
                }
            } else {
                if (result.permission_error) {
                    authManager.showNotification(result.message, 'error');
                    // 可能需要重新登录
                    setTimeout(() => {
                        authManager.checkAuthStatus();
                    }, 2000);
                } else {
                    authManager.showNotification(result.message, 'warning');
                }
                this.hideResults();

                // 记录查询统计（失败）
                if (typeof dashboardStats !== 'undefined') {
                    dashboardStats.recordQuery('brand', false);
                }
            }

        } catch (error) {
            console.error('品牌库查询请求失败:', error);
            authManager.showNotification('网络请求失败，请稍后重试', 'error');
            this.hideResults();

            // 记录查询统计（异常）
            if (typeof dashboardStats !== 'undefined') {
                dashboardStats.recordQuery('brand', false);
            }
        }
    },

    // 显示查询结果
    displayResults(data) {
        if (!data || data.length === 0) {
            this.showEmptyResults();
            return;
        }

        // 根据当前模式定义表格列配置
        let columns;
        if (this.currentMode === 'brand') {
            columns = [
                { key: 'logoUrl', label: 'Logo', sortable: false },
                { key: 'code', label: '品牌编号', sortable: true },
                { key: 'name', label: '品牌名称', sortable: true },
                { key: 'nameEn', label: '英文名称', sortable: true },
                { key: 'logoOwner', label: '商标持有人', sortable: true },
                { key: 'brandOriginDisplay', label: '品牌类型', sortable: true },
                { key: 'statusDisplay', label: '状态', sortable: true }
            ];
        } else {
            columns = [
                { key: 'logoUrl', label: 'Logo', sortable: false },
                { key: 'name', label: '品牌名称', sortable: true },
                { key: 'nameEn', label: '英文名称', sortable: true },
                { key: 'logoOwner', label: '商标持有人', sortable: true },
                { key: 'brandOriginDisplay', label: '品牌类型', sortable: true },
                { key: 'approvalStatusDisplay', label: '审核状态', sortable: true }
            ];
        }

        // 设置筛选器数据（不预处理Logo HTML）
        dataFilter.setData(data, columns);

        // 显示筛选结果
        this.displayFilteredResults(data);

        // 确保筛选器面板正确更新
        setTimeout(() => {
            if (dataFilter && dataFilter.updateStats) {
                dataFilter.updateStats();
            }
        }, 100);
    },

    // 自定义表格渲染器
    renderCustomTable(data, filterInstance) {
        if (!data || data.length === 0) {
            return `
                <div class="empty">
                    <div class="empty-img">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDEyOCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik02NCA5NkM4MC41Njg1IDk2IDk0IDgyLjU2ODUgOTQgNjZDOTQgNDkuNDMxNSA4MC41Njg1IDM2IDY0IDM2QzQ3LjQzMTUgMzYgMzQgNDkuNDMxNSAzNCA2NkMzNCA4Mi41Njg1IDQ3LjQzMTUgOTYgNjQgOTZaIiBzdHJva2U9IiNEQURERTIiIHN0cm9rZS13aWR0aD0iMiIvPgo8cGF0aCBkPSJNNTcuMzMzMyA2NkM1Ny4zMzMzIDYyLjMxODEgNjAuMzE4MSA1OS4zMzMzIDY0IDU5LjMzMzNDNjcuNjgxOSA1OS4zMzMzIDcwLjY2NjcgNjIuMzE4MSA3MC42NjY3IDY2QzcwLjY2NjcgNjkuNjgxOSA2Ny42ODE5IDcyLjY2NjcgNjQgNzIuNjY2N0M2MC4zMTgxIDcyLjY2NjcgNTcuMzMzMyA2OS42ODE5IDU3LjMzMzMgNjZaIiBmaWxsPSIjREFEREUyIi8+Cjwvc3ZnPgo=" alt="暂无数据">
                    </div>
                    <p class="empty-title">暂无品牌数据</p>
                    <p class="empty-subtitle text-muted">请调整查询条件或筛选条件后重试</p>
                </div>
            `;
        }

        // 根据当前模式生成表头
        let headerHTML;
        if (this.currentMode === 'brand') {
            headerHTML = `
                <th class="logo-column">Logo</th>
                <th class="code-column">品牌编号</th>
                <th class="name-column">品牌名称</th>
                <th>英文名称</th>
                <th class="owner-column">商标持有人</th>
                <th class="origin-column">品牌类型</th>
                <th class="status-column">状态</th>
            `;
        } else {
            headerHTML = `
                <th class="logo-column">Logo</th>
                <th class="name-column">品牌名称</th>
                <th>英文名称</th>
                <th class="owner-column">商标持有人</th>
                <th class="origin-column">品牌类型</th>
                <th class="status-column">审核状态</th>
            `;
        }

        // 生成带选择功能的表格HTML
        let tableHTML = `
            <div class="table-responsive">
                <table class="table table-vcenter brand-table">
                    <thead>
                        <tr>
                            ${headerHTML}
                        </tr>
                    </thead>
                    <tbody>
        `;

        data.forEach((item, index) => {
            const isSelected = filterInstance.selectedRows.has(index);

            // 品牌类型徽章
            const originBadge = item.brandOrigin === 'DOMESTIC'
                ? '<span>国产</span>'
                : '<span>进口</span>';

            // 根据模式生成不同的行内容
            let rowContent;
            if (this.currentMode === 'brand') {
                // 品牌查询模式的状态徽章
                const statusBadge = item.status === 'ENABLE'
                    ? '<span class="badge bg-success">启用</span>'
                    : '<span class="badge bg-danger">禁用</span>';

                rowContent = `
                    <td class="logo-column">${this.renderLogo(item.logoUrl, item.name)}</td>
                    <td class="code-column"><strong>${this.escapeHtml(item.code || '')}</strong></td>
                    <td class="name-column">
                        <div class="text-truncate" style="max-width: 150px;" title="${this.escapeHtml(item.name || '')}">
                            ${this.escapeHtml(item.name || '')}
                        </div>
                    </td>
                    <td>
                        <div class="text-truncate" style="max-width: 120px;" title="${this.escapeHtml(item.nameEn || '')}">
                            ${this.escapeHtml(item.nameEn || '')}
                        </div>
                    </td>
                    <td class="owner-column">
                        <div class="text-truncate" style="max-width: 200px;" title="${this.escapeHtml(item.logoOwner || '')}">
                            ${this.escapeHtml(item.logoOwner || '')}
                        </div>
                    </td>
                    <td class="origin-column">${originBadge}</td>
                    <td class="status-column">${statusBadge}</td>
                `;
            } else {
                // 审核查询模式的审核状态徽章
                let approvalBadge = '';
                if (item.approvalStatus === 'APPROVE') {
                    approvalBadge = '<span class="badge bg-warning">待审核</span>';
                } else if (item.approvalStatus === 'REJECT') {
                    approvalBadge = '<span class="badge bg-danger">驳回</span>';
                } else if (item.approvalStatus === 'APPROVED') {
                    approvalBadge = '<span class="badge bg-success">审核通过</span>';
                } else {
                    approvalBadge = '<span class="badge bg-secondary">未知</span>';
                }

                rowContent = `
                    <td class="logo-column">${this.renderLogo(item.logoUrl, item.name)}</td>
                    <td class="name-column">
                        <div class="text-truncate" style="max-width: 150px;" title="${this.escapeHtml(item.name || '')}">
                            ${this.escapeHtml(item.name || '')}
                        </div>
                    </td>
                    <td>
                        <div class="text-truncate" style="max-width: 120px;" title="${this.escapeHtml(item.nameEn || '')}">
                            ${this.escapeHtml(item.nameEn || '')}
                        </div>
                    </td>
                    <td class="owner-column">
                        <div class="text-truncate" style="max-width: 200px;" title="${this.escapeHtml(item.logoOwner || '')}">
                            ${this.escapeHtml(item.logoOwner || '')}
                        </div>
                    </td>
                    <td class="origin-column">${originBadge}</td>
                    <td class="status-column">${approvalBadge}</td>
                `;
            }

            tableHTML += `
                <tr data-row-index="${index}" class="${isSelected ? 'table-active' : ''}"
                    onclick="BrandModule.handleRowClick(${index}, event)"
                    style="cursor: pointer;">
                    ${rowContent}
                </tr>
            `;
        });

        tableHTML += `
                    </tbody>
                </table>
            </div>
        `;

        // 更新选择状态UI
        setTimeout(() => {
            filterInstance.updateAllRowSelectionUI();
        }, 0);

        return tableHTML;
    },

    // 处理行点击事件
    handleRowClick(rowIndex, event) {
        if (dataFilter) {
            dataFilter.toggleRowSelection(rowIndex, event);
        }
    },

    // 渲染Logo
    renderLogo(logoUrl, brandName) {
        if (!logoUrl) {
            return `<div class="brand-logo-container">
                <div class="brand-logo-placeholder">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="#6c757d" fill="none" stroke-linecap="round" stroke-linejoin="round" aria-label="无Logo">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M15 8h.01"></path>
                        <path d="M3 6a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3v-12z"></path>
                        <path d="M3 16l5 -5c.928 -.893 2.072 -.893 3 0l5 5"></path>
                        <path d="M14 14l1 -1c.928 -.893 2.072 -.893 3 0l3 3"></path>
                    </svg>
                </div>
            </div>`;
        }

        // 转义属性值以防止XSS
        const escapedUrl = this.escapeHtml(logoUrl);
        const escapedBrandName = this.escapeHtml(brandName || '品牌Logo');

        return `<div class="brand-logo-container">
            <img src="${escapedUrl}"
                 alt="${escapedBrandName}"
                 title="${escapedBrandName}"
        </div>`;
    },

    // 显示筛选后的结果
    displayFilteredResults(data) {
        const resultsContainer = document.getElementById('brand-results');
        const countElement = document.getElementById('brand-count');

        if (!resultsContainer || !countElement) {
            console.error('Required DOM elements not found!');
            return;
        }

        // 更新记录数量
        countElement.textContent = `${data.length} 条记录`;

        // 生成表格内容
        const tableHTML = dataFilter.generateSelectableTable(data, this.renderCustomTable.bind(this));
        resultsContainer.innerHTML = tableHTML;

        // 显示结果容器
        const resultsContainerParent = document.getElementById('results-container');
        const filterContainer = document.getElementById('data-filter-container');

        if (resultsContainerParent) resultsContainerParent.style.display = 'block';
        if (filterContainer) filterContainer.style.display = 'block';
    },

    // 显示空结果
    showEmptyResults() {
        const resultsContainer = document.getElementById('results-container');
        const filterContainer = document.getElementById('data-filter-container');
        
        if (resultsContainer) resultsContainer.style.display = 'none';
        if (filterContainer) filterContainer.style.display = 'none';
        
        authManager.showNotification('未找到符合条件的品牌信息', 'info');
    },

    // 隐藏结果
    hideResults() {
        const resultsContainer = document.getElementById('results-container');
        const filterContainer = document.getElementById('data-filter-container');
        
        if (resultsContainer) resultsContainer.style.display = 'none';
        if (filterContainer) filterContainer.style.display = 'none';
    },

    // 保存查询条件到本地存储
    saveQueryConditionsToStorage(conditions) {
        try {
            localStorage.setItem('brand-query-conditions', JSON.stringify(conditions));
        } catch (error) {
            console.warn('无法保存品牌库查询条件到本地存储:', error);
        }
    },

    // 从本地存储加载查询条件
    loadQueryConditionsFromStorage() {
        try {
            const saved = localStorage.getItem('brand-query-conditions');
            if (saved) {
                const conditions = JSON.parse(saved);
                
                // 恢复表单字段
                const codeInput = document.getElementById('brand-code');
                const nameInput = document.getElementById('brand-name');
                const statusSelect = document.getElementById('brand-status');
                
                if (codeInput && conditions.code) codeInput.value = conditions.code;
                if (nameInput && conditions.name) nameInput.value = conditions.name;
                if (statusSelect && conditions.status) statusSelect.value = conditions.status;
            }
        } catch (error) {
            console.warn('无法从本地存储加载品牌库查询条件:', error);
        }
    },

    // 清除本地存储的查询条件
    clearQueryConditionsFromStorage() {
        try {
            localStorage.removeItem('brand-query-conditions');
        } catch (error) {
            console.warn('无法清除品牌库查询条件:', error);
        }
    },

    // HTML转义函数
    escapeHtml(text) {
        if (text === null || text === undefined) {
            return '';
        }
        const div = document.createElement('div');
        div.textContent = String(text);
        return div.innerHTML;
    },

    // ========== 新增品牌功能 ==========

    // 初始化新增品牌模态框
    initAddBrandModal() {
        // 绑定模态框事件
        this.bindAddBrandModalEvents();

        // 初始化文件上传功能
        this.initFileUpload();

        // 初始化内嵌式裁剪功能
        this.initInlineCrop();

        // 初始化表单验证
        this.initFormValidation();
    },

    // 绑定新增品牌模态框事件
    bindAddBrandModalEvents() {
        const modal = document.getElementById('add-brand-modal');
        const form = document.getElementById('add-brand-form');
        const submitBtn = document.getElementById('add-brand-submit-btn');

        if (!modal || !form || !submitBtn) {
            console.warn('新增品牌模态框元素未找到');
            return;
        }

        // 模态框显示时重置表单
        modal.addEventListener('show.bs.modal', () => {
            this.resetAddBrandForm();
        });

        // 表单提交事件
        submitBtn.addEventListener('click', (e) => {
            e.preventDefault();
            this.handleAddBrandSubmit();
        });

        // 表单字段变化时验证
        form.addEventListener('input', () => {
            this.validateAddBrandForm();
            this.updateFormProgress();
        });

        form.addEventListener('change', () => {
            this.validateAddBrandForm();
            this.updateFormProgress();
        });

        // 品牌原产地按钮组事件
        this.initBrandOriginButtons();

        // 初始化进度指示器
        this.initProgressIndicator();
    },

    // 初始化品牌原产地按钮组
    initBrandOriginButtons() {
        const brandOriginRadios = document.querySelectorAll('input[name="brand_origin"]');

        brandOriginRadios.forEach(radio => {
            radio.addEventListener('change', () => {
                // 触发表单验证
                this.validateAddBrandForm();
                // 更新进度
                this.updateFormProgress();
            });
        });
    },

    // 初始化进度指示器
    initProgressIndicator() {
        // 初始化进度状态
        this.progressStatus = {
            basic: false,
            logo: false,
            trademark: false
        };

        // 绑定进度步骤点击事件
        const progressSteps = document.querySelectorAll('.progress-step');
        progressSteps.forEach(step => {
            step.addEventListener('click', () => {
                const stepType = step.getAttribute('data-step');
                this.scrollToFormSection(stepType);
            });
        });

        // 初始更新进度
        this.updateFormProgress();
    },

    // 更新表单完成进度
    updateFormProgress() {
        // 检查基本信息完成状态
        this.progressStatus.basic = this.validateBasicInfo();

        // 检查LOGO上传状态
        this.progressStatus.logo = this.uploadStatus.logo.status === 'success';

        // 检查商标网截图上传状态
        this.progressStatus.trademark = this.uploadStatus.trademark.status === 'success';

        // 更新UI显示
        this.updateProgressUI();
    },

    // 更新进度UI显示
    updateProgressUI() {
        const steps = ['basic', 'logo', 'trademark'];

        steps.forEach((stepType, index) => {
            const stepElement = document.getElementById(`progress-step-${stepType}`);
            const lineElement = document.getElementById(`progress-line-${index + 1}`);

            if (this.progressStatus[stepType]) {
                stepElement?.classList.add('completed');
                lineElement?.classList.add('completed');
            } else {
                stepElement?.classList.remove('completed');
                lineElement?.classList.remove('completed');
            }
        });
    },

    // 滚动到指定表单区域
    scrollToFormSection(stepType) {
        let targetElement = null;

        switch (stepType) {
            case 'basic':
                targetElement = document.querySelector('#add-brand-form .col-12 h6');
                break;
            case 'logo':
                targetElement = document.getElementById('logo-upload-area');
                break;
            case 'trademark':
                targetElement = document.getElementById('trademark-upload-area');
                break;
        }

        if (targetElement) {
            targetElement.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    },

    // 初始化文件上传功能
    initFileUpload() {
        // 初始化LOGO上传
        this.initLogoUpload();

        // 初始化商标网截图上传
        this.initTrademarkUpload();
    },

    // 初始化LOGO上传
    initLogoUpload() {
        const uploadArea = document.getElementById('logo-upload-area');
        const fileInput = document.getElementById('logo-file-input');
        const previewImg = document.getElementById('logo-preview-img');
        const recropBtn = document.getElementById('logo-recrop-btn');
        const removeBtn = document.getElementById('logo-remove-btn');

        if (!uploadArea || !fileInput) return;

        // 点击上传区域触发文件选择
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        // 文件选择事件
        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                this.handleLogoFileSelect(file);
            }
        });

        // 拖拽上传
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const file = e.dataTransfer.files[0];
            if (file) {
                this.handleLogoFileSelect(file);
            }
        });

        // 重新裁剪按钮事件
        if (recropBtn) {
            recropBtn.addEventListener('click', () => {
                if (this.originalLogoFile) {
                    // 重新启动内嵌裁剪流程
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        this.showInlineCrop(e.target.result);
                    };
                    reader.readAsDataURL(this.originalLogoFile);
                } else {
                    // 如果没有原始文件，使用当前预览图片
                    this.showInlineCrop(previewImg.src);
                }
            });
        }

        // 删除按钮事件
        if (removeBtn) {
            removeBtn.addEventListener('click', () => {
                this.removeLogoFile();
            });
        }

        // 上传按钮事件
        const uploadBtn = document.getElementById('logo-upload-btn');
        if (uploadBtn) {
            uploadBtn.addEventListener('click', () => {
                this.uploadLogoFile();
            });
        }
    },

    // 初始化商标网截图上传
    initTrademarkUpload() {
        const uploadArea = document.getElementById('trademark-upload-area');
        const fileInput = document.getElementById('trademark-file-input');
        const removeBtn = document.getElementById('trademark-remove-btn');

        if (!uploadArea || !fileInput) return;

        // 点击上传区域触发文件选择
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        // 文件选择事件
        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                this.handleTrademarkFileSelect(file);
            }
        });

        // 拖拽上传事件
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const file = e.dataTransfer.files[0];
            if (file) {
                this.handleTrademarkFileSelect(file);
            }
        });

        // 删除按钮事件
        if (removeBtn) {
            removeBtn.addEventListener('click', () => {
                this.removeTrademarkFile();
            });
        }

        // 上传按钮事件
        const uploadBtn = document.getElementById('trademark-upload-btn');
        if (uploadBtn) {
            uploadBtn.addEventListener('click', () => {
                this.uploadTrademarkFile();
            });
        }
    },

    // 处理LOGO文件选择
    handleLogoFileSelect(file) {
        // 检查是否是从裁剪更新触发的，如果是则跳过
        if (this.isUpdatingFromCrop) {
            console.log('跳过裁剪更新触发的文件选择处理');
            return;
        }

        // 验证文件类型
        if (!this.validateImageFile(file)) {
            authManager.showNotification('请选择JPG或PNG格式的图片文件', 'warning');
            return;
        }

        // 验证文件大小（限制为5MB）
        if (file.size > 5 * 1024 * 1024) {
            authManager.showNotification('图片文件大小不能超过5MB', 'warning');
            return;
        }

        // 存储原始文件信息
        this.originalLogoFile = file;

        // 更新uploadStatus中的文件引用（重要：确保拖拽上传的文件能被上传方法识别）
        this.uploadStatus.logo.file = file;

        // 同步文件到文件输入框（用于兼容性）
        const fileInput = document.getElementById('logo-file-input');
        if (fileInput) {
            // 创建一个新的FileList对象来模拟文件选择
            const dataTransfer = new DataTransfer();
            dataTransfer.items.add(file);
            fileInput.files = dataTransfer.files;
        }

        // 读取文件并自动启动内嵌裁剪
        const reader = new FileReader();
        reader.onload = (e) => {
            const imageSrc = e.target.result;

            // 检查是否支持裁剪功能
            if (this.isCropperSupported()) {
                // 自动启动内嵌裁剪模式
                console.log('自动启动LOGO内嵌裁剪模式');
                this.showInlineCrop(imageSrc);
            } else {
                // 回退到预览模式
                console.log('Cropper.js不可用，回退到预览模式');
                authManager.showNotification('图片裁剪功能不可用，使用原图预览', 'warning');
                this.showLogoPreview(imageSrc);
            }
        };

        reader.onerror = () => {
            console.error('文件读取失败');
            authManager.showNotification('文件读取失败，请重试', 'error');
        };

        reader.readAsDataURL(file);
    },

    // 处理商标网截图文件选择
    handleTrademarkFileSelect(file) {
        // 验证文件类型
        if (!this.validateImageFile(file)) {
            authManager.showNotification('请选择JPG或PNG格式的图片文件', 'warning');
            return;
        }

        // 验证文件大小（限制为10MB）
        if (file.size > 10 * 1024 * 1024) {
            authManager.showNotification('图片文件大小不能超过10MB', 'warning');
            return;
        }

        // 更新uploadStatus中的文件引用（重要：确保拖拽上传的文件能被上传方法识别）
        this.uploadStatus.trademark.file = file;

        // 同步文件到文件输入框（用于兼容性）
        const fileInput = document.getElementById('trademark-file-input');
        if (fileInput) {
            // 创建一个新的FileList对象来模拟文件选择
            const dataTransfer = new DataTransfer();
            dataTransfer.items.add(file);
            fileInput.files = dataTransfer.files;
        }

        // 读取文件并显示预览
        const reader = new FileReader();
        reader.onload = (e) => {
            this.showTrademarkPreview(e.target.result);
        };
        reader.readAsDataURL(file);
    },

    // 验证图片文件
    validateImageFile(file) {
        const allowedTypes = ['image/jpeg', 'image/png'];
        return allowedTypes.includes(file.type);
    },

    // 检查Cropper.js是否支持
    isCropperSupported() {
        return typeof Cropper !== 'undefined';
    },

    // 显示LOGO预览
    showLogoPreview(imageSrc) {
        const uploadArea = document.getElementById('logo-upload-area');
        const preview = document.getElementById('logo-preview');
        const previewImg = document.getElementById('logo-preview-img');

        if (uploadArea && preview && previewImg) {
            previewImg.src = imageSrc;
            uploadArea.style.display = 'none';
            preview.style.display = 'block';
        }
    },

    // 显示商标网截图预览
    showTrademarkPreview(imageSrc) {
        const uploadArea = document.getElementById('trademark-upload-area');
        const previewArea = document.getElementById('trademark-preview-area');
        const previewImg = document.getElementById('trademark-preview-img');

        if (uploadArea && previewArea && previewImg) {
            previewImg.src = imageSrc;
            uploadArea.style.display = 'none';
            previewArea.style.display = 'block';
        }
    },

    // 删除LOGO文件
    removeLogoFile() {
        // 销毁裁剪器实例
        if (this.cropper) {
            this.cropper.destroy();
            this.cropper = null;
        }

        // 清理原始文件引用
        this.originalLogoFile = null;

        // 重置上传状态
        this.uploadStatus.logo = {
            status: 'ready',
            fileId: null,
            fileUrl: null,
            file: null
        };

        // 重置文件输入框
        const fileInput = document.getElementById('logo-file-input');
        if (fileInput) {
            fileInput.value = '';
        }

        // 重置上传按钮状态
        this.resetUploadButtonState('logo');

        // 显示上传界面
        this.showLogoUpload();

        // 更新表单验证状态
        this.updateFormValidation();

        console.log('LOGO文件已删除，状态已重置');
    },

    // 删除商标网截图文件
    removeTrademarkFile() {
        // 重置上传状态
        this.uploadStatus.trademark = {
            status: 'ready',
            fileId: null,
            fileUrl: null,
            file: null
        };

        const uploadArea = document.getElementById('trademark-upload-area');
        const previewArea = document.getElementById('trademark-preview-area');
        const fileInput = document.getElementById('trademark-file-input');

        if (uploadArea && previewArea && fileInput) {
            uploadArea.style.display = 'flex';
            previewArea.style.display = 'none';
            fileInput.value = '';
        }

        // 重置上传按钮状态
        this.resetUploadButtonState('trademark');

        // 更新表单验证状态
        this.updateFormValidation();

        console.log('商标网截图文件已删除，状态已重置');
    },

    // 显示商标网截图上传界面
    showTrademarkUpload() {
        const uploadArea = document.getElementById('trademark-upload-area');
        const previewArea = document.getElementById('trademark-preview-area');

        if (uploadArea && previewArea) {
            uploadArea.style.display = 'flex';
            previewArea.style.display = 'none';
        }
    },

    // 初始化内嵌式裁剪功能
    initInlineCrop() {
        const cropConfirmBtn = document.getElementById('logo-crop-confirm');
        const cropCancelBtn = document.getElementById('logo-crop-cancel');
        const zoomInBtn = document.getElementById('logo-zoom-in');
        const zoomOutBtn = document.getElementById('logo-zoom-out');
        const resetBtn = document.getElementById('logo-reset');

        // 裁剪确认按钮事件
        if (cropConfirmBtn) {
            cropConfirmBtn.addEventListener('click', () => {
                this.confirmInlineCrop();
            });
        }

        // 裁剪取消按钮事件
        if (cropCancelBtn) {
            cropCancelBtn.addEventListener('click', () => {
                this.cancelInlineCrop();
            });
        }

        // 放大按钮事件
        if (zoomInBtn) {
            zoomInBtn.addEventListener('click', () => {
                this.zoomInlineCropper(0.1);
            });
        }

        // 缩小按钮事件
        if (zoomOutBtn) {
            zoomOutBtn.addEventListener('click', () => {
                this.zoomInlineCropper(-0.1);
            });
        }

        // 重置按钮事件
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.resetInlineCropper();
            });
        }
    },

    // 显示内嵌裁剪界面
    showInlineCrop(imageSrc) {
        console.log('显示内嵌裁剪界面');

        // 获取各个界面元素
        const uploadArea = document.getElementById('logo-upload-area');
        const cropArea = document.getElementById('logo-crop-area');
        const previewArea = document.getElementById('logo-preview-area');
        const cropImage = document.getElementById('logo-crop-image');

        if (!uploadArea || !cropArea || !cropImage) {
            console.error('内嵌裁剪界面元素未找到');
            return;
        }

        // 隐藏上传和预览界面，显示裁剪界面
        uploadArea.style.display = 'none';
        previewArea.style.display = 'none';
        cropArea.style.display = 'block';

        // 设置图片源
        cropImage.src = imageSrc;

        // 等待图片加载完成后初始化裁剪器
        cropImage.onload = () => {
            this.initInlineCropper(cropImage);
        };

        if (cropImage.complete) {
            this.initInlineCropper(cropImage);
        }
    },



    // 初始化内嵌裁剪器
    initInlineCropper(imageElement) {
        console.log('开始初始化内嵌裁剪器...');

        // 检查Cropper.js是否可用
        if (typeof Cropper === 'undefined') {
            console.error('Cropper.js 库未加载');
            authManager.showNotification('图片裁剪功能不可用', 'error');
            return;
        }

        // 销毁之前的裁剪器实例（如果存在）
        if (this.cropper) {
            console.log('销毁之前的裁剪器实例');
            this.cropper.destroy();
            this.cropper = null;
        }

        try {
            console.log('正在创建内嵌Cropper实例...');

            // 初始化Cropper.js
            this.cropper = new Cropper(imageElement, {
                // 强制保持102:36的宽高比例（约2.83:1）
                aspectRatio: 102 / 36,

                // 裁剪框配置
                viewMode: 1, // 限制裁剪框在画布内
                dragMode: 'move', // 拖拽模式：移动图片
                autoCropArea: 0.8, // 自动裁剪区域占比
                restore: false, // 禁用窗口大小调整时恢复裁剪框
                guides: true, // 显示网格线
                center: true, // 显示中心指示器
                highlight: true, // 高亮裁剪区域
                cropBoxMovable: true, // 允许移动裁剪框
                cropBoxResizable: true, // 允许调整裁剪框大小
                toggleDragModeOnDblclick: false, // 禁用双击切换拖拽模式

                // 响应式配置
                responsive: true,
                checkOrientation: false,

                // 最小裁剪框尺寸
                minCropBoxWidth: 50,
                minCropBoxHeight: 18,

                // 事件回调
                ready: () => {
                    console.log('内嵌Cropper.js 初始化完成');
                },

                cropstart: () => {
                    console.log('开始裁剪操作');
                },

                cropmove: () => {
                    // 裁剪过程中的实时反馈
                    this.updateInlineCropInfo();
                },

                cropend: () => {
                    console.log('裁剪操作结束');
                    this.updateInlineCropInfo();
                }
            });

            console.log('内嵌Cropper实例创建成功');

        } catch (error) {
            console.error('创建内嵌Cropper实例失败:', error);
            authManager.showNotification('图片裁剪器初始化失败', 'error');
        }
    },

    // 更新内嵌裁剪信息显示
    updateInlineCropInfo() {
        if (!this.cropper) return;

        const cropBoxData = this.cropper.getCropBoxData();
        const canvasData = this.cropper.getCanvasData();

        // 计算实际裁剪尺寸
        const scaleX = canvasData.naturalWidth / canvasData.width;
        const scaleY = canvasData.naturalHeight / canvasData.height;

        const actualWidth = Math.round(cropBoxData.width * scaleX);
        const actualHeight = Math.round(cropBoxData.height * scaleY);
        const actualRatio = (actualWidth / actualHeight).toFixed(2);

        // 更新信息显示
        const infoElement = document.querySelector('.logo-crop-info');
        if (infoElement) {
            infoElement.innerHTML = `<small class="text-muted">尺寸: ${actualWidth}×${actualHeight}</small>`;
        }

        console.log(`内嵌裁剪区域: ${actualWidth}×${actualHeight}, 比例: ${actualRatio}:1`);
    },

    // 缩放内嵌裁剪器
    zoomInlineCropper(ratio) {
        if (!this.cropper) return;
        this.cropper.zoom(ratio);
    },

    // 重置内嵌裁剪器
    resetInlineCropper() {
        if (!this.cropper) return;
        this.cropper.reset();
        authManager.showNotification('已重置到初始状态', 'info');
    },

    // 确认内嵌裁剪
    confirmInlineCrop() {
        console.log('确认内嵌裁剪');

        if (!this.cropper) {
            console.error('裁剪器实例不存在');
            authManager.showNotification('裁剪器未初始化', 'error');
            return;
        }

        try {
            // 获取裁剪后的画布，并设置输出尺寸为102×36
            const canvas = this.cropper.getCroppedCanvas({
                width: 102,
                height: 36,
                imageSmoothingEnabled: true,
                imageSmoothingQuality: 'high'
            });

            if (!canvas) {
                authManager.showNotification('裁剪失败，请重试', 'error');
                return;
            }

            // 将画布转换为Blob对象
            canvas.toBlob((blob) => {
                if (!blob) {
                    authManager.showNotification('图片处理失败，请重试', 'error');
                    return;
                }

                // 创建新的File对象
                const fileName = `logo_cropped_${Date.now()}.png`;
                const croppedFile = new File([blob], fileName, {
                    type: 'image/png',
                    lastModified: Date.now()
                });

                // 更新文件输入框
                this.updateLogoFileInput(croppedFile);

                // 清理原始文件引用
                this.originalLogoFile = null;

                // 显示预览界面
                this.showLogoPreview(canvas.toDataURL());

                // 显示成功消息
                authManager.showNotification('LOGO裁剪完成！', 'success');

            }, 'image/png', 0.9);

        } catch (error) {
            console.error('内嵌裁剪过程中发生错误:', error);
            authManager.showNotification('裁剪过程中发生错误，请重试', 'error');
        }
    },

    // 取消内嵌裁剪
    cancelInlineCrop() {
        console.log('取消内嵌裁剪');

        // 销毁裁剪器实例
        if (this.cropper) {
            this.cropper.destroy();
            this.cropper = null;
        }

        // 清理原始文件引用
        this.originalLogoFile = null;

        // 重置文件输入框
        const fileInput = document.getElementById('logo-file-input');
        if (fileInput) {
            fileInput.value = '';
        }

        // 显示上传界面
        this.showLogoUpload();

        authManager.showNotification('已取消LOGO上传', 'info');
    },

    // 显示LOGO上传界面
    showLogoUpload() {
        const uploadArea = document.getElementById('logo-upload-area');
        const cropArea = document.getElementById('logo-crop-area');
        const previewArea = document.getElementById('logo-preview-area');

        if (uploadArea && cropArea && previewArea) {
            uploadArea.style.display = 'flex';
            cropArea.style.display = 'none';
            previewArea.style.display = 'none';
        }
    },

    // 显示LOGO预览界面
    showLogoPreview(imageSrc) {
        const uploadArea = document.getElementById('logo-upload-area');
        const cropArea = document.getElementById('logo-crop-area');
        const previewArea = document.getElementById('logo-preview-area');
        const previewImg = document.getElementById('logo-preview-img');

        if (uploadArea && cropArea && previewArea && previewImg) {
            // 销毁裁剪器实例
            if (this.cropper) {
                this.cropper.destroy();
                this.cropper = null;
            }

            uploadArea.style.display = 'none';
            cropArea.style.display = 'none';
            previewArea.style.display = 'block';
            previewImg.src = imageSrc;
        }
    },





    // 更新LOGO文件输入框
    updateLogoFileInput(file) {
        const fileInput = document.getElementById('logo-file-input');

        if (fileInput) {
            // 设置标志，防止重复触发裁剪
            this.isUpdatingFromCrop = true;

            // 创建新的FileList（模拟文件选择）
            const dataTransfer = new DataTransfer();
            dataTransfer.items.add(file);
            fileInput.files = dataTransfer.files;

            // 延迟清除标志，确保不会重复触发
            setTimeout(() => {
                this.isUpdatingFromCrop = false;
            }, 100);

            console.log('LOGO文件已更新:', file.name, file.size, 'bytes');
        }
    },





    // 初始化表单验证
    initFormValidation() {
        const form = document.getElementById('add-brand-form');
        if (!form) return;

        // 为必填字段添加验证
        const requiredFields = form.querySelectorAll('[required]');
        requiredFields.forEach(field => {
            field.addEventListener('blur', () => {
                this.validateField(field);
                this.updateFormValidation(); // 触发表单验证更新
            });
            field.addEventListener('input', () => {
                // 实时更新表单验证状态
                setTimeout(() => this.updateFormValidation(), 100);
            });
        });

        // 为品牌名称字段添加特殊验证（二选一必填）
        const brandNameCn = document.getElementById('brand-name-cn');
        const brandNameEn = document.getElementById('brand-name-en');

        if (brandNameCn && brandNameEn) {
            brandNameCn.addEventListener('input', () => {
                this.validateBrandNames();
                this.updateFormValidation(); // 触发表单验证更新
            });
            brandNameEn.addEventListener('input', () => {
                this.validateBrandNames();
                this.updateFormValidation(); // 触发表单验证更新
            });
        }

        // 为国际分类选择添加验证
        const internationalClassification = document.getElementById('international-classification');
        if (internationalClassification) {
            internationalClassification.addEventListener('change', () => {
                this.updateFormValidation(); // 触发表单验证更新
            });
        }

        // 初始化表单验证状态
        this.updateFormValidation();
    },

    // 验证单个字段
    validateField(field) {
        const value = field.value.trim();
        const isValid = value !== '';

        if (isValid) {
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
        } else {
            field.classList.remove('is-valid');
            field.classList.add('is-invalid');

            const feedback = field.nextElementSibling;
            if (feedback && feedback.classList.contains('invalid-feedback')) {
                feedback.textContent = '此字段为必填项';
            }
        }

        return isValid;
    },

    // 验证品牌名称（二选一必填）
    validateBrandNames() {
        const brandNameCn = document.getElementById('brand-name-cn');
        const brandNameEn = document.getElementById('brand-name-en');

        if (!brandNameCn || !brandNameEn) return true;

        const cnValue = brandNameCn.value.trim();
        const enValue = brandNameEn.value.trim();
        const isValid = cnValue !== '' || enValue !== '';

        // 清除之前的验证状态
        [brandNameCn, brandNameEn].forEach(field => {
            field.classList.remove('is-valid', 'is-invalid');
        });

        if (!isValid) {
            // 如果两个都为空，标记为无效
            [brandNameCn, brandNameEn].forEach(field => {
                field.classList.add('is-invalid');
                const feedback = field.nextElementSibling;
                if (feedback && feedback.classList.contains('invalid-feedback')) {
                    feedback.textContent = '品牌中文名称和英文名称至少填写一个';
                }
            });
        } else {
            // 如果至少有一个有值，标记为有效
            if (cnValue !== '') brandNameCn.classList.add('is-valid');
            if (enValue !== '') brandNameEn.classList.add('is-valid');
        }

        return isValid;
    },

    // 验证整个表单
    validateAddBrandForm() {
        const form = document.getElementById('add-brand-form');
        if (!form) return false;

        let isValid = true;

        // 验证品牌名称（二选一必填）
        if (!this.validateBrandNames()) {
            isValid = false;
        }

        // 验证必填字段
        const requiredFields = form.querySelectorAll('[required]');
        requiredFields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });

        // 更新提交按钮状态
        const submitBtn = document.getElementById('add-brand-submit-btn');
        if (submitBtn) {
            submitBtn.disabled = !isValid;
        }

        return isValid;
    },

    // 重置新增品牌表单
    resetAddBrandForm() {
        const form = document.getElementById('add-brand-form');
        if (!form) return;

        // 重置表单
        form.reset();

        // 清除验证状态
        const fields = form.querySelectorAll('.form-control, .form-select, .btn-check');
        fields.forEach(field => {
            field.classList.remove('is-valid', 'is-invalid');
        });

        // 重置文件上传
        this.removeLogoFile();
        this.removeTrademarkFile();

        // 重置上传状态
        this.resetUploadStatus();

        // 重置提交按钮状态
        const submitBtn = document.getElementById('add-brand-submit-btn');
        if (submitBtn) {
            submitBtn.disabled = true; // 默认禁用，需要完成所有必填项
            submitBtn.classList.remove('btn-primary', 'btn-success', 'btn-danger');
            submitBtn.classList.add('btn-secondary');
        }

        // 重置进度指示器
        this.updateFormProgress();
    },

    // 处理新增品牌表单提交
    async handleAddBrandSubmit() {
        // 验证表单
        if (!this.validateAddBrandForm()) {
            authManager.showNotification('请完善表单信息', 'warning');
            return;
        }

        const form = document.getElementById('add-brand-form');
        const submitBtn = document.getElementById('add-brand-submit-btn');

        if (!form || !submitBtn) return;

        try {
            // 验证表单是否完整
            if (!this.validateAddBrandForm()) {
                authManager.showNotification('请完善表单信息后再提交', 'warning');
                return;
            }

            // 使用按钮管理器包装提交操作
            await buttonManager.wrapAsync(submitBtn, async () => {
                // 收集表单数据
                const formData = this.collectAddBrandFormData();

                // 调用真实API提交
                await this.simulateAddBrandSubmit(formData);

            }, '提交中...');

        } catch (error) {
            console.error('新增品牌提交错误:', error);
            authManager.showNotification('提交过程中发生错误', 'error');
        }
    },

    // 收集新增品牌表单数据
    collectAddBrandFormData() {
        const form = document.getElementById('add-brand-form');
        if (!form) return {};

        const formData = new FormData(form);
        const data = {};

        // 收集基础字段
        for (let [key, value] of formData.entries()) {
            if (key !== 'logo_file' && key !== 'trademark_file') {
                data[key] = value;
            }
        }

        // 收集多选字段
        const classificationSelect = document.getElementById('international-classification');
        if (classificationSelect) {
            const selectedOptions = Array.from(classificationSelect.selectedOptions);
            data.international_classification = selectedOptions.map(option => option.value);
        }

        // 收集上传成功的文件ID信息
        if (this.uploadStatus.logo.status === 'success' && this.uploadStatus.logo.fileId) {
            data.logo_file_id = this.uploadStatus.logo.fileId;
            data.logo_file_url = this.uploadStatus.logo.fileUrl;
            data.logo_file_name = this.uploadStatus.logo.fileName;
        }

        if (this.uploadStatus.trademark.status === 'success' && this.uploadStatus.trademark.fileId) {
            data.trademark_file_id = this.uploadStatus.trademark.fileId;
            data.trademark_file_url = this.uploadStatus.trademark.fileUrl;
            data.trademark_file_name = this.uploadStatus.trademark.fileName;
        }

        return data;
    },

    // 新增品牌提交
    async simulateAddBrandSubmit(formData) {
        try {
            // 验证必填字段
            if (!formData.brand_name_cn && !formData.brand_name_en) {
                authManager.showNotification('品牌中文名称或英文名称至少填写一个', 'error');
                return;
            }

            if (!formData.trademark_holder) {
                authManager.showNotification('商标持有人为必填项', 'error');
                return;
            }

            if (!formData.brand_origin) {
                authManager.showNotification('品牌原产地为必选项', 'error');
                return;
            }

            if (!formData.international_classification || formData.international_classification.length === 0) {
                authManager.showNotification('国际分类为必选项', 'error');
                return;
            }

            // 验证文件上传状态
            if (this.uploadStatus.logo.status !== 'success') {
                authManager.showNotification('请先上传LOGO文件', 'error');
                return;
            }

            if (this.uploadStatus.trademark.status !== 'success') {
                authManager.showNotification('请先上传商标网截图', 'error');
                return;
            }

            // 构建请求数据
            const requestData = {
                brand_name_cn: formData.brand_name_cn || null,
                brand_name_en: formData.brand_name_en || null,
                trademark_holder: formData.trademark_holder,
                brand_origin: formData.brand_origin,
                international_classification: formData.international_classification,
                logo_file_id: this.uploadStatus.logo.fileId,
                logo_file_url: this.uploadStatus.logo.fileUrl,
                trademark_file_id: this.uploadStatus.trademark.fileId
            };

            console.log('提交品牌数据:', requestData);

            // 调用新增品牌API
            const response = await fetch('/api/v1/data/brand/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authManager.token}`
                },
                body: JSON.stringify(requestData)
            });

            const result = await response.json();

            if (result.success) {
                // 提交成功
                authManager.showNotification('品牌新增成功！', 'success');

                // 关闭模态框
                const modal = document.getElementById('add-brand-modal');
                if (modal) {
                    const modalInstance = bootstrap.Modal.getInstance(modal);
                    if (modalInstance) {
                        modalInstance.hide();
                    }
                }

                // 重置表单
                this.resetAddBrandForm();

                console.log('品牌新增成功，业务ID:', result.data?.bizId);
            } else {
                // 提交失败
                if (result.permission_error) {
                    authManager.showNotification(result.message || '权限不足', 'error');
                    // 可能需要重新登录
                    if (result.message && result.message.includes('重新登录')) {
                        setTimeout(() => {
                            authManager.logout();
                        }, 2000);
                    }
                } else {
                    authManager.showNotification(result.message || '品牌新增失败', 'error');
                }
            }

        } catch (error) {
            console.error('新增品牌API调用失败:', error);

            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                authManager.showNotification('网络连接失败，请检查网络设置', 'error');
            } else {
                authManager.showNotification('新增过程中发生错误，请稍后重试', 'error');
            }
        }
    },

    // 上传LOGO文件
    async uploadLogoFile() {
        const fileInput = document.getElementById('logo-file-input');

        // 优先从文件输入框获取文件，如果没有则从uploadStatus获取（支持拖拽上传）
        let file = null;
        if (fileInput && fileInput.files[0]) {
            file = fileInput.files[0];
        } else if (this.uploadStatus.logo.file) {
            file = this.uploadStatus.logo.file;
        }

        if (!file) {
            authManager.showNotification('请先选择LOGO文件', 'warning');
            return;
        }

        this.uploadStatus.logo.file = file;

        try {
            // 更新上传状态为上传中
            this.updateUploadStatus('logo', 'uploading');

            // 调用通用上传方法
            const result = await this.uploadImageFile(file, 'logo');

            if (result.success) {
                // 上传成功，保存文件信息
                this.uploadStatus.logo.status = 'success';
                this.uploadStatus.logo.fileId = result.fileId;
                this.uploadStatus.logo.fileUrl = result.fileUrl;
                this.uploadStatus.logo.fileName = result.fileName;
                this.uploadStatus.logo.fileSize = result.fileSize;

                // 更新UI状态显示
                this.updateUploadStatus('logo', 'success');
                this.updateFormValidation();
                this.updateFormProgress();
                authManager.showNotification('LOGO上传成功！', 'success');
                console.log('LOGO上传成功，文件信息:', {
                    fileId: result.fileId,
                    fileName: result.fileName,
                    fileUrl: result.fileUrl
                });
            } else {
                // 上传失败
                this.uploadStatus.logo.status = 'error';
                this.updateUploadStatus('logo', 'error');
                this.updateFormProgress();
                authManager.showNotification(result.message || 'LOGO上传失败', 'error');
            }
        } catch (error) {
            console.error('LOGO上传过程中发生错误:', error);
            this.uploadStatus.logo.status = 'error';
            this.updateUploadStatus('logo', 'error');
            this.updateFormProgress();
            authManager.showNotification('LOGO上传过程中发生错误', 'error');
        }
    },

    // 上传商标网截图文件
    async uploadTrademarkFile() {
        const fileInput = document.getElementById('trademark-file-input');

        // 优先从文件输入框获取文件，如果没有则从uploadStatus获取（支持拖拽上传）
        let file = null;
        if (fileInput && fileInput.files[0]) {
            file = fileInput.files[0];
        } else if (this.uploadStatus.trademark.file) {
            file = this.uploadStatus.trademark.file;
        }

        if (!file) {
            authManager.showNotification('请先选择商标网截图文件', 'warning');
            return;
        }

        this.uploadStatus.trademark.file = file;

        try {
            // 更新上传状态为上传中
            this.updateUploadStatus('trademark', 'uploading');

            // 调用通用上传方法
            const result = await this.uploadImageFile(file, 'trademark');

            if (result.success) {
                // 上传成功，保存文件信息
                this.uploadStatus.trademark.status = 'success';
                this.uploadStatus.trademark.fileId = result.fileId;
                this.uploadStatus.trademark.fileUrl = result.fileUrl;
                this.uploadStatus.trademark.fileName = result.fileName;
                this.uploadStatus.trademark.fileSize = result.fileSize;

                // 更新UI状态显示
                this.updateUploadStatus('trademark', 'success');
                this.updateFormValidation();
                this.updateFormProgress();
                authManager.showNotification('商标网截图上传成功！', 'success');
                console.log('商标网截图上传成功，文件信息:', {
                    fileId: result.fileId,
                    fileName: result.fileName,
                    fileUrl: result.fileUrl
                });
            } else {
                // 上传失败
                this.uploadStatus.trademark.status = 'error';
                this.updateUploadStatus('trademark', 'error');
                this.updateFormProgress();
                authManager.showNotification(result.message || '商标网截图上传失败', 'error');
            }
        } catch (error) {
            console.error('商标网截图上传过程中发生错误:', error);
            this.uploadStatus.trademark.status = 'error';
            this.updateUploadStatus('trademark', 'error');
            this.updateFormProgress();
            authManager.showNotification('商标网截图上传过程中发生错误', 'error');
        }
    },

    // 通用图片上传方法
    async uploadImageFile(file, type) {
        console.log(`开始上传${type}文件:`, file.name, file.size, 'bytes');

        // 检查认证管理器和令牌
        if (typeof authManager === 'undefined' || !authManager.token) {
            console.error('认证令牌不可用');
            return {
                success: false,
                message: '认证失败，请重新登录'
            };
        }

        const formData = new FormData();
        formData.append('file', file);

        try {
            const response = await fetch('/api/v1/data/brand/upload', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${authManager.token}`
                },
                body: formData
            });

            const result = await response.json();

            if (response.ok && result.success) {
                return {
                    success: true,
                    fileId: result.file_id,
                    fileUrl: result.public_url,
                    fileName: result.file_name,
                    fileSize: result.file_size,
                    message: result.message
                };
            } else {
                // 处理特定的HTTP错误状态码
                if (response.status === 401) {
                    console.warn('认证令牌已过期或无效');
                    authManager.handleTokenExpiry();
                    return {
                        success: false,
                        message: '认证已过期，请重新登录'
                    };
                } else if (response.status === 403) {
                    console.warn('没有文件上传权限');
                    return {
                        success: false,
                        message: '没有文件上传权限，请联系管理员'
                    };
                } else {
                    return {
                        success: false,
                        message: result.message || `上传失败 (HTTP ${response.status})`
                    };
                }
            }
        } catch (error) {
            console.error('上传请求失败:', error);
            return {
                success: false,
                message: '网络错误，请检查连接'
            };
        }
    },

    // 更新上传状态显示（简化版）
    updateUploadStatus(type, status) {
        const uploadBtn = document.getElementById(`${type}-upload-btn`);
        if (!uploadBtn) return;

        // 更新按钮状态
        const btnText = uploadBtn.querySelector('.btn-text');
        const btnSpinner = uploadBtn.querySelector('.btn-spinner');

        switch (status) {
            case 'ready':
                uploadBtn.disabled = false;
                if (btnText) btnText.textContent = type === 'logo' ? '上传LOGO' : '上传截图';
                if (btnSpinner) btnSpinner.style.display = 'none';
                uploadBtn.classList.remove('btn-success', 'btn-danger');
                uploadBtn.classList.add('btn-primary');
                break;

            case 'uploading':
                uploadBtn.disabled = true;
                if (btnText) btnText.textContent = '上传中...';
                if (btnSpinner) btnSpinner.style.display = 'inline-block';
                break;

            case 'success':
                uploadBtn.disabled = true;
                if (btnText) btnText.textContent = '已上传';
                if (btnSpinner) btnSpinner.style.display = 'none';
                uploadBtn.classList.remove('btn-primary', 'btn-danger');
                uploadBtn.classList.add('btn-success');
                break;

            case 'error':
                uploadBtn.disabled = false;
                if (btnText) btnText.textContent = '重新上传';
                if (btnSpinner) btnSpinner.style.display = 'none';
                uploadBtn.classList.remove('btn-primary', 'btn-success');
                uploadBtn.classList.add('btn-danger');
                break;
        }
    },

    // 重置上传按钮状态（简化版）
    resetUploadButtonState(type) {
        const uploadBtn = document.getElementById(`${type}-upload-btn`);

        if (uploadBtn) {
            // 重置按钮状态
            uploadBtn.disabled = false;
            uploadBtn.classList.remove('btn-success', 'btn-danger');
            uploadBtn.classList.add('btn-primary');

            // 重置按钮文本
            const btnText = uploadBtn.querySelector('.btn-text');
            const btnSpinner = uploadBtn.querySelector('.btn-spinner');
            if (btnText) btnText.textContent = type === 'logo' ? '上传LOGO' : '上传截图';
            if (btnSpinner) btnSpinner.style.display = 'none';
        }
    },

    // 更新表单验证状态
    updateFormValidation() {
        // 检查基本信息
        this.formValidation.basicInfo = this.validateBasicInfo();

        // 检查文件上传状态
        this.formValidation.logoUploaded = this.uploadStatus.logo.status === 'success';
        this.formValidation.trademarkUploaded = this.uploadStatus.trademark.status === 'success';

        // 更新进度显示
        this.updateFormProgress();

        // 更新提交按钮状态
        this.updateSubmitButtonState();
    },

    // 验证基本信息
    validateBasicInfo() {
        const brandNameCn = document.getElementById('brand-name-cn')?.value.trim();
        const brandNameEn = document.getElementById('brand-name-en')?.value.trim();
        const trademarkHolder = document.getElementById('trademark-holder')?.value.trim();

        // 获取品牌原产地选择（按钮组）
        const brandOriginRadios = document.querySelectorAll('input[name="brand_origin"]:checked');
        const brandOrigin = brandOriginRadios.length > 0 ? brandOriginRadios[0].value : '';

        const internationalClassification = document.getElementById('international-classification')?.value;

        // 品牌名称二选一必填
        const hasValidName = brandNameCn || brandNameEn;

        // 其他必填字段
        const hasTrademarkHolder = !!trademarkHolder;
        const hasBrandOrigin = !!brandOrigin;
        const hasClassification = internationalClassification && internationalClassification.length > 0;

        return hasValidName && hasTrademarkHolder && hasBrandOrigin && hasClassification;
    },



    // 更新提交按钮状态
    updateSubmitButtonState() {
        const submitBtn = document.getElementById('add-brand-submit-btn');
        if (!submitBtn) return;

        const allCompleted = this.formValidation.basicInfo &&
                           this.formValidation.logoUploaded &&
                           this.formValidation.trademarkUploaded;

        submitBtn.disabled = !allCompleted;

        if (allCompleted) {
            submitBtn.classList.remove('btn-secondary');
            submitBtn.classList.add('btn-primary');
            submitBtn.title = '所有必填项已完成，可以提交';
        } else {
            submitBtn.classList.remove('btn-primary');
            submitBtn.classList.add('btn-secondary');

            // 生成提示信息
            const missing = [];
            if (!this.formValidation.basicInfo) missing.push('基本信息');
            if (!this.formValidation.logoUploaded) missing.push('品牌LOGO');
            if (!this.formValidation.trademarkUploaded) missing.push('商标网截图');

            submitBtn.title = `请完成：${missing.join('、')}`;
        }
    },

    // 重置上传状态
    resetUploadStatus() {
        // 重置上传状态
        this.uploadStatus.logo = {
            status: 'ready',
            fileId: null,
            fileUrl: null,
            file: null
        };
        this.uploadStatus.trademark = {
            status: 'ready',
            fileId: null,
            fileUrl: null,
            file: null
        };

        // 重置表单验证状态
        this.formValidation = {
            basicInfo: false,
            logoUploaded: false,
            trademarkUploaded: false
        };

        // 更新显示
        this.updateFormValidation();
    }
};

// 重写authManager的显示主要内容方法，添加ELECTRON权限检查
if (typeof authManager !== 'undefined') {
    const originalShowMainContent = authManager.showMainContent;
    authManager.showMainContent = function() {
        originalShowMainContent.call(this);
        // 在品牌库模块中，需要额外检查ELECTRON权限
        setTimeout(() => BrandModule.checkElectronAccess(), 100);
    };

    const originalShowLoginPrompt = authManager.showLoginPrompt;
    authManager.showLoginPrompt = function() {
        originalShowLoginPrompt.call(this);
        // 在品牌库模块中，隐藏ELECTRON权限提示
        const electronAccessDenied = document.getElementById('electron-access-denied');
        if (electronAccessDenied) electronAccessDenied.style.display = 'none';
    };
}



// ========== 国际分类下拉多选组件 ==========
BrandModule.classificationComponent = {

    // 国际分类数据
    classificationData: [
        { value: '1', text: '第1类 - 化学原料' },
        { value: '2', text: '第2类 - 颜料油漆' },
        { value: '3', text: '第3类 - 日化用品' },
        { value: '4', text: '第4类 - 燃料油脂' },
        { value: '5', text: '第5类 - 医药' },
        { value: '6', text: '第6类 - 金属材料' },
        { value: '7', text: '第7类 - 机械设备' },
        { value: '8', text: '第8类 - 手工器械' },
        { value: '9', text: '第9类 - 科学仪器' },
        { value: '10', text: '第10类 - 医疗器械' },
        { value: '11', text: '第11类 - 灯具空调' },
        { value: '12', text: '第12类 - 运输工具' },
        { value: '13', text: '第13类 - 军火烟火' },
        { value: '14', text: '第14类 - 珠宝钟表' },
        { value: '15', text: '第15类 - 乐器' },
        { value: '16', text: '第16类 - 办公用品' },
        { value: '17', text: '第17类 - 橡胶制品' },
        { value: '18', text: '第18类 - 皮革皮具' },
        { value: '19', text: '第19类 - 建筑材料' },
        { value: '20', text: '第20类 - 家具' },
        { value: '21', text: '第21类 - 厨房洁具' },
        { value: '22', text: '第22类 - 绳网袋篷' },
        { value: '23', text: '第23类 - 纱线丝' },
        { value: '24', text: '第24类 - 布料床单' },
        { value: '25', text: '第25类 - 服装鞋帽' },
        { value: '26', text: '第26类 - 纽扣拉链' },
        { value: '27', text: '第27类 - 地毯席垫' },
        { value: '28', text: '第28类 - 健身器材' },
        { value: '29', text: '第29类 - 食品' },
        { value: '30', text: '第30类 - 方便食品' },
        { value: '31', text: '第31类 - 饲料种籽' },
        { value: '32', text: '第32类 - 啤酒饮料' },
        { value: '33', text: '第33类 - 酒' },
        { value: '34', text: '第34类 - 烟草烟具' },
        { value: '35', text: '第35类 - 广告销售' },
        { value: '36', text: '第36类 - 金融物管' },
        { value: '37', text: '第37类 - 建筑修理' },
        { value: '38', text: '第38类 - 通讯服务' },
        { value: '39', text: '第39类 - 运输贮藏' },
        { value: '40', text: '第40类 - 材料加工' },
        { value: '41', text: '第41类 - 教育娱乐' },
        { value: '42', text: '第42类 - 网站服务' },
        { value: '43', text: '第43类 - 餐饮住宿' },
        { value: '44', text: '第44类 - 医疗园艺' },
        { value: '45', text: '第45类 - 社会服务' }
    ],

    // 选中的分类
    selectedClassifications: new Set(),

    // 初始化国际分类下拉组件
    init() {
        // 初始化隐藏的原始select
        this.initializeHiddenSelect();

        // 渲染下拉列表
        this.renderClassificationList();

        // 绑定事件
        this.bindClassificationEvents();
    },

    // 初始化隐藏的原始select
    initializeHiddenSelect() {
        const hiddenSelect = document.getElementById('international-classification');
        if (hiddenSelect) {
            // 清空现有选项
            hiddenSelect.innerHTML = '';

            // 添加所有分类选项
            this.classificationData.forEach(item => {
                const option = document.createElement('option');
                option.value = item.value;
                option.textContent = item.text;
                hiddenSelect.appendChild(option);
            });
        }
    },

    // 渲染分类列表
    renderClassificationList() {
        const listContainer = document.getElementById('classification-dropdown-list');
        if (!listContainer) return;

        listContainer.innerHTML = '';

        this.classificationData.forEach(item => {
            const optionDiv = document.createElement('div');
            optionDiv.className = 'classification-option';
            optionDiv.dataset.value = item.value;

            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.id = `classification-${item.value}`;
            checkbox.checked = this.selectedClassifications.has(item.value);

            const label = document.createElement('label');
            label.htmlFor = `classification-${item.value}`;
            label.className = 'classification-option-text';
            label.textContent = item.text;

            optionDiv.appendChild(checkbox);
            optionDiv.appendChild(label);

            if (this.selectedClassifications.has(item.value)) {
                optionDiv.classList.add('selected');
            }

            listContainer.appendChild(optionDiv);
        });
    },

    // 绑定分类组件事件
    bindClassificationEvents() {
        const searchInput = document.getElementById('classification-search');
        const toggleBtn = document.getElementById('classification-toggle');
        const clearBtn = document.getElementById('classification-clear');
        const filterInput = document.getElementById('classification-filter');
        const selectAllBtn = document.getElementById('classification-select-all');
        const clearAllBtn = document.getElementById('classification-clear-all');
        const dropdownMenu = document.getElementById('classification-dropdown-menu');
        const listContainer = document.getElementById('classification-dropdown-list');

        // 搜索输入框点击事件
        if (searchInput) {
            searchInput.addEventListener('click', () => {
                // 如果有选中项，先清空输入框显示，然后显示下拉菜单
                if (searchInput.classList.contains('has-selections')) {
                    searchInput.value = '';
                    searchInput.placeholder = '搜索分类或点击选择...';
                }
                this.showClassificationDropdown();
            });

            searchInput.addEventListener('focus', () => {
                // 如果有选中项，先清空输入框显示
                if (searchInput.classList.contains('has-selections')) {
                    searchInput.value = '';
                    searchInput.placeholder = '搜索分类或点击选择...';
                }
                this.showClassificationDropdown();
            });

            // 输入框失去焦点时恢复显示选中项
            searchInput.addEventListener('blur', () => {
                setTimeout(() => {
                    // 延迟执行，避免与下拉菜单点击冲突
                    const dropdownMenu = document.getElementById('classification-dropdown-menu');
                    if (!dropdownMenu || dropdownMenu.style.display === 'none') {
                        this.updateSearchInputDisplay();
                    }
                }, 150);
            });
        }

        // 切换按钮事件
        if (toggleBtn) {
            toggleBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleClassificationDropdown();
            });
        }

        // 清除按钮事件
        if (clearBtn) {
            clearBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.clearAllClassifications();
            });
        }

        // 过滤输入框事件
        if (filterInput) {
            filterInput.addEventListener('input', (e) => {
                this.filterClassifications(e.target.value);
            });
        }

        // 全选按钮事件
        if (selectAllBtn) {
            selectAllBtn.addEventListener('click', () => {
                this.selectAllClassifications();
            });
        }

        // 清空按钮事件
        if (clearAllBtn) {
            clearAllBtn.addEventListener('click', () => {
                this.clearAllClassifications();
            });
        }

        // 列表项点击事件
        if (listContainer) {
            listContainer.addEventListener('click', (e) => {
                const option = e.target.closest('.classification-option');
                if (option) {
                    const value = option.dataset.value;
                    this.toggleClassification(value);
                }
            });
        }

        // 点击外部关闭下拉菜单
        document.addEventListener('click', (e) => {
            const container = document.querySelector('.international-classification-container');
            if (container && !container.contains(e.target)) {
                this.hideClassificationDropdown();
            }
        });
    },

    // 切换下拉菜单显示状态
    toggleClassificationDropdown() {
        const dropdownMenu = document.getElementById('classification-dropdown-menu');
        if (dropdownMenu) {
            if (dropdownMenu.style.display === 'none' || !dropdownMenu.style.display) {
                this.showClassificationDropdown();
            } else {
                this.hideClassificationDropdown();
            }
        }
    },

    // 显示下拉菜单
    showClassificationDropdown() {
        const dropdownMenu = document.getElementById('classification-dropdown-menu');
        const toggleBtn = document.getElementById('classification-toggle');

        if (dropdownMenu) {
            dropdownMenu.style.display = 'block';
        }

        if (toggleBtn) {
            const icon = toggleBtn.querySelector('.icon');
            if (icon) {
                icon.style.transform = 'rotate(180deg)';
            }
        }
    },

    // 隐藏下拉菜单
    hideClassificationDropdown() {
        const dropdownMenu = document.getElementById('classification-dropdown-menu');
        const toggleBtn = document.getElementById('classification-toggle');

        if (dropdownMenu) {
            dropdownMenu.style.display = 'none';
        }

        if (toggleBtn) {
            const icon = toggleBtn.querySelector('.icon');
            if (icon) {
                icon.style.transform = 'rotate(0deg)';
            }
        }

        // 清空过滤输入框
        const filterInput = document.getElementById('classification-filter');
        if (filterInput) {
            filterInput.value = '';
            this.filterClassifications('');
        }

        // 恢复搜索框显示选中项
        this.updateSearchInputDisplay();
    },

    // 切换分类选择状态
    toggleClassification(value) {
        if (this.selectedClassifications.has(value)) {
            this.selectedClassifications.delete(value);
        } else {
            this.selectedClassifications.add(value);
        }

        this.updateClassificationUI();
        this.updateHiddenSelect();
        BrandModule.updateFormProgress();
    },

    // 全选分类
    selectAllClassifications() {
        const visibleOptions = document.querySelectorAll('.classification-option:not([style*="display: none"])');
        visibleOptions.forEach(option => {
            const value = option.dataset.value;
            this.selectedClassifications.add(value);
        });

        this.updateClassificationUI();
        this.updateHiddenSelect();
        BrandModule.updateFormProgress();
    },

    // 清空所有分类
    clearAllClassifications() {
        this.selectedClassifications.clear();
        this.updateClassificationUI();
        this.updateHiddenSelect();
        BrandModule.updateFormProgress();
    },

    // 过滤分类
    filterClassifications(keyword) {
        const options = document.querySelectorAll('.classification-option');
        const listContainer = document.getElementById('classification-dropdown-list');

        let hasVisibleOptions = false;

        options.forEach(option => {
            const text = option.querySelector('.classification-option-text').textContent.toLowerCase();
            const isVisible = text.includes(keyword.toLowerCase());

            option.style.display = isVisible ? 'flex' : 'none';
            if (isVisible) hasVisibleOptions = true;
        });

        // 显示无结果提示
        let noResultsDiv = listContainer.querySelector('.classification-no-results');
        if (!hasVisibleOptions && keyword.trim()) {
            if (!noResultsDiv) {
                noResultsDiv = document.createElement('div');
                noResultsDiv.className = 'classification-no-results';
                noResultsDiv.textContent = '未找到匹配的分类';
                listContainer.appendChild(noResultsDiv);
            }
            noResultsDiv.style.display = 'block';
        } else if (noResultsDiv) {
            noResultsDiv.style.display = 'none';
        }
    },

    // 更新分类UI
    updateClassificationUI() {
        // 更新选项状态
        const options = document.querySelectorAll('.classification-option');
        options.forEach(option => {
            const value = option.dataset.value;
            const checkbox = option.querySelector('input[type="checkbox"]');
            const isSelected = this.selectedClassifications.has(value);

            checkbox.checked = isSelected;
            option.classList.toggle('selected', isSelected);
        });

        // 更新搜索框显示
        this.updateSearchInputDisplay();
    },



    // 更新搜索框显示
    updateSearchInputDisplay() {
        const searchInput = document.getElementById('classification-search');
        if (!searchInput) return;

        const count = this.selectedClassifications.size;
        if (count === 0) {
            searchInput.placeholder = '搜索分类或点击选择...';
            searchInput.value = '';
            searchInput.classList.remove('has-selections');
        } else {
            // 获取选中的分类名称
            const selectedTexts = [];
            this.selectedClassifications.forEach(value => {
                const item = this.classificationData.find(item => item.value === value);
                if (item) {
                    // 简化显示，只显示类别号
                    const classNumber = item.text.match(/第(\d+)类/);
                    if (classNumber) {
                        selectedTexts.push(`第${classNumber[1]}类`);
                    } else {
                        selectedTexts.push(item.text);
                    }
                }
            });

            // 按类别号排序
            selectedTexts.sort((a, b) => {
                const numA = parseInt(a.match(/\d+/)?.[0] || '0');
                const numB = parseInt(b.match(/\d+/)?.[0] || '0');
                return numA - numB;
            });

            // 在输入框中显示选中的分类
            const displayText = selectedTexts.join(', ');
            searchInput.value = displayText;
            searchInput.placeholder = '';
            searchInput.classList.add('has-selections');
        }
    },

    // 更新隐藏的select
    updateHiddenSelect() {
        const hiddenSelect = document.getElementById('international-classification');
        if (!hiddenSelect) return;

        // 清除所有选中状态
        Array.from(hiddenSelect.options).forEach(option => {
            option.selected = false;
        });

        // 设置选中状态
        this.selectedClassifications.forEach(value => {
            const option = hiddenSelect.querySelector(`option[value="${value}"]`);
            if (option) {
                option.selected = true;
            }
        });
    }
}

// 模块初始化
document.addEventListener('DOMContentLoaded', function() {
    BrandModule.init();
});
