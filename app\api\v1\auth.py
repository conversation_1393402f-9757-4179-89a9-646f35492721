"""
认证API路由
处理外部平台登录和JWT会话管理
"""
import asyncio
from datetime import timed<PERSON><PERSON>
from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel

from app.core.security import security_manager
from app.core.logging import get_logger
from app.core.config import get_settings
from app.core.auth import get_client_ip
from app.services.auth_service import auth_service

logger = get_logger(__name__)
router = APIRouter()
security = HTTPBearer()
settings = get_settings()


class LoginRequest(BaseModel):
    """登录请求模型"""
    username: str
    password: str
    platform: str  # "aviation" 或 "engine"


class LoginResponse(BaseModel):
    """登录响应模型"""
    success: bool
    access_token: str = None
    token_type: str = "bearer"
    expires_in: int = None  # 动态计算，基于配置文件
    platform: str = None
    user_info: Dict[str, Any] = None
    message: str = ""


class TokenInfo(BaseModel):
    """令牌信息模型"""
    valid: bool
    expired: bool
    user_id: str = None
    platform: str = None
    expires_at: str = None
    time_remaining: int = None  # 剩余秒数


async def verify_user_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """验证用户令牌"""
    try:
        token = credentials.credentials
        if security_manager.is_token_expired(token):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌已过期，请重新登录"
            )
        
        payload = security_manager.verify_token(token)
        return payload
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"令牌验证失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证令牌"
        )


@router.post("/login", response_model=LoginResponse)
async def login(login_data: LoginRequest, request: Request):
    """
    外部平台登录
    支持航空平台和航发平台登录
    """
    # 获取客户端IP
    client_ip = get_client_ip(request)
    logger.info(f"用户尝试登录 - 平台: {login_data.platform}, 用户名: {login_data.username}, 客户端IP: {client_ip}")

    try:
        # 根据平台选择认证方式
        if login_data.platform == "aviation":
            platform_response = await auth_service.login_aviation_platform(
                login_data.username, login_data.password, client_ip
            )
        elif login_data.platform == "engine":
            platform_response = await auth_service.login_engine_platform(
                login_data.username, login_data.password, client_ip
            )
        else:
            return LoginResponse(
                success=False,
                message="不支持的平台类型，请选择 'aviation' 或 'engine'"
            )
        
        if not platform_response['success']:
            return LoginResponse(
                success=False,
                message=platform_response['message'],
                platform=login_data.platform
            )
        
        # 创建本地JWT令牌
        user_info = platform_response.get("user_info", {})
        token_data = {
            "sub": login_data.username,
            "username": login_data.username,
            "platform": login_data.platform,
            "local_username": user_info.get("local_username", login_data.username),
            "service_username": user_info.get("service_username", login_data.username),
            "electron_username": user_info.get("electron_username", ""),
            "platform_display": user_info.get("platform_display", ""),
            "display_name": user_info.get("display_name", login_data.username),
            "electron_access": user_info.get("electron_access", False),
            "electron_token": user_info.get("electron_token", ""),
            "user_info": user_info,
        }
        
        access_token = security_manager.create_access_token(
            data=token_data
            # 使用配置文件中的默认过期时间
        )
        
        logger.info(f"用户登录成功 - 平台: {login_data.platform}, 用户名: {login_data.username}")
        
        return LoginResponse(
            success=True,
            access_token=access_token,
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,  # 转换为秒
            platform=login_data.platform,
            user_info=platform_response.get("user_info", {}),
            message=platform_response['message']
        )
        
    except Exception as e:
        logger.error(f"登录过程中发生错误: {str(e)}")
        return LoginResponse(
            success=False,
            message="登录服务暂时不可用，请稍后重试"
        )


@router.post("/logout")
async def logout(user: Dict = Depends(verify_user_token)):
    """
    用户登出
    """
    try:
        username = user.get("sub")
        platform = user.get("platform")
        
        logger.info(f"用户登出 - 平台: {platform}, 用户名: {username}")
        
        return {
            "success": True,
            "message": "登出成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"登出过程中发生错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登出服务暂时不可用"
        )


@router.get("/verify", response_model=TokenInfo)
async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """
    验证令牌有效性
    """
    try:
        token = credentials.credentials
        
        # 检查令牌是否过期
        is_expired = security_manager.is_token_expired(token)
        
        if is_expired:
            return TokenInfo(
                valid=False,
                expired=True
            )
        
        # 验证令牌并获取过期时间
        payload = security_manager.verify_token(token)
        expiry_time = security_manager.get_token_expiry_time(token)
        
        # 计算剩余时间
        time_remaining = None
        if expiry_time:
            from datetime import datetime
            remaining_delta = expiry_time - datetime.utcnow()
            time_remaining = max(0, int(remaining_delta.total_seconds()))
        
        return TokenInfo(
            valid=True,
            expired=False,
            user_id=payload.get("sub"),
            platform=payload.get("platform"),
            expires_at=expiry_time.isoformat() if expiry_time else None,
            time_remaining=time_remaining
        )
        
    except HTTPException:
        return TokenInfo(
            valid=False,
            expired=True
        )
    except Exception as e:
        logger.error(f"令牌验证过程中发生错误: {str(e)}")
        return TokenInfo(
            valid=False,
            expired=True
        )
