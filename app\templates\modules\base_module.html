{% extends "base.html" %}

{% block title %}{{ app_name }} - {{ module_name }}{% endblock %}

{% block content %}
<div class="page-header d-print-none">
    <div class="container-fluid">
        <div class="row g-2 align-items-center">
            <div class="col">
                <!-- 面包屑导航 -->
                <div class="page-pretitle">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="/">控制台</a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">{{ module_name }}</li>
                        </ol>
                    </nav>
                </div>
                <h2 class="page-title">
                    {% block module_icon %}{% endblock %}
                    {{ module_name }}
                </h2>
                <div class="text-muted mt-1">
                    {% block module_description %}{% endblock %}
                </div>
            </div>
            <div class="col-auto ms-auto d-print-none">
                <div class="btn-list">
                    <a href="/" class="btn btn-outline-secondary">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M5 12l14 0"></path>
                            <path d="M5 12l6 6"></path>
                            <path d="M5 12l6 -6"></path>
                        </svg>
                        返回控制台
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="page-body">
    <div class="container-fluid">
        <!-- 登录提示 -->
        <div class="row" id="login-prompt">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <h3>需要登录</h3>
                        <p class="text-muted">请先登录到电子超市以使用{{ module_name }}功能</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#login-modal">
                            立即登录
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- ELECTRON权限不足提示 -->
        <div class="row" id="electron-access-denied" style="display: none;">
            <div class="col-12">
                <div class="card border-warning">
                    <div class="card-header bg-warning-lt">
                        <h3 class="card-title text-warning">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M12 9v2m0 4v.01"></path>
                                <path d="M5 19h14a2 2 0 0 0 1.84 -2.75l-7.1 -12.25a2 2 0 0 0 -3.5 0l-7.1 12.25a2 2 0 0 0 1.75 2.75"></path>
                            </svg>
                            电子超市平台权限不足
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <h4>无法访问{{ module_name }}</h4>
                                <p class="text-muted mb-3">
                                    您已成功登录到商城，但电子超市系统登录失败。
                                    数据采集功能需要电子超市平台权限才能正常使用。
                                </p>
                                <div class="alert alert-info">
                                    <h5 class="alert-title">可能的原因：</h5>
                                    <ul class="mb-0">
                                        <li>您的账号没有电子超市平台访问权限</li>
                                        <li>电子超市系统暂时不可用</li>
                                        <li>网络连接问题导致电子超市登录失败</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-auto">
                                <div class="avatar avatar-lg bg-warning-lt text-warning">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="32" height="32" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"></path>
                                        <path d="M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6"></path>
                                        <path d="M3 3l18 18"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4">
                            <button class="btn btn-warning me-2" data-bs-toggle="modal" data-bs-target="#login-modal">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"></path>
                                    <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"></path>
                                </svg>
                                重新登录
                            </button>
                            <a href="/" class="btn btn-outline-secondary">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M5 12l14 0"></path>
                                    <path d="M5 12l6 6"></path>
                                    <path d="M5 12l6 -6"></path>
                                </svg>
                                返回控制台
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 模块内容 -->
        <div id="main-content" style="display: none;">
            <!-- 查询输入卡片 -->
            <div class="card mb-4 query-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"></path>
                            <path d="M21 21l-6 -6"></path>
                        </svg>
                        查询条件
                    </h5>
                    <div class="card-actions">
                        <button class="btn btn-sm btn-outline-secondary" onclick="clearQueryCondition('{{ module_key }}')">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M4 7l16 0"></path>
                                <path d="M10 11l0 6"></path>
                                <path d="M14 11l0 6"></path>
                                <path d="M5 7l1 -4l4 0l1 4"></path>
                                <path d="M9 7l6 0"></path>
                            </svg>
                            清空条件
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <form id="{{ module_key }}-form">
                        <div class="mb-3">
                            <textarea class="form-control" name="query_text" rows="5" 
                                placeholder="{% block query_placeholder %}该功能未启用"></textarea>
                            <div class="form-text">{% endblock %}
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 模块特定的JavaScript代码
document.addEventListener('DOMContentLoaded', function() {
    // 确保数据采集管理器已加载
    if (typeof dataCollectionManager !== 'undefined') {
        console.log('{{ module_name }}模块已加载');
    }

    // 检查ELECTRON访问权限
    checkElectronAccess();
});

// 检查ELECTRON访问权限
function checkElectronAccess() {
    if (typeof authManager !== 'undefined' && authManager.userInfo) {
        const loginPrompt = document.getElementById('login-prompt');
        const electronAccessDenied = document.getElementById('electron-access-denied');
        const mainContent = document.getElementById('main-content');

        if (authManager.token && authManager.userInfo.local_username) {
            // 用户已登录，检查ELECTRON权限
            if (authManager.userInfo.electron_access) {
                // 有ELECTRON权限，显示主要内容
                if (loginPrompt) loginPrompt.style.display = 'none';
                if (electronAccessDenied) electronAccessDenied.style.display = 'none';
                if (mainContent) mainContent.style.display = 'block';
            } else {
                // 没有ELECTRON权限，显示权限不足提示
                if (loginPrompt) loginPrompt.style.display = 'none';
                if (electronAccessDenied) electronAccessDenied.style.display = 'block';
                if (mainContent) mainContent.style.display = 'none';
            }
        } else {
            // 用户未登录，显示登录提示
            if (loginPrompt) loginPrompt.style.display = 'block';
            if (electronAccessDenied) electronAccessDenied.style.display = 'none';
            if (mainContent) mainContent.style.display = 'none';
        }
    }
}

// 重写authManager的显示主要内容方法，添加ELECTRON权限检查
if (typeof authManager !== 'undefined') {
    const originalShowMainContent = authManager.showMainContent;
    authManager.showMainContent = function() {
        originalShowMainContent.call(this);
        // 在模块页面中，需要额外检查ELECTRON权限
        setTimeout(checkElectronAccess, 100);
    };

    const originalShowLoginPrompt = authManager.showLoginPrompt;
    authManager.showLoginPrompt = function() {
        originalShowLoginPrompt.call(this);
        // 在模块页面中，隐藏ELECTRON权限提示
        const electronAccessDenied = document.getElementById('electron-access-denied');
        if (electronAccessDenied) electronAccessDenied.style.display = 'none';
    };
}

// 初始化数据筛选器
function initializeDataFilter(moduleKey) {
    if (!dataFilter) {
        dataFilter = new DataFilter('data-filter-container', {
            storageKey: `data-filter-${moduleKey}`,
            onFilterChange: function(filteredData) {
                updateResultsDisplay(filteredData, moduleKey);
            }
        });
    }
}

// 更新结果显示
function updateResultsDisplay(data, moduleKey) {
    const countElement = document.getElementById(`${moduleKey}-count`);
    if (countElement) {
        countElement.textContent = `${data.length} 条记录`;
    }

    // 子类可以重写此方法来自定义显示逻辑
    if (typeof displayFilteredResults === 'function') {
        displayFilteredResults(data);
    }
}


</script>
<script src="/static/js/data-filter.js"></script>
<script src="/static/js/data-collection.js"></script>
{% endblock %}
