/**
 * 需求单查询模块 JavaScript
 * 使用模块化设计模式，提供完整的数据采集、筛选、分页、导出功能
 */

// 需求单模块对象
const XqdModule = {
    // 模块初始化
    init() {
        console.log('需求单查询模块已加载');
        
        // 检查ELECTRON访问权限
        this.checkElectronAccess();
        
        // 绑定表单提交事件
        this.bindFormEvents();
        
        // 加载保存的查询条件
        this.loadQueryConditionsFromStorage();
        
        // 初始化数据筛选器
        this.initializeDataFilter();
        
        // 初始化日期时间范围选择器
        this.initializeDateTimeRangePickers();

        // 加载采购方列表
        this.loadPurchaserList();

        // 初始化搜索下拉框
        this.initializeSearchDropdown();
    },

    // 检查ELECTRON访问权限
    checkElectronAccess() {
        if (typeof authManager !== 'undefined' && authManager.userInfo) {
            const loginPrompt = document.getElementById('login-prompt');
            const electronAccessDenied = document.getElementById('electron-access-denied');
            const mainContent = document.getElementById('main-content');

            if (authManager.token && authManager.userInfo.local_username) {
                // 用户已登录，检查ELECTRON权限
                if (authManager.userInfo.electron_access) {
                    // 有ELECTRON权限，显示主要内容
                    if (loginPrompt) loginPrompt.style.display = 'none';
                    if (electronAccessDenied) electronAccessDenied.style.display = 'none';
                    if (mainContent) mainContent.style.display = 'block';
                } else {
                    // 没有ELECTRON权限，显示权限不足提示
                    if (loginPrompt) loginPrompt.style.display = 'none';
                    if (electronAccessDenied) electronAccessDenied.style.display = 'block';
                    if (mainContent) mainContent.style.display = 'none';
                }
            } else {
                // 用户未登录，显示登录提示
                if (loginPrompt) loginPrompt.style.display = 'block';
                if (electronAccessDenied) electronAccessDenied.style.display = 'none';
                if (mainContent) mainContent.style.display = 'none';
            }
        }
    },

    // 绑定表单事件
    bindFormEvents() {
        const form = document.getElementById('xqd-form');
        if (form) {
            form.addEventListener('submit', this.handleFormSubmit.bind(this));
        }
    },

    // 初始化数据筛选器
    initializeDataFilter() {
        if (!dataFilter) {
            dataFilter = new DataFilter('data-filter-container', {
                storageKey: 'xqd-filter',
                useTabSeparatedCopy: true, // 使用制表符分隔的复制格式，适合Excel粘贴
                onFilterChange: (filteredData) => {
                    // 更新分页管理器的筛选数据
                    if (window.paginationManager_xqd) {
                        window.paginationManager_xqd.setData(dataFilter.originalData, filteredData);

                        // 显示第一页数据
                        const firstPageData = window.paginationManager_xqd.getCurrentPageData();
                        this.displayFilteredResults(firstPageData, true);
                    } else {
                        // 如果分页管理器还未初始化，直接显示筛选结果
                        this.displayFilteredResults(filteredData, false);
                    }
                }
            });
        }
    },

    // 处理表单提交
    async handleFormSubmit(event) {
        event.preventDefault();

        // 检查ELECTRON权限
        if (!this.checkElectronAccessForQuery()) {
            return;
        }

        const form = event.target;
        const formData = new FormData(form);

        // 获取表单数据
        const queryParams = {
            askSheetCode: formData.get('askSheetCode')?.trim() || null,
            askSheetName: formData.get('askSheetName')?.trim() || null,
            answerBeginTimeStart: formData.get('answerBeginTimeStart') || null,
            answerBeginTimeEnd: formData.get('answerBeginTimeEnd') || null,
            answerEndTimeStart: formData.get('answerEndTimeStart') || null,
            answerEndTimeEnd: formData.get('answerEndTimeEnd') || null,
            askSheetStatus: formData.get('askSheetStatus') || null,
            prodName: formData.get('prodName')?.trim() || null,
            purchaserName: formData.get('purchaserName') || null
        };

        // 验证至少有一个查询条件
        const hasCondition = Object.values(queryParams).some(value => value !== null && value !== '');
        if (!hasCondition) {
            authManager.showNotification('请至少输入一个查询条件', 'warning');
            return;
        }

        // 保存查询条件到本地存储（仅保存比价单号）
        this.saveQueryConditionsToStorage({
            askSheetCode: queryParams.askSheetCode
        });

        // 使用按钮管理器包装查询操作
        const queryButton = document.getElementById('xqd-submit-btn');

        try {
            await buttonManager.wrapAsync(queryButton, async () => {
                // 执行查询
                await this.executeQuery(queryParams);
            }, '查询需求单中...');

        } catch (error) {
            console.error('需求单查询错误:', error);
            authManager.showNotification('查询过程中发生错误', 'error');
        }
    },

    // 搜索下拉框数据缓存
    purchaserListCache: [],
    searchDropdownConfig: {
        debounceDelay: 300,
        maxDisplayItems: 100,
        minSearchLength: 0
    },

    // 加载采购方列表
    async loadPurchaserList() {
        // 检查用户权限
        if (typeof authManager === 'undefined' || !authManager.userInfo || !authManager.userInfo.electron_access) {
            console.log('用户没有ELECTRON权限，跳过采购方列表加载');
            this.updateSearchDropdownState('no-permission');
            return;
        }

        try {
            // 显示加载状态
            this.updateSearchDropdownState('loading');

            const response = await fetch('/api/v1/data/xqd/purchaser-list', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${authManager.token}`
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.success) {
                // 缓存采购方列表数据
                this.purchaserListCache = result.data || [];

                // 更新隐藏的select元素（用于表单提交）
                const purchaserSelect = document.getElementById('purchaser-select');
                if (purchaserSelect) {
                    purchaserSelect.innerHTML = '<option value="">请选择发布企业</option>';
                    this.purchaserListCache.forEach(purchaser => {
                        const option = document.createElement('option');
                        option.value = purchaser;
                        option.textContent = purchaser;
                        purchaserSelect.appendChild(option);
                    });
                }

                // 更新搜索下拉框
                this.updateSearchDropdownList(this.purchaserListCache);
                this.updateSearchDropdownState('success');

                console.log(`采购方列表加载成功，共 ${this.purchaserListCache.length} 个采购方`);

            } else {
                console.error('采购方列表加载失败:', result.message);
                this.updateSearchDropdownState('error');

                // 如果是权限错误，显示特定提示
                if (result.permission_error) {
                    authManager.showNotification('ELECTRON平台权限不足，无法加载采购方列表', 'warning');
                } else {
                    authManager.showNotification(`采购方列表加载失败: ${result.message}`, 'error');
                }
            }

        } catch (error) {
            console.error('采购方列表加载异常:', error);
            this.updateSearchDropdownState('error');
            authManager.showNotification('采购方列表加载失败，请检查网络连接', 'error');
        }
    },

    // 初始化搜索下拉框
    initializeSearchDropdown() {
        const container = document.getElementById('purchaser-search-container');
        const input = document.getElementById('purchaser-search-input');
        const clearBtn = document.getElementById('purchaser-clear-btn');
        const refreshBtn = document.getElementById('purchaser-refresh-btn');
        const dropdownList = document.getElementById('purchaser-dropdown-list');

        if (!container || !input || !clearBtn || !refreshBtn || !dropdownList) {
            console.error('搜索下拉框元素未找到');
            return;
        }

        // 防抖搜索
        let searchTimeout;

        // 检测是否为移动设备
        const isMobileDevice = () => {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                   (navigator.maxTouchPoints && navigator.maxTouchPoints > 2);
        };

        // 点击输入框显示下拉列表
        input.addEventListener('click', () => {
            if (this.purchaserListCache.length > 0) {
                this.showDropdown();
                this.updateSearchDropdownList(this.purchaserListCache);

                // 移动设备上确保输入框可以获得焦点
                if (isMobileDevice()) {
                    // 短暂延迟后聚焦，确保下拉框已显示
                    setTimeout(() => {
                        input.focus();
                    }, 100);
                }
            }
        });

        // 输入框获得焦点
        input.addEventListener('focus', () => {
            if (this.purchaserListCache.length > 0) {
                this.showDropdown();
                this.updateSearchDropdownList(this.purchaserListCache);
            }
        });

        // 搜索输入
        input.addEventListener('input', (e) => {
            const query = e.target.value.trim();

            // 清除之前的搜索定时器
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }

            // 防抖搜索
            searchTimeout = setTimeout(() => {
                this.performSearch(query);
            }, this.searchDropdownConfig.debounceDelay);

            // 显示/隐藏清除按钮
            this.toggleClearButton(query.length > 0);
        });

        // 键盘导航
        input.addEventListener('keydown', (e) => {
            this.handleKeyboardNavigation(e);
        });

        // 清除按钮点击
        clearBtn.addEventListener('click', () => {
            this.clearSelection();
        });

        // 刷新按钮点击
        refreshBtn.addEventListener('click', () => {
            this.refreshPurchaserList();
        });

        // 点击外部关闭下拉框
        document.addEventListener('click', (e) => {
            if (!container.contains(e.target)) {
                this.hideDropdown();
            }
        });

        // 初始化状态
        this.updateSearchDropdownState('ready');
    },



    // 执行搜索
    performSearch(query) {
        if (!query || query.length < this.searchDropdownConfig.minSearchLength) {
            // 显示所有选项
            this.updateSearchDropdownList(this.purchaserListCache);
            return;
        }

        // 模糊搜索
        const filteredResults = this.purchaserListCache.filter(purchaser =>
            purchaser.toLowerCase().includes(query.toLowerCase())
        );

        this.updateSearchDropdownList(filteredResults, query);
    },

    // 更新搜索下拉框列表
    updateSearchDropdownList(items, searchQuery = '') {
        const dropdownList = document.getElementById('purchaser-dropdown-list');
        if (!dropdownList) return;

        // 清空现有内容
        dropdownList.innerHTML = '';

        if (items.length === 0) {
            // 显示无结果提示
            const noResultsItem = document.createElement('div');
            noResultsItem.className = 'dropdown-item no-results';
            noResultsItem.innerHTML = '<span class="text-muted">未找到匹配的企业</span>';
            dropdownList.appendChild(noResultsItem);
            return;
        }

        // 限制显示数量
        const displayItems = items.slice(0, this.searchDropdownConfig.maxDisplayItems);

        displayItems.forEach((purchaser) => {
            const item = document.createElement('div');
            item.className = 'dropdown-item';
            item.setAttribute('data-value', purchaser);
            item.setAttribute('tabindex', '0');

            // 高亮搜索关键词
            if (searchQuery) {
                item.innerHTML = this.highlightSearchTerm(purchaser, searchQuery);
            } else {
                item.textContent = purchaser;
            }

            // 点击选择
            item.addEventListener('click', () => {
                this.selectPurchaser(purchaser);
            });

            // 键盘选择
            item.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.selectPurchaser(purchaser);
                }
            });

            dropdownList.appendChild(item);
        });

        // 如果有更多项目，显示提示
        if (items.length > this.searchDropdownConfig.maxDisplayItems) {
            const moreItem = document.createElement('div');
            moreItem.className = 'dropdown-item no-results';
            moreItem.innerHTML = `<span class="text-muted">还有 ${items.length - this.searchDropdownConfig.maxDisplayItems} 个结果，请继续输入以缩小范围</span>`;
            dropdownList.appendChild(moreItem);
        }
    },

    // 高亮搜索关键词
    highlightSearchTerm(text, searchTerm) {
        if (!searchTerm) return text;

        const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
        return text.replace(regex, '<span class="highlight">$1</span>');
    },

    // 选择采购方
    selectPurchaser(purchaser) {
        const input = document.getElementById('purchaser-search-input');
        const hiddenSelect = document.getElementById('purchaser-select');

        if (input) {
            input.value = purchaser;
            // 移除readonly设置，保持输入框可编辑状态以支持移动设备
            input.blur(); // 失去焦点以隐藏虚拟键盘
        }

        if (hiddenSelect) {
            hiddenSelect.value = purchaser;
        }

        this.hideDropdown();
        this.toggleClearButton(true);

        console.log(`已选择采购方: ${purchaser}`);
    },

    // 清除选择
    clearSelection() {
        const input = document.getElementById('purchaser-search-input');
        const hiddenSelect = document.getElementById('purchaser-select');

        if (input) {
            input.value = '';
            input.placeholder = '搜索或选择发布企业...';
            // 保持输入框可编辑状态以支持移动设备
            input.blur(); // 失去焦点以隐藏虚拟键盘
        }

        if (hiddenSelect) {
            hiddenSelect.value = '';
        }

        this.hideDropdown();
        this.toggleClearButton(false);

        console.log('已清除采购方选择');
    },

    // 显示/隐藏清除按钮
    toggleClearButton(show) {
        const clearBtn = document.getElementById('purchaser-clear-btn');
        if (clearBtn) {
            clearBtn.style.display = show ? 'flex' : 'none';
        }
    },

    // 显示下拉框
    showDropdown() {
        const container = document.getElementById('purchaser-search-container');
        if (container) {
            container.classList.add('open');
        }
    },

    // 隐藏下拉框
    hideDropdown() {
        const container = document.getElementById('purchaser-search-container');

        if (container) {
            container.classList.remove('open');
        }

        // 移除readonly设置，保持输入框始终可编辑以支持移动设备
    },

    // 更新搜索下拉框状态
    updateSearchDropdownState(state) {
        const container = document.getElementById('purchaser-search-container');
        const dropdownList = document.getElementById('purchaser-dropdown-list');

        if (!container || !dropdownList) return;

        // 清除所有状态类
        container.classList.remove('loading', 'error', 'success');

        switch (state) {
            case 'loading':
                container.classList.add('loading');
                dropdownList.innerHTML = `
                    <div class="dropdown-item loading-item">
                        <i class="spinner-border spinner-border-sm me-2" role="status"></i>
                        <span>加载中...</span>
                    </div>
                `;
                break;
            case 'error':
                container.classList.add('error');
                dropdownList.innerHTML = `
                    <div class="dropdown-item no-results">
                        <span class="text-danger">加载失败，请刷新页面重试</span>
                    </div>
                `;
                break;
            case 'success':
                container.classList.add('success');
                break;
            case 'no-permission':
                dropdownList.innerHTML = `
                    <div class="dropdown-item no-results">
                        <span class="text-warning">需要ELECTRON权限才能加载企业列表</span>
                    </div>
                `;
                break;
            case 'ready':
            default:
                // 默认状态
                break;
        }
    },

    // 键盘导航处理
    handleKeyboardNavigation(e) {
        const dropdownList = document.getElementById('purchaser-dropdown-list');
        if (!dropdownList || !dropdownList.parentElement.classList.contains('open')) {
            return;
        }

        const items = dropdownList.querySelectorAll('.dropdown-item:not(.no-results):not(.loading-item)');
        const currentActive = dropdownList.querySelector('.dropdown-item.active');
        let currentIndex = currentActive ? Array.from(items).indexOf(currentActive) : -1;

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                currentIndex = Math.min(currentIndex + 1, items.length - 1);
                this.setActiveItem(items, currentIndex);
                break;
            case 'ArrowUp':
                e.preventDefault();
                currentIndex = Math.max(currentIndex - 1, 0);
                this.setActiveItem(items, currentIndex);
                break;
            case 'Enter':
                e.preventDefault();
                if (currentActive) {
                    const value = currentActive.getAttribute('data-value');
                    if (value) {
                        this.selectPurchaser(value);
                    }
                }
                break;
            case 'Escape':
                e.preventDefault();
                this.hideDropdown();
                break;
        }
    },

    // 手动刷新采购方列表
    async refreshPurchaserList() {
        // 检查用户权限
        if (typeof authManager === 'undefined' || !authManager.userInfo || !authManager.userInfo.electron_access) {
            authManager.showNotification('需要ELECTRON权限才能刷新企业列表', 'warning');
            return;
        }

        const input = document.getElementById('purchaser-search-input');

        // 保存当前选中的值
        const currentValue = input ? input.value : '';

        try {
            // 显示刷新加载状态
            this.setRefreshButtonState(true);
            this.updateSearchDropdownState('loading');

            console.log('开始手动刷新采购方列表...');

            const response = await fetch('/api/v1/data/xqd/purchaser-list', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${authManager.token}`
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.success) {
                // 更新缓存数据
                const oldCacheLength = this.purchaserListCache.length;
                this.purchaserListCache = result.data || [];

                // 更新隐藏的select元素
                const purchaserSelect = document.getElementById('purchaser-select');
                if (purchaserSelect) {
                    purchaserSelect.innerHTML = '<option value="">请选择发布企业</option>';
                    this.purchaserListCache.forEach(purchaser => {
                        const option = document.createElement('option');
                        option.value = purchaser;
                        option.textContent = purchaser;
                        purchaserSelect.appendChild(option);
                    });
                }

                // 更新搜索下拉框
                this.updateSearchDropdownList(this.purchaserListCache);
                this.updateSearchDropdownState('success');

                // 检查当前选中的值是否仍然存在
                if (currentValue && this.purchaserListCache.includes(currentValue)) {
                    // 保持当前选择
                    if (input) {
                        input.value = currentValue;
                    }
                    if (purchaserSelect) {
                        purchaserSelect.value = currentValue;
                    }
                    this.toggleClearButton(true);
                    console.log(`刷新完成，保持当前选择: ${currentValue}`);
                } else if (currentValue) {
                    // 当前选择的值不存在于新数据中，清除选择
                    this.clearSelection();
                    authManager.showNotification(`刷新完成，但之前选择的企业"${currentValue}"已不存在`, 'warning');
                } else {
                    console.log('刷新完成，无当前选择');
                }

                // 显示刷新结果
                const changeInfo = oldCacheLength !== this.purchaserListCache.length
                    ? `，数据已更新 (${oldCacheLength} → ${this.purchaserListCache.length})`
                    : '';

                authManager.showNotification(`企业列表刷新成功${changeInfo}`, 'success');

                console.log(`采购方列表刷新成功，共 ${this.purchaserListCache.length} 个采购方${changeInfo}`);

            } else {
                console.error('采购方列表刷新失败:', result.message);
                this.updateSearchDropdownState('error');

                // 如果是权限错误，显示特定提示
                if (result.permission_error) {
                    authManager.showNotification('ELECTRON平台权限不足，无法刷新采购方列表', 'warning');
                } else {
                    authManager.showNotification(`企业列表刷新失败: ${result.message}`, 'error');
                }
            }

        } catch (error) {
            console.error('采购方列表刷新异常:', error);
            this.updateSearchDropdownState('error');
            authManager.showNotification('企业列表刷新失败，请检查网络连接', 'error');
        } finally {
            // 恢复刷新按钮状态
            this.setRefreshButtonState(false);
        }
    },

    // 设置刷新按钮状态
    setRefreshButtonState(loading) {
        const refreshBtn = document.getElementById('purchaser-refresh-btn');
        if (!refreshBtn) return;

        if (loading) {
            refreshBtn.classList.add('loading');
            refreshBtn.disabled = true;
            refreshBtn.title = '正在刷新...';
        } else {
            refreshBtn.classList.remove('loading');
            refreshBtn.disabled = false;
            refreshBtn.title = '刷新企业列表';
        }
    },

    // 设置活动项
    setActiveItem(items, index) {
        // 清除所有活动状态
        items.forEach(item => item.classList.remove('active'));

        // 设置新的活动项
        if (index >= 0 && index < items.length) {
            const activeItem = items[index];
            activeItem.classList.add('active');

            // 滚动到可见区域
            activeItem.scrollIntoView({
                block: 'nearest',
                behavior: 'smooth'
            });
        }
    },

    // 检查ELECTRON访问权限（用于查询）
    checkElectronAccessForQuery() {
        if (typeof authManager === 'undefined' || !authManager.userInfo) {
            authManager.showNotification('请先登录', 'warning');
            return false;
        }

        if (!authManager.userInfo.electron_access) {
            authManager.showNotification('需要电子超市平台权限才能进行需求单查询', 'error');
            return false;
        }

        return true;
    },

    // 执行查询
    async executeQuery(queryParams) {
        try {
            const response = await fetch('/api/v1/data/xqd', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authManager.token}`
                },
                body: JSON.stringify(queryParams)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            if (result.success) {
                this.setupFilter(result.data);
                authManager.showNotification(`查询成功，找到 ${result.data.length} 条需求单记录`, 'success');

                // 记录查询统计
                if (typeof dashboardStats !== 'undefined') {
                    dashboardStats.recordQuery('xqd', true);
                }
            } else {
                // 检查是否为权限相关错误
                if (result.permission_error) {
                    authManager.showNotification('权限已失效，正在重新登录...', 'warning');
                    authManager.handleTokenExpiry();
                    return;
                }

                console.warn('查询失败:', result.message);
                authManager.showNotification(result.message || '查询失败', 'error');
                this.setupFilter([]);

                // 记录查询统计（失败）
                if (typeof dashboardStats !== 'undefined') {
                    dashboardStats.recordQuery('xqd', false);
                }
            }

        } catch (error) {
            console.error('需求单查询API错误:', error);
            authManager.showNotification('查询请求失败，请检查网络连接', 'error');
            this.setupFilter([]);
        }
    },

    // 设置筛选器
    setupFilter(data) {
        if (!dataFilter) {
            this.initializeDataFilter();
        }

        // 初始化分页管理器
        if (!window.paginationManager_xqd) {
            window.paginationManager_xqd = new PaginationManager({
                moduleId: 'xqd',
                pageSize: 20,
                renderCallback: () => {
                    const currentPageData = window.paginationManager_xqd.getCurrentPageData();
                    this.displayFilteredResults(currentPageData, true);
                }
            });
        }

        // 定义列结构
        const columns = [
            { key: 'askSheetCode', label: '比价单号' },
            { key: 'askSheetName', label: '比价单名称' },
            { key: 'answerBeginTime', label: '开始时间' },
            { key: 'answerEndTime', label: '结束时间' },
            { key: 'prodNum', label: '商品数量' },
            { key: 'askSheetStatus', label: '状态' },
            { key: 'askSheetUser', label: '客户名称' },
            { key: 'purchaserName', label: '客户单位' }
        ];

        // 设置筛选器数据
        dataFilter.setData(data, columns);

        // 设置分页数据
        window.paginationManager_xqd.setData(data, data);

        // 显示第一页数据
        const firstPageData = window.paginationManager_xqd.getCurrentPageData();
        this.displayFilteredResults(firstPageData, true);

        // 确保筛选器面板正确更新
        setTimeout(() => {
            if (dataFilter && dataFilter.updateStats) {
                dataFilter.updateStats();
            }
        }, 100);
    },

    // 显示筛选后的结果
    displayFilteredResults(data, isPaginated = false) {
        const resultsContainer = document.getElementById('xqd-results');
        const countElement = document.getElementById('xqd-count');

        if (!resultsContainer || !countElement) {
            console.error('Required DOM elements not found!');
            return;
        }

        // 生成表格内容
        let tableHTML = '';

        if (isPaginated && window.paginationManager_xqd) {
            // 分页模式
            const paginationInfo = window.paginationManager_xqd.getInfo();
            countElement.textContent = `第${paginationInfo.startItem}-${paginationInfo.endItem}条，共${paginationInfo.totalItems}条记录`;
            // 生成表格
            tableHTML = dataFilter.generateSelectableTable(data, this.renderCustomTable.bind(this));

            // 添加分页控件
            const paginationHTML = window.paginationManager_xqd.generatePaginationHTML();
            tableHTML += paginationHTML;
        } else {
            // 非分页模式
            countElement.textContent = `${data.length} 条记录`;
            tableHTML = dataFilter.generateSelectableTable(data, this.renderCustomTable.bind(this));
        }

        resultsContainer.innerHTML = tableHTML;
    },

    // 自定义表格渲染器
    renderCustomTable(data, filterInstance) {
        if (!data || data.length === 0) {
            return `
                <div class="empty">
                    <div class="empty-img">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDEyOCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik02NCA5NkM4MC41Njg1IDk2IDk0IDgyLjU2ODUgOTQgNjZDOTQgNDkuNDMxNSA4MC41Njg1IDM2IDY0IDM2QzQ3LjQzMTUgMzYgMzQgNDkuNDMxNSAzNCA2NkMzNCA4Mi41Njg1IDQ3LjQzMTUgOTYgNjQgOTZaIiBzdHJva2U9IiNEQURERTIiIHN0cm9rZS13aWR0aD0iMiIvPgo8cGF0aCBkPSJNNTcuMzMzMyA2NkM1Ny4zMzMzIDYyLjMxODEgNjAuMzE4MSA1OS4zMzMzIDY0IDU5LjMzMzNDNjcuNjgxOSA1OS4zMzMzIDcwLjY2NjcgNjIuMzE4MSA3MC42NjY3IDY2QzcwLjY2NjcgNjkuNjgxOSA2Ny42ODE5IDcyLjY2NjcgNjQgNzIuNjY2N0M2MC4zMTgxIDcyLjY2NjcgNTcuMzMzMyA2OS42ODE5IDU3LjMzMzMgNjZaIiBmaWxsPSIjREFEREUyIi8+Cjwvc3ZnPgo=" alt="暂无数据">
                    </div>
                    <p class="empty-title">暂无需求单数据</p>
                    <p class="empty-subtitle text-muted">请调整查询条件或筛选条件后重试</p>
                </div>
            `;
        }

        // 生成带选择功能的表格HTML
        let tableHTML = `
            <div class="table-responsive">
                <table class="table table-vcenter">
                    <thead>
                        <tr>
                            <th>比价单号</th>
                            <th>比价单名称</th>
                            <th>开始时间</th>
                            <th>结束时间</th>
                            <th>商品数量</th>
                            <th>状态</th>
                            <th style="width: 90px;">客户名称</th>
                            <th>客户单位</th>
                            <th style="width: 80px;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        data.forEach((item, index) => {
            const isSelected = filterInstance.selectedRows.has(index);

            // 状态徽章
            let statusBadge = '';

            // 优先判断 answerStatus，如果为 'QUOTED' 则显示"已报价"
            if (item.answerStatus === 'QUOTED') {
                statusBadge = '<span class="badge bg-success">已报价</span>';
            } else {
                // 其他情况按原有的 askSheetStatus 逻辑处理
                switch(item.askSheetStatus) {
                    case 'DRAFT':
                        statusBadge = '<span class="badge bg-secondary">草稿</span>';
                        break;
                    case 'INQUIRING':
                        statusBadge = '<span class="badge bg-blue">询价中</span>';
                        break;
                    case 'CONFIRM_WAIT':
                        statusBadge = '<span class="badge bg-orange">决标中</span>';
                        break;
                    case 'FINISHED':
                        statusBadge = '<span class="badge bg-green">已完成</span>';
                        break;
                    case 'APPROVAL_ING':
                        statusBadge = '<span class="badge bg-yellow">审批中</span>';
                        break;
                    default:
                        statusBadge = '<span class="badge bg-secondary">未知</span>';
                }
            }

            // 确保商品数量正确显示
            const prodNumValue = item.prodNum !== null && item.prodNum !== undefined ? item.prodNum : 0;

            tableHTML += `
                <tr data-row-index="${index}" class="${isSelected ? 'table-active' : ''}"
                    onclick="XqdModule.handleRowClick(${index}, event)"
                    style="cursor: pointer;">
                    <td><strong>${this.escapeHtml(item.askSheetCode || '')}</strong></td>
                    <td>
                        <div class="table-cell-ellipsis sheet-name" title="${this.escapeHtml(item.askSheetName || '')}">
                            ${this.escapeHtml(item.askSheetName || '')}
                        </div>
                    </td>
                    <td><span class="text-muted">${item.answerBeginTime || ''}</span></td>
                    <td><span class="text-muted">${item.answerEndTime || ''}</span></td>
                    <td><span class="badge bg-info">${prodNumValue}</span></td>
                    <td>${statusBadge}</td>
                    <td>
                        <div class="table-cell-ellipsis customer-name" title="${this.escapeHtml(item.askSheetUser || '')}">
                            ${this.escapeHtml(item.askSheetUser || '')}
                        </div>
                    </td>
                    <td>
                        <div class="table-cell-ellipsis customer-unit" title="${this.escapeHtml(item.purchaserName || '')}">
                            ${this.escapeHtml(item.purchaserName || '')}
                        </div>
                    </td>
                    <td>
                        <div class="btn-list flex-nowrap">
                            <button class="btn btn-sm btn-outline-primary"
                                    onclick="event.stopPropagation(); XqdModule.viewDetail('${item.askSheetCode}', '${item.askSheetId}')"
                                    title="查看详情">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"></path>
                                    <path d="M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6"></path>
                                </svg>
                            </button>
                            ${this.generateDownloadButton(item)}
                        </div>
                    </td>
                </tr>
            `;
        });

        tableHTML += `
                    </tbody>
                </table>
            </div>
        `;

        // 更新选择状态UI
        setTimeout(() => {
            filterInstance.updateAllRowSelectionUI();
        }, 0);

        return tableHTML;
    },

    // 生成下载按钮
    generateDownloadButton(item) {
        const askSheetCode = item.askSheetCode || '';
        const askSheetId = item.askSheetId || '';
        const askSheetStatus = item.askSheetStatus || '';
        const answerStatus = item.answerStatus || '';

        // 只有状态为"INQUIRING"的需求单才显示下载按钮
        if (askSheetStatus === 'INQUIRING' && answerStatus != 'QUOTED') {
            return `
                <button class="btn btn-sm btn-outline-success"
                        onclick="event.stopPropagation(); XqdModule.downloadReport('${askSheetCode}', '${askSheetId}')"
                        id="download-btn-${askSheetCode}"
                        title="下载报价单">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2"></path>
                        <path d="M7 11l5 5l5 -5"></path>
                        <path d="M12 4l0 12"></path>
                    </svg>
                </button>
            `;
        } else {
            // 其他状态不显示下载按钮
            return '';
        }
    },

    // 查看需求单详情
    viewDetail(askSheetCode, askSheetId) {
        // 检查ELECTRON权限
        if (!this.checkElectronAccessForQuery()) {
            return;
        }

        if (!askSheetCode || !askSheetId) {
            authManager.showNotification('缺少必要的参数信息', 'warning');
            return;
        }

        // 构建详情页面URL
        const detailUrl = `/modules/xqd/detail?askSheetCode=${encodeURIComponent(askSheetCode)}&askSheetId=${encodeURIComponent(askSheetId)}`;

        // 在新标签页中打开详情页面
        window.open(detailUrl, '_blank');

        authManager.showNotification(`正在打开需求单详情: ${askSheetCode}`, 'info');
    },

    // 下载报价单
    async downloadReport(askSheetCode, askSheetId) {
        console.log(`开始下载报价单: ${askSheetCode}, ID: ${askSheetId}`);

        // 检查ELECTRON权限
        if (!this.checkElectronAccessForQuery()) {
            return;
        }

        // 防止重复点击
        const downloadBtn = document.getElementById(`download-btn-${askSheetCode}`);
        if (downloadBtn && downloadBtn.disabled) {
            return;
        }

        try {
            // 显示下载状态
            this.showDownloadLoadingState(askSheetCode);

            // 调用下载API
            const response = await fetch('/api/v1/data/xqd/download', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authManager.token}`
                },
                body: JSON.stringify({
                    askSheetCode: askSheetCode,
                    askSheetId: askSheetId
                })
            });

            if (!response.ok) {
                // 尝试读取错误响应
                let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
                try {
                    const errorData = await response.json();
                    errorMessage = errorData.message || errorData.detail || errorMessage;
                    console.warn('下载API错误响应:', errorData);
                } catch (e) {
                    console.warn('无法解析错误响应为JSON');
                }
                throw new Error(errorMessage);
            }

            // 检查响应类型
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                // 如果返回JSON，说明有错误
                const result = await response.json();
                throw new Error(result.message || '下载失败');
            }

            // 获取文件流
            const blob = await response.blob();

            // 创建下载链接
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${askSheetCode}.xlsx`;
            document.body.appendChild(a);
            a.click();

            // 清理
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            authManager.showNotification(`报价单 ${askSheetCode}.xlsx 下载成功`, 'success');

        } catch (error) {
            console.error('下载报价单错误:', error);
            authManager.showNotification(`下载失败: ${error.message}`, 'error');
        } finally {
            this.hideDownloadLoadingState(askSheetCode);
        }
    },

    // 显示下载加载状态
    showDownloadLoadingState(askSheetCode) {
        const downloadBtn = document.getElementById(`download-btn-${askSheetCode}`);
        if (downloadBtn) {
            downloadBtn.disabled = true;
            downloadBtn.title = '下载中...';
            downloadBtn.innerHTML = `
                <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
            `;
        }
    },

    // 隐藏下载加载状态
    hideDownloadLoadingState(askSheetCode) {
        const downloadBtn = document.getElementById(`download-btn-${askSheetCode}`);
        if (downloadBtn) {
            downloadBtn.disabled = false;
            downloadBtn.title = '下载报价单';
            downloadBtn.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2"></path>
                    <path d="M7 11l5 5l5 -5"></path>
                    <path d="M12 4l0 12"></path>
                </svg>
            `;
        }
    },

    // 初始化日期时间范围选择器
    initializeDateTimeRangePickers() {
        // 为所有日期时间范围输入框添加点击事件
        const rangeInputs = document.querySelectorAll('.datetime-range-input');
        rangeInputs.forEach(input => {
            input.addEventListener('click', function() {
                const picker = this.closest('.datetime-range-picker');
                const panel = picker.querySelector('.datetime-range-panel');

                // 关闭其他打开的面板
                document.querySelectorAll('.datetime-range-panel.show').forEach(p => {
                    if (p !== panel) {
                        p.classList.remove('show');
                    }
                });

                // 切换当前面板
                panel.classList.toggle('show');
            });
        });

        // 点击外部关闭面板
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.datetime-range-picker')) {
                document.querySelectorAll('.datetime-range-panel.show').forEach(panel => {
                    panel.classList.remove('show');
                });
            }
        });
    },

    // 保存查询条件到本地存储（仅保存比价单号）
    saveQueryConditionsToStorage(conditions) {
        try {
            // 只保存比价单号字段到本地存储
            const xqdConditions = {
                askSheetCode: conditions.askSheetCode || ''
            };
            localStorage.setItem('xqd-query-conditions', JSON.stringify(xqdConditions));
        } catch (error) {
            console.warn('无法保存需求单查询条件到本地存储:', error);
        }
    },

    // 从本地存储加载查询条件（仅恢复比价单号）
    loadQueryConditionsFromStorage() {
        try {
            const stored = localStorage.getItem('xqd-query-conditions');
            if (stored) {
                const conditions = JSON.parse(stored);
                const form = document.getElementById('xqd-form');
                if (form) {
                    // 仅恢复比价单号字段值
                    if (conditions.askSheetCode) {
                        const askSheetCodeField = form.querySelector('input[name="askSheetCode"]');
                        if (askSheetCodeField) askSheetCodeField.value = conditions.askSheetCode;
                    }
                }
            }
        } catch (error) {
            console.warn('无法从本地存储加载需求单查询条件:', error);
        }
    },

    // 处理行点击事件（智能选择）
    handleRowClick(rowIndex, event) {
        // 检查是否在文本选择模式
        const selection = window.getSelection();
        if (selection.toString().length > 0) {
            // 如果有文本被选中，不触发行选择
            return;
        }

        // 检查点击的目标元素
        const target = event.target;

        // 如果点击的是可选择的文本内容，允许文本选择
        if (target.tagName === 'CODE' ||
            target.tagName === 'SPAN' ||
            target.closest('.table-cell-ellipsis') ||
            target.closest('.badge') ||
            target.closest('.btn')) {
            // 延迟检查是否真的在选择文本
            setTimeout(() => {
                const newSelection = window.getSelection();
                if (newSelection.toString().length === 0) {
                    // 没有选择文本，触发行选择
                    dataFilter.toggleRowSelection(rowIndex, event);
                }
            }, 10);
        } else {
            // 点击其他区域，直接触发行选择
            dataFilter.toggleRowSelection(rowIndex, event);
        }
    },

    // HTML转义函数
    escapeHtml(text) {
        if (text === null || text === undefined) {
            return '';
        }
        const div = document.createElement('div');
        div.textContent = String(text);
        return div.innerHTML;
    }
};

// 全局函数，保持向后兼容性
function clearXqdQueryCondition() {
    const form = document.getElementById('xqd-form');
    if (form) {
        form.reset();
        // 清空本地存储
        try {
            localStorage.removeItem('xqd-query-conditions');
        } catch (error) {
            console.warn('无法清空需求单本地存储:', error);
        }
        // 清空日期时间范围选择器
        clearAllDateTimeRanges();
        authManager.showNotification('查询条件已清空', 'info');
    }
}

// 关闭日期时间范围选择器
function closeDateTimeRangePicker(button) {
    const panel = button.closest('.datetime-range-panel');
    panel.classList.remove('show');
}

// 设置日期时间范围快捷选项
function setDateTimeRangeShortcut(button, preset) {
    const panel = button.closest('.datetime-range-panel');
    const startDateInput = panel.querySelector('.start-date');
    const startTimeInput = panel.querySelector('.start-time');
    const endDateInput = panel.querySelector('.end-date');
    const endTimeInput = panel.querySelector('.end-time');

    const now = new Date();
    let startDate, endDate;

    switch (preset) {
        case 'today':
            startDate = new Date(now);
            endDate = new Date(now);
            startTimeInput.value = '00:00';
            endTimeInput.value = '23:59';
            break;
        case 'yesterday':
            startDate = new Date(now);
            startDate.setDate(now.getDate() - 1);
            endDate = new Date(startDate);
            startTimeInput.value = '00:00';
            endTimeInput.value = '23:59';
            break;
        case 'last7days':
            startDate = new Date(now);
            startDate.setDate(now.getDate() - 7);
            endDate = new Date(now);
            startTimeInput.value = '00:00';
            endTimeInput.value = '23:59';
            break;
        case 'thisMonth':
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
            endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
            startTimeInput.value = '00:00';
            endTimeInput.value = '23:59';
            break;
        default:
            return;
    }

    startDateInput.value = formatDate(startDate);
    endDateInput.value = formatDate(endDate);
}

// 清空日期时间范围
function clearDateTimeRange(button) {
    const panel = button.closest('.datetime-range-panel');
    const startDateInput = panel.querySelector('.start-date');
    const startTimeInput = panel.querySelector('.start-time');
    const endDateInput = panel.querySelector('.end-date');
    const endTimeInput = panel.querySelector('.end-time');

    startDateInput.value = '';
    startTimeInput.value = '00:00';
    endDateInput.value = '';
    endTimeInput.value = '23:59';
}

// 确认日期时间范围选择
function confirmDateTimeRange(button) {
    const panel = button.closest('.datetime-range-panel');
    const picker = panel.closest('.datetime-range-picker');
    const displayInput = picker.querySelector('.datetime-range-input');
    const startHiddenInput = picker.querySelector('input[name$="Start"]');
    const endHiddenInput = picker.querySelector('input[name$="End"]');

    const startDateInput = panel.querySelector('.start-date');
    const startTimeInput = panel.querySelector('.start-time');
    const endDateInput = panel.querySelector('.end-date');
    const endTimeInput = panel.querySelector('.end-time');

    const startDate = startDateInput.value;
    const startTime = startTimeInput.value;
    const endDate = endDateInput.value;
    const endTime = endTimeInput.value;

    // 验证输入
    if (!startDate && !endDate) {
        // 都为空，清空显示
        displayInput.value = '';
        startHiddenInput.value = '';
        endHiddenInput.value = '';
        panel.classList.remove('show');
        return;
    }

    if (startDate && endDate) {
        const startDateTime = new Date(`${startDate}T${startTime}`);
        const endDateTime = new Date(`${endDate}T${endTime}`);

        if (endDateTime < startDateTime) {
            authManager.showNotification('结束时间不能早于开始时间', 'warning');
            return;
        }
    }

    // 设置隐藏字段值
    if (startDate) {
        startHiddenInput.value = `${startDate}T${startTime}`;
    } else {
        startHiddenInput.value = '';
    }

    if (endDate) {
        endHiddenInput.value = `${endDate}T${endTime}`;
    } else {
        endHiddenInput.value = '';
    }

    // 设置显示文本
    let displayText = '';
    if (startDate && endDate) {
        displayText = `${startDate} ${startTime} 至 ${endDate} ${endTime}`;
    } else if (startDate) {
        displayText = `从 ${startDate} ${startTime} 开始`;
    } else if (endDate) {
        displayText = `到 ${endDate} ${endTime} 结束`;
    }

    displayInput.value = displayText;
    panel.classList.remove('show');

    if (displayText) {
        authManager.showNotification('日期时间范围设置成功', 'success');
    }
}

// 格式化日期为 YYYY-MM-DD 格式
function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

// 清空所有日期时间范围选择器
function clearAllDateTimeRanges() {
    const dateTimeRangePickers = document.querySelectorAll('.datetime-range-picker');
    dateTimeRangePickers.forEach(picker => {
        const input = picker.querySelector('.datetime-range-input');
        const startField = picker.querySelector('input[name$="Start"]');
        const endField = picker.querySelector('input[name$="End"]');

        if (input) input.value = '';
        if (startField) startField.value = '';
        if (endField) endField.value = '';

        // 清空面板中的日期时间输入
        const startDate = picker.querySelector('.start-date');
        const startTime = picker.querySelector('.start-time');
        const endDate = picker.querySelector('.end-date');
        const endTime = picker.querySelector('.end-time');

        if (startDate) startDate.value = '';
        if (startTime) startTime.value = '00:00';
        if (endDate) endDate.value = '';
        if (endTime) endTime.value = '23:59';
    });
}

// 重写authManager的显示主要内容方法，添加ELECTRON权限检查
if (typeof authManager !== 'undefined') {
    const originalShowMainContent = authManager.showMainContent;
    authManager.showMainContent = function() {
        originalShowMainContent.call(this);
        // 在需求单模块中，需要额外检查ELECTRON权限
        setTimeout(() => XqdModule.checkElectronAccess(), 100);
    };

    const originalShowLoginPrompt = authManager.showLoginPrompt;
    authManager.showLoginPrompt = function() {
        originalShowLoginPrompt.call(this);
        // 在需求单模块中，隐藏ELECTRON权限提示
        const electronAccessDenied = document.getElementById('electron-access-denied');
        if (electronAccessDenied) electronAccessDenied.style.display = 'none';
    };
}

// 模块初始化
document.addEventListener('DOMContentLoaded', function() {
    XqdModule.init();
});
