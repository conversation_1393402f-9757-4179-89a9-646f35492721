"""
应用配置管理
支持跨平台配置和环境变量管理
"""
import os
from typing import Optional, List
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""
    
    # 基础应用配置
    APP_NAME: str = "新航发航空数据查询"
    APP_VERSION: str = "1.21.2"
    DEBUG: bool = False
    SECRET_KEY: str = "default-secret-key-change-in-production"
    
    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000

    # 反向代理配置
    BEHIND_PROXY: bool = False  # 是否部署在反向代理后面
    TRUSTED_HOSTS: Optional[List[str]] = None  # 受信任的主机列表
    FORCE_HTTPS: bool = False  # 是否强制使用HTTPS
    
    # JWT配置
    JWT_SECRET_KEY: str = "default-jwt-secret-key"
    JWT_ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 480  # 6小时
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/app.log"
    LOG_ROTATION: str = "1 day"
    LOG_RETENTION: str = "30 days"
    
    # 外部平台配置
    CRYPTO_SERVICE_URL: str = "https://tool.ksccc.cc:4433/crypto.php"
    
    # 航空平台配置
    AVIATION_SSO_URL: str = "https://sso.eavic.com/login"
    AVIATION_SERVICE_URL: str = "https://www.eavic.com/caslogin"
    AVIATION_ELECTRON_URL: str = "https://www.eavic.com/rest/electron/electronShop"
    
    # 航发平台配置
    ENGINE_SSO_URL: str = "https://sso.aecc-mall.com/login"
    ENGINE_SERVICE_URL: str = "https://www.aecc-mall.com/caslogin"
    ENGINE_ELECTRON_URL: str = "https://www.aecc-mall.com/rest/electron/electronShopLogin"
    
    # 电子超市配置
    ESHOP_API_URL: str = "https://eshop.eavic.com/api/member/sso/login"

    # 友商数据采集平台配置 - Playwright模式开关
    COLIPU_USE_PLAYWRIGHT: bool = True  # 晨光平台是否启用Playwright增强模式
    OFFICEMATE_USE_PLAYWRIGHT: bool = True  # 欧菲斯平台是否启用Playwright增强模式
    COMIX_USE_PLAYWRIGHT: bool = False  # 齐心平台是否启用Playwright增强模式
    XFS_USE_PLAYWRIGHT: bool = False  # 鑫方盛平台是否启用Playwright增强模式
    LXWL_USE_PLAYWRIGHT: bool = False  # 领先未来平台是否启用Playwright增强模式
    LXWL_NEW_USE_PLAYWRIGHT: bool = False  # 领先未来(新)平台是否启用Playwright增强模式
    XHGJ_USE_PLAYWRIGHT: bool = False  # 咸亨平台是否启用Playwright增强模式

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# 创建全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings
