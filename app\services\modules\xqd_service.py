"""
需求单查询服务
负责与电子超市需求单API的对接
"""

import aiohttp
import asyncio
from typing import Dict, List, Any, Optional
from app.core.config import settings
from app.core.logging import logger
from app.services.base_electron_service import BaseElectronService


class XqdService(BaseElectronService):
    """需求单查询服务类"""
    
    def __init__(self):
        super().__init__()
        # API端点配置
        # 需求单列表查询
        self.api_url = "https://eshop.eavic.com/api/inquiry/supplier/open_sourcing/query"
        # 报价单下载
        self.download_api_url = "https://eshop.eavic.com/api/inquiry/supplier/open_sourcing/template/export"
        # 需求单详情
        self.detail_api_url = "https://eshop.eavic.com/api/inquiry/supplier/open_sourcing/ask_sheet/detail"
        # 需求单产品列表
        self.detail_list_api_url = "https://eshop.eavic.com/api/inquiry/supplier/open_sourcing/ask_sheet/detail_list"
        # 采购方列表
        self.purchaser_api_url = "https://eshop.eavic.com/api/inquiry/supplier/open_sourcing/pur_name/query"
    
    async def query_ask_sheet_list(
        self,
        access_token: str,
        client_ip: Optional[str] = None,
        askSheetCode: Optional[str] = None,
        askSheetName: Optional[str] = None,
        answerBeginTimeStart: Optional[str] = None,
        answerBeginTimeEnd: Optional[str] = None,
        answerEndTimeStart: Optional[str] = None,
        answerEndTimeEnd: Optional[str] = None,
        askSheetStatus: Optional[str] = None,
        prodName: Optional[str] = None,
        purchaserName: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        查询需求单列表

        Args:
            access_token: ELECTRON平台访问令牌
            askSheetCode: 比价单号（可选）
            askSheetName: 比价单名称（可选）
            answerBeginTimeStart: 报价开始时间起始（可选）
            answerBeginTimeEnd: 报价开始时间结束（可选）
            answerEndTimeStart: 报价截止时间起始（可选）
            answerEndTimeEnd: 报价截止时间结束（可选）
            askSheetStatus: 询价单状态（可选）
            prodName: 采购清单信息（可选）
            purchaserName: 采购方名称（可选）

        Returns:
            包含查询结果的字典
        """
        try:
            logger.info(f"开始需求单查询 - 比价单号: {askSheetCode}, 状态: {askSheetStatus}")

            # 构建请求体
            request_body = {
                "askSheetCode": askSheetCode,
                "askSheetStatus": askSheetStatus,
                "purchaserName": purchaserName,
                "answerBeginTimeStart": self._format_datetime(answerBeginTimeStart),
                "answerBeginTimeEnd": self._format_datetime(answerBeginTimeEnd),
                "answerEndTimeStart": self._format_datetime(answerEndTimeStart),
                "answerEndTimeEnd": self._format_datetime(answerEndTimeEnd),
                "prodName": prodName,
                "askSheetName": askSheetName,
                "limit": 100,
                "current": 1
            }

            logger.debug(f"发送API请求到: {self.api_url}")
            logger.debug(f"请求体: {request_body}")

            # 使用基类的统一请求方法
            response_data = await self._post_request(
                url=self.api_url,
                access_token=access_token,
                client_ip=client_ip,
                json_data=request_body
            )

            # 处理API响应
            return await self._process_api_response(response_data)

        except Exception as e:
            logger.error(f"需求单查询异常: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': f'查询过程中发生错误: {str(e)}',
                'data': []
            }

    async def get_purchaser_list(
        self,
        access_token: str,
        client_ip: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取采购方列表

        Args:
            access_token: ELECTRON平台访问令牌

        Returns:
            包含采购方列表的字典
        """
        try:
            logger.info("开始获取采购方列表")

            # 构建请求体（空对象）
            request_body = {}

            logger.debug(f"发送API请求到: {self.purchaser_api_url}")
            logger.debug(f"请求体: {request_body}")

            # 使用基类的统一请求方法
            response_data = await self._post_request(
                url=self.purchaser_api_url,
                access_token=access_token,
                client_ip=client_ip,
                json_data=request_body
            )

            # 处理API响应
            return await self._process_purchaser_list_response(response_data)

        except Exception as e:
            logger.error(f"采购方列表查询异常: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': f'查询过程中发生错误: {str(e)}',
                'data': []
            }

    async def _process_purchaser_list_response(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理采购方列表API响应数据

        Args:
            response_data: API响应数据

        Returns:
            处理后的响应数据
        """
        try:
            # 检查响应状态码
            if response_data.get("code") != "200":
                error_msg = response_data.get("msg", "未知错误")
                logger.warning(f"采购方列表API返回错误: {error_msg}")

                # 检查是否为权限相关错误
                permission_keywords = ["没有权限", "权限不足", "未授权", "token失效", "登录失效", "会话过期", "无权限", "权限错误"]
                is_permission_error = any(keyword in error_msg for keyword in permission_keywords)

                return {
                    'success': False,
                    'message': f'查询失败: {error_msg}',
                    'data': [],
                    'permission_error': is_permission_error  # 标记权限错误
                }

            # 提取数据部分
            purchaser_list = response_data.get("data", [])

            logger.info(f"采购方列表查询成功，共找到 {len(purchaser_list)} 个采购方")

            return {
                'success': True,
                'message': f'查询成功，找到 {len(purchaser_list)} 个采购方',
                'data': purchaser_list
            }

        except Exception as e:
            logger.error(f"采购方列表响应处理异常: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': f'响应处理过程中发生错误: {str(e)}',
                'data': []
            }

    def _format_datetime(self, datetime_str: Optional[str]) -> Optional[str]:
        """
        格式化日期时间字符串
        
        Args:
            datetime_str: 输入的日期时间字符串
            
        Returns:
            格式化后的日期时间字符串
        """
        if not datetime_str:
            return None
        
        try:
            # 如果输入是 datetime-local 格式 (YYYY-MM-DDTHH:MM)，转换为 API 需要的格式
            if 'T' in datetime_str:
                datetime_str = datetime_str.replace('T', ' ')
            
            # 确保格式为 YYYY-MM-DD HH:MM:SS
            if len(datetime_str) == 16:  # YYYY-MM-DD HH:MM
                datetime_str += ':00'
            
            return datetime_str
        except Exception as e:
            logger.warning(f"日期时间格式化失败: {datetime_str}, 错误: {str(e)}")
            return None
    
    async def _process_api_response(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理API响应数据
        
        Args:
            response_data: API响应的原始数据
            
        Returns:
            处理后的响应数据
        """
        try:
            # 检查响应状态码
            if response_data.get("code") != "200":
                error_msg = response_data.get("msg", "未知错误")
                logger.warning(f"API返回错误: {error_msg}")

                # 检查是否为权限相关错误
                permission_keywords = ["没有权限", "权限不足", "未授权", "token失效", "登录失效", "会话过期", "无权限", "权限错误"]
                is_permission_error = any(keyword in error_msg for keyword in permission_keywords)

                return {
                    'success': False,
                    'message': f'查询失败: {error_msg}',
                    'data': [],
                    'permission_error': is_permission_error  # 标记权限错误
                }
            
            # 提取数据部分
            data_section = response_data.get("data", {})
            ask_sheet_list = data_section.get("list", [])
            total_count = data_section.get("total", "0")
            
            logger.info(f"查询成功，共找到 {len(ask_sheet_list)} 条需求单记录，总计 {total_count} 条")
            
            # 处理需求单数据
            processed_ask_sheets = []
            for i, ask_sheet in enumerate(ask_sheet_list):
                logger.debug(f"处理第 {i+1} 条原始数据: {ask_sheet}")
                processed_ask_sheet = self._process_ask_sheet_item(ask_sheet)
                logger.debug(f"处理后的数据: {processed_ask_sheet}")
                processed_ask_sheets.append(processed_ask_sheet)
            
            return {
                'success': True,
                'message': f'查询成功，找到 {len(processed_ask_sheets)} 条记录',
                'data': processed_ask_sheets,
                'total': int(total_count) if total_count.isdigit() else len(processed_ask_sheets)
            }
            
        except Exception as e:
            logger.error(f"处理API响应异常: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': f'数据处理错误: {str(e)}',
                'data': []
            }
    
    def _process_ask_sheet_item(self, ask_sheet: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理单个需求单项目数据

        Args:
            ask_sheet: 原始需求单数据

        Returns:
            处理后的需求单数据
        """
        try:
            # 确保 prodNum 字段正确处理
            prod_num = ask_sheet.get('prodNum', 0)
            if prod_num is None:
                prod_num = 0
            else:
                try:
                    prod_num = int(prod_num)
                except (ValueError, TypeError):
                    logger.warning(f"无法转换 prodNum 为整数: {prod_num}")
                    prod_num = 0

            logger.debug(f"处理需求单项目: {ask_sheet.get('askSheetCode', '')}, prodNum: {prod_num}")

            return {
                'id': ask_sheet.get('id', ''),
                'askSheetCode': ask_sheet.get('askSheetCode', ''),
                'askSheetId': ask_sheet.get('askSheetId', ''),
                'answerSheetId': ask_sheet.get('answerSheetId', ''),
                'purchaserName': ask_sheet.get('purchaserName', ''),
                'askSheetName': ask_sheet.get('askSheetName', ''),
                'askSheetStatus': ask_sheet.get('askSheetStatus', ''),
                'prodNum': prod_num,
                'purchaserType': ask_sheet.get('purchaserType', ''),
                'answerBeginTime': ask_sheet.get('answerBeginTime', ''),
                'answerEndTime': ask_sheet.get('answerEndTime', ''),
                'answerEffTime': ask_sheet.get('answerEffTime', ''),
                'askSheetUser': ask_sheet.get('askSheetUser', ''),
                'askSheetUserMobile': ask_sheet.get('askSheetUserMobile', ''),
                'answerNum': ask_sheet.get('answerNum', 0),
                'answerSheetCode': ask_sheet.get('answerSheetCode', ''),
                'answerTime': ask_sheet.get('answerTime', ''),
                'answerStatus': ask_sheet.get('answerStatus', ''),
                'showInviteIcon': ask_sheet.get('showInviteIcon', False)
            }
        except Exception as e:
            logger.warning(f"处理需求单项目数据异常: {str(e)}")
            return {
                'id': '',
                'askSheetCode': '',
                'askSheetId': '',
                'answerSheetId': '',
                'purchaserName': '',
                'askSheetName': '',
                'askSheetStatus': '',
                'prodNum': 0,
                'purchaserType': '',
                'answerBeginTime': '',
                'answerEndTime': '',
                'answerEffTime': '',
                'askSheetUser': '',
                'askSheetUserMobile': '',
                'answerNum': 0,
                'answerSheetCode': '',
                'answerTime': '',
                'answerStatus': '',
                'showInviteIcon': False
            }

    async def download_ask_sheet_template(
        self,
        access_token: str,
        ask_sheet_code: str,
        ask_sheet_id: str,
        client_ip: Optional[str] = None
    ) -> bytes:
        """
        下载需求单报价单模板

        Args:
            access_token: ELECTRON平台访问令牌
            ask_sheet_code: 比价单号
            ask_sheet_id: 比价单ID

        Returns:
            Excel文件的字节内容
        """
        try:
            logger.info(f"开始下载报价单模板 - 比价单号: {ask_sheet_code}, ID: {ask_sheet_id}")

            # 构建请求体
            request_body = {
                "askSheetCode": ask_sheet_code,
                "askSheetId": ask_sheet_id
            }

            logger.debug(f"发送下载请求到: {self.download_api_url}")
            logger.debug(f"请求体: {request_body}")

            # 使用基类的统一文件下载方法
            file_content = await self._download_file_request(
                url=self.download_api_url,
                access_token=access_token,
                json_data=request_body,
                client_ip=client_ip,
                timeout=60
            )

            logger.info(f"成功下载报价单，文件大小: {len(file_content)} 字节")
            return file_content

        except Exception as e:
            logger.error(f"报价单下载异常: {str(e)}", exc_info=True)
            raise Exception(f'下载过程中发生错误: {str(e)}')

    async def get_ask_sheet_detail(
        self,
        access_token: str,
        ask_sheet_code: str,
        ask_sheet_id: str,
        client_ip: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取需求单基础信息详情

        Args:
            access_token: ELECTRON平台访问令牌
            ask_sheet_code: 比价单号
            ask_sheet_id: 比价单ID

        Returns:
            包含详情数据的字典
        """
        try:
            logger.info(f"开始获取需求单详情 - 比价单号: {ask_sheet_code}, ID: {ask_sheet_id}")

            # 构建请求体
            request_body = {
                "askSheetCode": ask_sheet_code,
                "askSheetId": ask_sheet_id
            }

            logger.debug(f"发送详情请求到: {self.detail_api_url}")
            logger.debug(f"请求体: {request_body}")

            # 使用基类的统一请求方法
            response_data = await self._post_request(
                url=self.detail_api_url,
                access_token=access_token,
                client_ip=client_ip,
                json_data=request_body
            )

            return self._process_detail_response(response_data)

        except Exception as e:
            logger.error(f"获取需求单详情异常: {str(e)}", exc_info=True)
            raise Exception(f"获取需求单详情失败: {str(e)}")

    async def get_ask_sheet_detail_list(
        self,
        access_token: str,
        ask_sheet_code: str,
        ask_sheet_id: str,
        client_ip: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取需求单产品列表详情

        Args:
            access_token: ELECTRON平台访问令牌
            ask_sheet_code: 比价单号
            ask_sheet_id: 比价单ID

        Returns:
            包含产品列表数据的字典
        """
        try:
            logger.info(f"开始获取需求单产品列表 - 比价单号: {ask_sheet_code}, ID: {ask_sheet_id}")

            # 构建请求体
            request_body = {
                "askSheetCode": ask_sheet_code,
                "askSheetId": ask_sheet_id
            }

            logger.debug(f"发送产品列表请求到: {self.detail_list_api_url}")
            logger.debug(f"请求体: {request_body}")

            # 使用基类的统一请求方法
            response_data = await self._post_request(
                url=self.detail_list_api_url,
                access_token=access_token,
                client_ip=client_ip,
                json_data=request_body
            )

            return self._process_detail_list_response(response_data)

        except Exception as e:
            logger.error(f"获取需求单产品列表异常: {str(e)}", exc_info=True)
            raise Exception(f"获取需求单产品列表失败: {str(e)}")

    def _process_detail_response(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理需求单详情API响应数据

        Args:
            response_data: API响应数据

        Returns:
            处理后的响应数据
        """
        try:
            # 检查响应状态码
            if response_data.get("code") != "200":
                error_msg = response_data.get("msg", "未知错误")
                logger.warning(f"详情API返回错误: {error_msg}")

                # 检查是否为权限相关错误
                permission_keywords = ["没有权限", "权限不足", "未授权", "token失效", "登录失效", "会话过期", "无权限", "权限错误"]
                is_permission_error = any(keyword in error_msg for keyword in permission_keywords)

                return {
                    'success': False,
                    'message': f'查询失败: {error_msg}',
                    'data': {},
                    'permission_error': is_permission_error  # 标记权限错误
                }

            # 提取数据部分
            detail_data = response_data.get("data", {})

            logger.debug(f"详情查询成功，获取到数据: {detail_data}")

            # 处理详情数据
            processed_detail = self._process_detail_item(detail_data)

            return {
                'success': True,
                'message': '查询成功',
                'data': processed_detail
            }

        except Exception as e:
            logger.error(f"处理详情响应数据异常: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': f'数据处理失败: {str(e)}',
                'data': {}
            }

    def _process_detail_list_response(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理需求单产品列表API响应数据

        Args:
            response_data: API响应数据

        Returns:
            处理后的响应数据
        """
        try:
            # 检查响应状态码
            if response_data.get("code") != "200":
                error_msg = response_data.get("msg", "未知错误")
                logger.warning(f"产品列表API返回错误: {error_msg}")

                # 检查是否为权限相关错误
                permission_keywords = ["没有权限", "权限不足", "未授权", "token失效", "登录失效", "会话过期", "无权限", "权限错误"]
                is_permission_error = any(keyword in error_msg for keyword in permission_keywords)

                return {
                    'success': False,
                    'message': f'查询失败: {error_msg}',
                    'data': [],
                    'permission_error': is_permission_error  # 标记权限错误
                }

            # 提取数据部分
            product_list = response_data.get("data", [])

            logger.info(f"产品列表查询成功，共找到 {len(product_list)} 条产品记录")

            # 处理产品列表数据
            processed_products = []
            for i, product in enumerate(product_list):
                logger.debug(f"处理第 {i+1} 条产品数据: {product}")
                processed_product = self._process_product_item(product)
                logger.debug(f"处理后的产品数据: {processed_product}")
                processed_products.append(processed_product)

            return {
                'success': True,
                'message': '查询成功',
                'data': processed_products
            }

        except Exception as e:
            logger.error(f"处理产品列表响应数据异常: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': f'数据处理失败: {str(e)}',
                'data': []
            }

    def _process_detail_item(self, detail: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理单个需求单详情数据项

        Args:
            detail: 原始详情数据

        Returns:
            处理后的详情数据
        """
        try:
            return {
                'askSheetCode': detail.get('askSheetCode', ''),
                'purchaserName': detail.get('purchaserName', ''),
                'askSheetName': detail.get('askSheetName', ''),
                'askSheetStatus': detail.get('askSheetStatus', ''),
                'askSheetType': detail.get('askSheetType', ''),
                'answerEndTime': detail.get('answerEndTime', ''),
                'answerEffTime': detail.get('answerEffTime', ''),
                'askSheetUser': detail.get('askSheetUser', ''),
                'askSheetUserMobile': detail.get('askSheetUserMobile', ''),
                'answerBeginTime': detail.get('answerBeginTime', ''),
                'createTime': detail.get('createTime', ''),
                'updateTime': detail.get('updateTime', '')
            }

        except Exception as e:
            logger.error(f"处理详情数据项异常: {str(e)}", exc_info=True)
            return {}

    def _process_product_item(self, product: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理单个产品数据项

        Args:
            product: 原始产品数据

        Returns:
            处理后的产品数据
        """
        try:
            return {
                'prodName': product.get('prodName', ''),
                'brand': product.get('brandName', ''),  # 修正：brandName -> brand
                'num': product.get('prodNum', 0),       # 修正：prodNum -> num
                'unit': product.get('unit', ''),
                'specDesc': product.get('prodSpec', ''), # 修正：prodSpec -> specDesc
                'manufacturer': product.get('manufacturer', ''),
                'sku': product.get('sku', ''),
                'model': product.get('model', ''),
                'remark': product.get('remark', ''),
                'brandCode': product.get('brandCode', ''),
                'tenantId': product.get('tenantId', '')
            }

        except Exception as e:
            logger.error(f"处理产品数据项异常: {str(e)}", exc_info=True)
            return {}


# 创建全局服务实例
xqd_service = XqdService()
