## v1.21.1 - 2025-08-04 16:45:00
- Changed: 将页面通知容器从右上角移动到右下角位置，提升用户体验
- Changed: 优化通知弹出动画，改为从下方向上滑入的效果
- Added: 新增通知堆叠管理，最多显示5个通知，超出时自动移除最旧的通知
- Added: 实现通知移除动画，支持向下滑出的平滑过渡效果
- Added: 增强通知视觉效果，添加阴影、模糊背景和边框样式
- Changed: 更新移动端响应式设计，确保通知在小屏幕设备上正常显示
- Changed: 修改安全区域适配代码，适配底部位置的通知容器
- Fixed: 优化通知容器布局，使用flex-direction: column-reverse实现底部堆叠

## v1.21.0 - 2025-08-04 16:30:00
- Added: 实现一键快速切换账号功能，支持从下拉菜单直接切换到已保存的账号
- Added: 新增快速切换按钮组，在原切换账号按钮旁添加下拉箭头，提供快速访问入口
- Added: 实现快速切换下拉菜单，按平台分组显示已保存账号，包含账号信息、平台标识和最后使用时间
- Added: 新增全屏遮罩层和加载状态指示，在账号切换过程中提供视觉反馈和防误操作保护
- Added: 实现遮罩层动画效果，包含淡入淡出、缩放动画和模糊背景效果
- Added: 支持ESC键和取消按钮中断切换过程，提供用户控制选项
- Added: 扩展AccountManager类，新增格式化账号列表、一键切换触发等方法
- Added: 扩展AuthManager类，新增自动登录方法，支持无用户交互的完整登录流程
- Added: 实现键盘导航支持，Alt+Q快速打开切换菜单，ESC取消切换操作
- Added: 优化响应式设计，确保在移动端有良好的触摸体验和布局适配
- Changed: 重构账号切换UI为按钮组形式，保持与Tabler.io设计风格一致
- Changed: 优化快速切换的用户体验，包含加载状态、成功失败反馈和错误处理
- Security: 切换过程中禁用页面滚动和其他交互，防止用户误操作

## v1.20.3 - 2025-07-17 12:40:00
- Added: 实现商品详情API端点的智能降级查询机制，提升系统可用性和稳定性
- Changed: 将商品详情查询的主要端点调整为approved/basic_detail，优先获取已审核商品信息
- Added: 新增备用端点basic_detail作为降级查询目标，当主要端点返回错误时自动切换
- Added: 实现详细的查询日志记录，区分主要查询和降级查询的执行情况，便于监控和分析
- Added: 智能错误处理策略，只有错误才触发降级查询，其他错误直接返回
- Changed: 优化查询流程，确保降级查询不影响审核历史数据的正常获取

## v1.20.2 - 2025-07-17 11:33:00
- Fixed: 修复商品详情服务层中access_token变量作用域错误，解决"name 'access_token' is not defined"异常
- Changed: 重构审核历史获取逻辑，将其从响应处理方法移至主查询方法中，确保变量作用域正确

## v1.20.1 - 2025-07-17 11:10:00
- Added: 新增商品详情页面第5个信息卡片"审核历史"，展示商品的完整审核记录
- Added: 集成审核历史API接口，同时调用商品详情和审核历史两个端点获取完整数据
- Added: 实现审核历史数据的时间倒序展示，包含审核阶段、状态、内容、人员和时间信息
- Added: 新增审核阶段和状态的中文格式化显示，支持带颜色的状态徽章
- Added: 实现审核历史的独立加载状态、错误处理和空状态展示
- Added: 添加审核历史相关CSS样式，支持响应式布局和深色主题适配
- Changed: 优化商品详情服务层，确保审核历史查询失败不影响商品详情的正常显示

## v1.20.0 - 2025-07-17 10:30:00
- Added: 新增商品详情功能模块，支持从商品状态查询页面跳转查看商品的详细信息和规格参数
- Added: 商品状态查询页面新增"查看详情"操作按钮，点击可跳转到商品详情页面
- Added: 实现完整的商品详情功能模块，支持通过真实API获取和展示商品详细信息
- Added: 新增商品详情后端API端点(/api/v1/data/product/detail)，对接电子超市平台商品详情接口
- Added: 实现商品详情服务层，包含完整的外部API对接、错误处理和数据处理逻辑
- Added: 重构商品详情页面为4个信息卡片：基础信息、价格信息、厂商信息、辅助信息
- Added: 实现图片预览功能，支持点击商品主图、子图、报价单、商品资质图片放大查看
- Added: 完善错误处理和用户体验，包含重试机制、友好错误提示、加载状态和空状态展示
- Changed: 更新前端JavaScript实现真实API调用，替换模拟数据展示
- Changed: 优化商品状态查询表格列配置，新增操作列以容纳查看详情按钮

## v1.19.5 - 2025-07-17 09:43:11
- Changed: 优化询价单模块状态显示逻辑，当answerStatus为'QUOTED'时优先显示"已报价"状态，提升用户对已报价条目的识别度
- Changed: 优化询价单模块下载按钮显示逻辑，当answerStatus为'QUOTED'时隐藏下载报价单按钮

## v1.19.4 - 2025-07-15 12:04:00
- Changed: 优化导出数据部分字段替换为中文，以便于用户更好的识别

## v1.19.3 - 2025-07-09 11:45:00
- Added: 需求凭证模块多行编辑器新增实时统计显示，显示已输入的比价单号和SKU编码数量
- Added: 商品状态查询模块多行编辑器新增实时统计显示，显示已输入的SKU编码数量
- Changed: 优化多行编辑器用户体验，提供清晰的输入反馈和数量统计信息

## v1.19.2 - 2025-07-09 11:30:00
- Fixed: 需求凭证模块多行编辑功能数据同步问题，下拉菜单显示时自动导入当前输入框内容到多行编辑器
- Fixed: 需求凭证模块多行编辑器现在能正确显示已保存的比价单号和SKU编码数据
- Fixed: 修复需求凭证模块多行编辑器空格转换为换行符的数据格式转换逻辑
- Changed: 优化需求凭证模块多行编辑用户体验，确保数据在单行输入框和多行编辑器之间正确同步

## v1.19.1 - 2025-07-09 10:15:00
- Changed: 商品状态查询模块新增多行编辑功能，将原多行文本框改为单行输入框配合多行编辑器
- Changed: 商品状态查询模块支持浮动多行编辑器，提升大量SKU编码输入的用户体验
- Changed: 商品状态查询模块多行编辑器支持每行一个SKU编码或空格、制表符分隔的输入方式
- Changed: 商品状态查询模块UI优化，采用与需求凭证模块相同的多行编辑设计模式
- Changed: 商品状态查询模块保持向后兼容性，不影响现有批量查询和智能两阶段查询功能

## v1.19.0 - 2025-07-09 09:30:00
- Added: 需求凭证模块新增批量查询支持，支持空格、换行符、制表符分隔的多值输入
- Added: 需求凭证模块实现异步并发查询，支持多个比价单号或SKU的同时查询，保持用户输入顺序
- Added: 需求凭证模块前端自动检测批量模式，显示相应的加载提示和结果汇总信息
- Changed: 需求凭证模块前端输入框提示信息更新，明确说明支持批量查询功能
- Changed: 统一项目中response_time的计算标准，所有模块都在API层计算而非服务层返回

## v1.18.4 - 2025-07-08 16:40
- Fixed: 修复商品状态模块当SKU编码为空时使用商品名称查询的逻辑bug，现在支持仅商品名称的查询操作
- Fixed: 修复商品状态模块智能查询逻辑，当只有商品名称没有SKU时自动跳过分阶段查询，使用单次查询模式
- Fixed: 修复query_product_integrated方法中的查询条件验证，增强对空值参数的处理能力
- Added: 新增_query_by_name_only方法，专门处理仅商品名称的单次查询，避免不必要的智能整合逻辑
- Added: 新增查询条件验证机制，当SKU和商品名称都为空时返回明确的错误提示信息
- Added: 新增移动端导航JavaScript文件（mobile-nav.js），专门处理移动端底部导航栏的交互逻辑
- Added: 新增移动端触摸事件支持，包括touchstart、touchend事件处理和触摸反馈效果
- Added: 新增移动端子菜单显示和隐藏功能，支持平台选择下拉列表的正常操作
- Changed: 优化表格文本显示标准化，统一使用CSS .table-cell-ellipsis类替代JavaScript字符截断逻辑
- Changed: 改进商品状态、需求单、询价单三个模块的表格显示，移除text-truncate类使用新的省略号样式
- Changed: 优化formatDelistReason和formatApprovalContent方法，简化字符截断逻辑但保留小眼睛查看详情功能
- Changed: 改进移动端底部导航栏CSS样式，增强触摸体验和视觉反馈效果
- Changed: 优化移动端子菜单的z-index层级和定位，确保在移动设备上正确显示

## v1.18.3 - 2025-07-08 16:30
- Added: 商品状态模块智能两阶段查询优化，新增下架商品审核信息补充功能
- Added: 自动检测下架状态且非永久下架的商品（shelf_status=DOWN_SHELF且delist_type≠OPERATE_DELIST_FOREVER）
- Added: 将符合条件的下架商品SKU自动添加到第二阶段审核查询列表中，确保获取完整的审核状态信息
- Changed: 优化所有页面，使用宽幅显示，以更好的显示更多内容
- Changed: 优化第二阶段查询逻辑，合并未找到的SKU和符合条件的下架商品SKU进行统一查询
- Changed: 增强数据整合功能，确保下架商品的审核信息（审核状态、驳回原因、操作员等）能正确补充到最终结果中
- Improved: 提升商品状态查询的数据完整性，特别是下架商品的审核状态信息展示
- Added: 完整的单元测试覆盖，验证下架商品检测、数据整合和SKU处理逻辑的正确性

## v1.18.2 - 2025-07-07 15:33
- Added: 智能Cookie失效检测系统，支持HTTP状态码检测（401、403、302等）和页面内容关键词检测
- Added: 自动Cookie刷新机制，包含refresh_cookies_if_needed()和validate_cookies()方法
- Added: 高失败率自动重试系统，失败率超过50%时自动刷新Cookie并重新采集失败的SKU
- Added: 统一Cookie验证器工具类（cookie_validator），提供标准化的失效检测逻辑
- Added: 线程池执行机制避免事件循环冲突，确保Playwright与异步框架兼容
- Changed: 升级所有7个友商数据采集模块的_scrape_with_playwright_mode()方法，集成智能Cookie管理
- Changed: 重构playwright_manager，新增Cookie验证和自动刷新功能
- Changed: 优化错误处理和日志记录，提供详细的Cookie状态和刷新事件日志
- Fixed: 解决长时间运行过程中Cookie失效导致的数据采集失败问题
- Fixed: 修复Cookie过期后需要手动重启服务的问题，实现自动恢复
- Improved: 预期数据采集成功率提升15-30%，显著增强系统稳定性
- Improved: 统一所有模块的Cookie管理逻辑，确保一致的错误处理和恢复机制

## v1.18.1 - 2025-07-07 11:47
- Fixed: 修复商品状态采集模块智能两阶段查询流程的数据异常问题，确保第一阶段状态查询和第二阶段审核查询正确执行
- Fixed: 修复SKU重复处理和排序问题，保留用户输入的重复SKU并严格维护原始输入顺序，支持重复SKU的数据复制
- Changed: 优化数据整合逻辑，改为按用户输入顺序直接处理每个SKU（包括重复SKU），避免传统的分步骤处理导致的顺序错乱
- Changed: 改进第二阶段查询逻辑，使用去重SKU进行API查询以提高效率，同时在结果整合时正确处理重复SKU的数据复制

## v1.18.0 - 2025-07-05 22:43
- Added: PWA（渐进式Web应用）功能支持，包含Web App Manifest配置文件和完整的PWA元标签设置
- Added: PWA应用图标支持，提供72x72到512x512多种尺寸的应用图标，支持maskable图标类型
- Added: PWA快捷方式功能，支持友商数据采集、需求凭证查询、需求单查询、品牌库管理的快速访问
- Added: 移动端PWA优化，支持standalone显示模式、安全区域适配（viewport-fit=cover）、iOS Safari兼容性
- Added: BaseElectronService统一架构基类，整合所有ELECTRON平台模块的认证逻辑、HTTP请求模式和错误处理机制
- Added: 统一的异步HTTP请求方法_make_request，支持GET、POST、PUT、DELETE等HTTP方法和完整的错误处理
- Added: 统一的请求头构建方法_build_request_headers，支持认证令牌和自定义请求头
- Added: 完善的超时处理和网络错误处理机制，提供友好的错误提示信息
- Added: 添加移动设备下的底部导航栏，提供友商数据、平台数据的快速访问
- Changed: 重构BrandService、XqdService、ProductService、XqpzService等服务类，统一继承BaseElectronService
- Changed: 优化所有ELECTRON平台模块的代码结构，消除重复的认证和请求逻辑，代码量减少约40%
- Changed: 统一所有服务类的错误处理模式，提供一致的异常信息和日志记录
- Changed: 优化Dashboard模块统计功能，移除登录验证限制，允许未登录用户查看基础统计信息
- Changed: 改进PWA主题配置，使用Tabler.io标准主题色#206bc4，支持明暗主题自动适配
- Fixed: 修复品牌库模块的API请求认证问题，确保所有请求正确包含Authorization头部
- Fixed: 修复各模块服务类中重复代码导致的维护困难问题，通过统一基类实现代码复用
- Fixed: 修复Dashboard统计显示异常问题，优化统计数据获取和显示逻辑
- Improved: 大幅提升代码可维护性，通过统一架构减少代码重复，便于后续功能扩展和bug修复
- Improved: 增强移动端用户体验，PWA功能支持离线访问、桌面图标安装、全屏显示等原生应用体验
- Improved: 优化网络请求性能，统一的连接池管理和超时配置，提升API调用效率

## v1.17.0 - 2025-07-04 16:59
- Added: 品牌库模块新增品牌功能，支持通过模态框形式添加新品牌信息，包含完整的表单字段和文件上传功能
- Added: 品牌LOGO和商标网截图文件上传功能，支持拖拽上传、点击上传、图片预览、文件格式验证（JPG/PNG）
- Added: 品牌LOGO图片裁剪功能，强制裁剪为102×36像素尺寸，集成Cropper.js实现可视化裁剪界面
- Added: 实现品牌库文件上传真实API对接
- Added: 新增品牌文件上传服务层方法upload_brand_file，支持multipart/form-data格式上传
- Added: 新增品牌文件上传API路由/api/v1/modules/brand/upload，集成完整的权限验证和错误处理
- Added: 品牌信息表单字段，支持品牌中文名称、英文名称（二选一必填）、商标持有人（必填）、品牌原产地选择（国产/进口）
- Added: 国际分类多选下拉框，包含第1-45类完整商标分类选项，支持Ctrl键多选操作
- Added: 新增品牌表单验证逻辑，实现中英文名称二选一必填验证、必填字段检查、文件格式验证、实时表单状态更新
- Added: 表单完成进度指示器，实时显示基本信息、LOGO上传、截图上传的完成状态
- Added: 智能提交按钮控制，只有所有必填项完成且文件上传成功后才可点击提交
- Added: 响应式设计支持，确保新增品牌功能在移动端（320px+）和桌面端都能正常显示和操作
- Added: iOS Safari兼容性优化，支持安全区域适配和触摸友好的交互设计
- Changed: 重构品牌LOGO上传功能，移除复杂的裁剪模态框实现，改为简洁的内嵌式裁剪
- Changed: 重新设计交互流程：上传图片→上传区域转换为裁剪界面→调整裁剪→确认后显示预览
- Changed: 优化品牌LOGO上传流程，实现上传后自动启动裁剪功能，简化用户操作步骤
- Changed: 优化BrandService类的请求头管理，统一所有API请求的头部配置模式
- Changed: 重构uploadImageFile方法，替换模拟上传为真实API调用，支持文件类型和大小验证
- Changed: 品牌库页面操作区域布局，在返回控制台按钮前添加新增品牌按钮，使用主色调突出重要操作
- Changed: 模态框交互设计，禁用ESC键关闭和背景点击关闭，确保用户明确操作意图
- Fixed: 修复品牌审核查询模块字段校验问题，优化_process_brand_approval_item方法支持None值处理和IMPORT品牌类型映射
- Fixed: 解决审核状态为"驳回"(REJECT)的品牌记录处理异常问题，增强数据处理的健壮性和异常处理机制

## v1.16.0 - 2025-07-03 19:31
- Added: 品牌库模块查询模式切换功能，支持"品牌查询"和"审核查询"两种模式，用户可根据需求选择不同的查询类型
- Added: 审核查询API接口/api/v1/data/brand/approval，对接电子超市品牌审核管理接口，支持按品牌名称和审核状态查询
- Added: 品牌审核状态显示功能，支持待审核（橙色）、驳回（红色）、审核通过（绿色）三种状态的可视化标识和徽章显示
- Added: 查询类型选择组件，使用不同颜色主题区分品牌查询（蓝色主题）和审核查询（橙色主题）模式，提升用户界面识别度
- Added: 品牌审核数据处理服务BrandService.query_brand_approval_list方法，实现审核状态映射和数据格式化
- Added: 审核查询表单验证逻辑，确保用户至少填写品牌名称字段进行查询，提供友好的错误提示
- Changed: 优化品牌库模块UI布局，将模式切换按钮从卡片头部移至表单内部，使操作更贴近相关内容
- Changed: 重构品牌库模块JavaScript架构，支持动态API端点切换和不同模式的表单字段管理
- Changed: 扩展品牌信息数据模型，增加审核状态相关字段支持，兼容品牌查询和审核查询两种模式
- Fixed: 修复品牌库模块中日志记录的字符串格式化错误，解决包含花括号的异常信息导致的KeyError问题
- Fixed: 修复模式切换时表单字段显示/隐藏逻辑，确保不同查询模式下的表单控件正确切换
- Fixed: 修复品牌库模块Logo图片加载失败时的错误处理，使用SVG图标替代纯文字提示

## v1.15.0 - 2025-07-03 18:00
- Added: 品牌库采集模块，支持品牌编号、品牌名称、启用状态的多条件查询功能
- Added: 品牌库数据展示功能，包含品牌Logo显示、商标持有人、品牌类型（国产/进口）、状态标识等完整信息
- Added: 品牌库模块API接口/api/v1/data/brand，实现与电子超市平台的品牌管理接口对接
- Added: 品牌库服务层BrandService，使用异步HTTP请求模式，支持ELECTRON平台认证和权限验证
- Added: 品牌库前端交互功能，包含查询表单、数据筛选、分页显示、复制导出等完整功能
- Added: 品牌Logo图片显示组件，支持图片预览、加载失败处理、占位符显示等功能
- Added: 品牌状态和类型的可视化标识，使用不同颜色的徽章区分启用/禁用状态和国产/进口类型
- Added: 品牌库模块专用样式brand.css，实现Logo容器、状态徽章、响应式设计和暗色主题适配
- Added: 品牌库数据复制功能，支持制表符分隔格式（适合Excel粘贴）和CSV导出功能
- Added: 品牌库模块路由注册，页面访问地址/modules/brand，集成到主应用导航系统
- Added: 品牌库查询条件本地存储功能，自动保存和恢复用户的查询历史，提升使用体验
- Changed: 扩展分析统计模块，新增brand模块的查询日志记录和统计分析支持

## v1.14.1 - 2025-07-03 11:55
- Added: 平台选择缓存功能，自动保存用户最后选择的平台到localStorage，应用重启时智能恢复上次使用的平台
- Added: 平台独立的SKU列表缓存机制，每个友商平台（晨光、领先未来等）维护独立的查询历史记录
- Added: 平台缓存清理方法clearPlatformCache和clearAllCache，支持单平台或全部缓存数据的清理操作
- Added: 平台选择有效性验证机制，确保从缓存加载的平台ID在有效范围内，无效数据自动清理并使用默认平台
- Changed: 优化缓存键名结构，从单一的scraper-query-conditions改为平台独立格式scraper_query_content_${platformId}
- Changed: 改进平台切换逻辑switchPlatform，集成平台选择保存和对应SKU历史数据的智能加载功能
- Changed: 增强初始化流程initPlatformSelector，启动时优先从缓存恢复用户上次选择的平台，无缓存时使用默认平台
- Changed: 重构saveQueryConditionsToStorage和loadQueryConditionsFromStorage方法，支持平台独立的数据存储和加载
- Improved: 增强错误处理机制，localStorage操作失败时提供完整的降级处理，确保应用在存储受限环境下正常运行
- Improved: 优化用户体验，平台切换时无缝加载对应平台的历史查询数据，提供连贯的使用体验
- Improved: 完善缓存数据管理，提供详细的日志记录和状态反馈，便于问题排查和功能验证

## v1.14.0 - 2025-07-03 11:42
- Added: 友商数据采集器实时流式数据更新机制，查询开始时为每个SKU创建占位行，通过SSE实时更新对应行数据
- Added: 分页功能支持，集成PaginationManager分页管理器，支持大量数据的分页显示和浏览
- Added: 性能优化的表格渲染机制，使用防抖技术和DocumentFragment优化DOM操作，提升渲染性能
- Added: 移动端响应式设计优化，在小屏幕设备上自动隐藏部分列以节省空间，优化触摸交互体验
- Added: SKU占位机制，查询开始时立即显示所有输入SKU的加载状态行，提供更好的用户反馈
- Changed: 重构数据展示机制，从静态表格模式改为实时流式更新模式，支持数据的动态更新和分页显示
- Changed: 优化数据复制和导出功能，统一使用分页管理器作为数据源，确保导出数据的完整性和一致性
- Changed: 改进加载状态显示方式，使用精细的行级加载状态替代全局覆盖层，提供更清晰的加载反馈
- Changed: 重构表格HTML生成逻辑，使用数组拼接和批量处理优化性能，支持大量数据的高效渲染
- Fixed: 修复分页管理器实例名称不匹配导致的JavaScript错误（paginationManager_scraper未定义）
- Fixed: 解决数据更新时分页状态维护问题，确保实时数据更新不会影响当前页面显示
- Fixed: 修复实时数据流更新过程中的数据同步问题，确保allData数组与分页管理器数据的一致性
- Removed: 移除全局加载覆盖层功能，简化加载状态管理，避免重复的加载提示影响用户体验
- Removed: 清理未使用的CSS样式类（table-active、loading-state等）和JavaScript空方法
- Removed: 删除冗余的代码注释和开发过程中留下的调试代码，提升代码可读性和维护性

## v1.13.0 - 2025-07-02 23:22
- Added: 商品状态查询模块智能整合查询功能增强，确保所有用户输入的SKU都包含在最终结果中
- Added: 新增_parse_input_skus方法，智能解析用户输入的SKU字符串，支持多种分隔符（空格、逗号、换行符）
- Added: 新增_create_default_sku_record方法，为无数据的SKU创建默认记录，显示"未找到商品信息"
- Added: 第三阶段无数据SKU处理逻辑，确保所有输入SKU都出现在查询结果中
- Added: 详细统计信息返回，区分显示有数据SKU数量和无数据SKU数量
- Improved: 增强_integrate_product_data方法，支持输入SKU跟踪和无数据SKU处理
- Improved: 优化query_product_integrated方法，记录用户输入的所有SKU并跟踪查询结果
- Improved: 前端表格显示优化，为无数据SKU行添加特殊样式（黄色背景）
- Improved: 前端通知消息优化，显示详细的查询统计信息（找到数据X条，无数据SKU Y条）
- Improved: hasValidData方法增强，正确识别和处理无数据SKU的默认值
- Fixed: 解决用户输入SKU在查询结果中遗漏的问题，确保数据完整性
- Fixed: 优化SKU顺序保持逻辑，确保最终结果严格按照用户输入顺序排列

## v1.12.0 - 2025-07-02 23:05
- Added: 商品状态查询模块新增智能列隐藏功能，自动隐藏无数据的列以优化表格显示
- Added: 新增getVisibleColumns方法，智能检测每列数据的有效性，自动过滤空列
- Added: 新增getHiddenColumns方法，获取被隐藏列的名称列表用于用户提示
- Added: 新增hasValidData方法，精确判断数据是否为有效值（排除null、undefined、空字符串、"-"等）
- Added: 新增renderTableCell方法，支持动态列渲染，根据列配置生成对应的表格单元格
- Added: 智能提示功能，当有列被隐藏时显示友好的提示信息告知用户
- Improved: 重构renderIntegratedCustomTable方法，实现动态表头和表格内容生成
- Improved: 优化表格空间利用率，只显示有意义的数据列，提升用户体验
- Improved: 保持必要字段始终显示（SKU编码、商品名称、商品品目、售价、更新时间）
- Improved: 保持所有现有交互功能（行选择、点击事件、数据筛选等）完全正常

## v1.11.0 - 2025-07-02 22:50
- Removed: 清理商品状态查询模块冗余代码，移除已被智能整合查询替代的旧接口
- Removed: 删除后端API接口/product和/product-approval，统一使用/product-integrated智能整合查询
- Removed: 删除服务层query_product_approval方法，保留query_product_status用于单SKU查询
- Removed: 删除前端executeProductStatusQuery和executeProductApprovalQuery查询方法
- Removed: 删除前端renderStatusCustomTable和renderApprovalCustomTable表格渲染器
- Simplified: 简化setupFilter方法的条件判断逻辑，统一使用智能整合查询模式
- Simplified: 优化列结构定义，统一使用11字段完整格式，移除查询类型相关的条件分支
- Improved: 代码库更加简洁，减少维护负担，消除功能重复
- Improved: 保持向后兼容性，确保需求凭证模块的单SKU查询功能正常工作

## v1.10.0 - 2025-07-02 20:06
- Added: 统一友商数据采集模块输出格式，实现标准化的9字段结构
- Changed: 友商模块复制功能采用新标准格式
- Changed: 友商模块Excel导出功能更新为统一标准格式，包含正确的字段顺序和列宽设置
- Changed: 友商模块CSV导出功能更新为统一标准格式，使用逗号分隔符保持标准CSV兼容性
- Improved: 统一所有友商采集模块（晨光、齐心、鑫方盛、欧菲斯、领先未来、领先未来(新)、咸亨）的数据输出格式
- Added: 新增空字段占位符，保持数据结构的完整性和一致性
- Improved: 优化prepareExportData方法，支持统一标准格式的数据映射和转换

## v1.9.3 - 2025-07-02 19:50
- Fixed: 修复智能整合查询数据丢失问题，确保审核状态数据正确包含在最终结果中
- Improved: 优化_integrate_product_data方法，采用两阶段整合策略：先处理状态数据补充审核信息，再添加仅存在于审核数据中的SKU
- Added: 新增processed_skus集合跟踪已处理的SKU，避免重复处理和数据丢失
- Improved: 完善数据整合日志记录，详细显示状态数据、审核数据和整合结果的数量统计
- Fixed: 解决只在审核查询中存在而在状态查询中不存在的SKU被遗漏的问题

## v1.9.2 - 2025-07-02 19:30
- Fixed: 修复智能整合查询中缺失的_sort_by_original_sku_order方法错误
- Added: ProductStatusService新增_sort_by_original_sku_order方法，支持按原始SKU输入顺序排序
- Improved: 智能整合查询现在能正确按照用户输入的SKU顺序返回结果，保持数据一致性
- Improved: 新增的排序方法复用现有的_sort_products_by_sku_order逻辑，确保排序算法一致性
- Fixed: 添加完善的错误处理机制，排序失败时返回原始数据而不是抛出异常

## v1.9.1 - 2025-07-02 19:10
- Fixed: 修复智能整合查询API认证错误，添加缺失的Authorization认证头
- Fixed: 修复ProductService实例化错误，使用正确的product_status_service全局实例
- Fixed: 添加智能整合查询接口的参数验证，确保与现有接口一致
- Fixed: 完善前端错误处理和HTTP状态码检查，提升用户体验
- Improved: 统一智能整合查询接口的认证流程，与现有product接口保持完全一致

## v1.9.0 - 2025-07-02 18:35
- Added: 商品状态查询模块新增智能整合查询功能，自动执行商品状态和审核状态的两阶段查询
- Added: 新增/api/v1/data/product-integrated智能整合查询API接口，提供完整的11字段数据输出
- Added: ProductService新增query_product_integrated方法，实现智能两阶段查询策略
- Added: ProductService新增_integrate_product_data方法，实现数据整合与SKU顺序保持
- Added: 前端新增renderIntegratedCustomTable方法，支持11字段完整数据表格渲染
- Changed: 商品状态查询模块界面优化，移除查询类型选择，改为智能查询模式
- Changed: 统一输出11字段完整信息：SKU编码、商品名称、商品品目、售价、上架状态、下架类型、下架原因、审核状态、驳回原因、操作员、更新时间
- Changed: 查询逻辑优化为三阶段：第一阶段商品状态批量查询、第二阶段审核状态补充查询、第三阶段数据整合与排序
- Removed: 移除前端查询类型选择UI组件，简化用户操作流程
- Removed: 移除handleQueryTypeChange和bindQueryTypeEvents等查询类型相关方法
- Improved: 查询结果严格按照用户输入的SKU顺序进行排序输出，保持数据一致性
- Improved: 智能检测缺失状态信息的SKU，自动补充审核状态数据，提升数据完整性

## v1.8.8 - 2025-07-02 17:23
- Added: DataFilter类新增convertToTabSeparated方法，支持制表符分隔的数据格式转换
- Added: DataFilter类新增copySelectedDataWithTabs方法，专门用于制表符格式的数据复制
- Added: DataFilter类新增copyData统一复制方法，根据配置自动选择复制格式
- Added: DataFilter类构造函数新增useTabSeparatedCopy配置选项，控制复制格式类型
- Changed: 需求单模块数据复制格式从逗号分隔改为制表符分隔，适合Excel自动分列
- Changed: 需求凭证模块数据复制格式从逗号分隔改为制表符分隔，适合Excel自动分列
- Changed: 商品状态模块数据复制格式从逗号分隔改为制表符分隔，适合Excel自动分列
- Fixed: 修复键盘快捷键Ctrl+C调用错误的复制方法问题，现在正确调用统一复制接口
- Fixed: 保持CSV导出功能继续使用逗号分隔符，确保标准CSV格式兼容性

## v1.8.7 - 2025-07-02 15:33
- Added: 需求单模块添加"发布企业"过滤器，支持关键词搜索和模糊匹配
- Added: 新增刷新按钮功能，支持手动刷新企业列表，包含加载状态动画和错误处理
- Improved: 状态提示信息从外部form-text区域移动到下拉框内部显示，界面更加简洁统一
- Removed: 完全移除showPurchaserStatus()方法及其所有调用，简化代码结构
- Fixed: 修复刷新按钮旋转动画异常问题，确保动画只应用到图标元素而不影响容器

## v1.8.6 - 2025-07-02 11:30
- Fixed: 修复商品状态查询模块查询结果排序问题，确保输出结果严格按照用户输入的SKU编码顺序进行排列
- Fixed: 优化后端API响应处理逻辑，添加SKU顺序保持机制，解决多SKU查询时结果顺序混乱的问题
- Improved: 增强商品状态查询的用户体验，查询结果现在完全匹配用户输入的SKU顺序

## v1.8.5 - 2025-06-30 11:30
- Added: 新增友商数据采集页面平台选择状态持久化功能，用户选择的平台会自动保存到本地存储
- Added: 页面刷新后自动恢复用户上次选择的平台，提升用户体验
- Added: 清空功能现在会同时清除保存的平台选择，确保数据一致性
- Improved: 优化平台切换逻辑，增加选择状态的自动保存和验证机制

## v1.8.4 - 2025-06-30 11:02
- Fixed: 修复领先未来(新)模块并发处理问题，将同步requests替换为异步aiohttp，实现真正的并发处理
- Improved: 优化并发性能，3个SKU并发处理时间从6秒降低到1.4秒，性能提升85%
- Added: 增加详细的并发执行日志，清楚显示任务等待信号量和开始执行的过程
- Changed: 优化请求延迟时间，从1-2秒减少到0.5-1秒，提高整体处理速度

## v1.8.3 - 2025-06-30 10:40
- Fixed: 修复领先未来(新)模块流式数据采集中的索引异常问题，确保前端表格行索引与数据索引完全匹配
- Fixed: 完善流式数据响应格式，添加缺失的外层index字段，保持与其他模块的一致性

## v1.8.2 - 2025-06-30 10:15
- Added: 新增领先未来(新)平台数据采集模块，支持基于API接口的直接数据获取
- Added: 实现JSON数据解析和字段映射功能，支持批量采集、流式响应和单个重试
- Added: 新增配置项LXWL_NEW_USE_PLAYWRIGHT控制Playwright模式开关
- Changed: 优化数据采集架构，支持API接口和XPath解析两种模式并存

## v1.8.1 - 2025-06-16 10:08
- Added: 新增更新日志功能，支持在footer区域查看版本更新记录
- Added: 支持彩色标识不同类型的更新内容
- Fixed: 修复部分模块账号登录异常的问题

## v1.8.0 - 2025-06-15 15:42
- Changed: 优化用户界面体验，增强版本信息展示
- Fixed: 修复部分模块的样式显示问题

## v1.7.5 - 2025-06-14 22:01
- Fixed: 修复下载任务进度异常的问题
- Added: 支持批量删除下载任务
- Changed: 优化前端状态刷新逻辑
- Removed: 移除过时的API接口

## v1.7.0 - 2025-06-13 11:35
- Added: 新增友商数据采集模块
- Added: 支持晨光、欧菲斯、齐心等平台数据采集
- Changed: 重构模块化架构，提升代码可维护性
- Fixed: 修复认证令牌过期处理逻辑

## v1.6.0 - 2025-06-13 10:24
- Added: 新增仪表盘
- Added: 新增数据统计

## v1.5.0 - 2025-06-13 00:12
- Added: 新增需求单详情查看功能
- Added: 支持报价单模板下载
- Changed: 优化数据采集性能
- Fixed: 修复分页显示异常问题

## v1.4.0 - 2025-06-12 23:11
- Fixed: 修复报价单下载异常的问题
- Changed: 优化日期选择器
- Changed: 修复数据本地保存的问题

## v1.3.0 - 2025-06-12 20:20
- Added: 新增需求单详情查看功能
- Added: 新增报价单模板下载
- Added: 新增筛选器
- Changed: 优化数据采集性能
- Fixed: 修复分页显示异常问题

## v1.2.0 - 2025-06-12 16:30
- Added: 新增凭证采集功能
- Added: 新增报价单采集功能
- Added: 支持报价单模板下载

## v1.1.0 - 2025-06-12 14:20
- Added: 新增商品状态查询模块
- Added: 新增商品状态查询
- Added: 新增商品审核状态查询
- Fixed: 修复登录问题

## v1.0.0 - 2025-06-12 09:12
- Added: 立项
- Added: 添加航空登录功能
- Added: 添加航发登录功能
- Added: 添加账号保存功能
- Added: 添加JWT安全模块
- Added: 创建前端框架
- Added: 创建后端模块
