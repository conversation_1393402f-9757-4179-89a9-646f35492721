/* 需求单查询模块样式 */

/* ========== 搜索下拉框组件样式 ========== */

/* 搜索下拉框容器 */
.search-dropdown-container {
    position: relative;
    width: 100%;
}

/* 搜索输入框包装器 */
.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

/* 搜索输入框 */
.search-input {
    padding-right: 80px !important; /* 为刷新按钮、清除按钮和下拉箭头留出空间 */
    cursor: pointer;
    transition: all 0.2s ease-in-out;
}

.search-input:focus {
    cursor: text;
    border-color: #3b82f6;
    box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
}

/* 移除readonly相关样式，输入框现在始终可编辑以支持移动设备虚拟键盘 */

/* 移动设备优化 */
@media (max-width: 768px) {
    .search-input {
        /* 确保移动设备上的输入体验 */
        -webkit-user-select: text;
        -moz-user-select: text;
        -ms-user-select: text;
        user-select: text;

        /* 防止iOS Safari的缩放 */
        font-size: 16px;
    }

    /* 移动设备上的虚拟键盘优化 */
    .search-input:focus {
        /* 防止页面滚动到输入框 */
        scroll-margin-top: 100px;
    }
}

/* 刷新按钮 */
.refresh-btn {
    position: absolute;
    right: 30px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
}

.refresh-btn:hover:not(.loading) {
    color: #3b82f6;
    background-color: rgba(59, 130, 246, 0.1);
}

.refresh-btn:active:not(.loading) {
    transform: translateY(-50%) scale(0.95);
}

/* 刷新图标 */
.refresh-icon {
    transition: transform 0.2s ease-in-out;
    transform-origin: center;
}

/* 刷新按钮加载状态 */
.refresh-btn.loading {
    color: #3b82f6;
    cursor: not-allowed;
    /* 确保按钮本身的transform不受影响 */
    transform: translateY(-50%);
}

/* 只对图标应用旋转动画 */
.refresh-btn.loading .refresh-icon {
    animation: refreshSpin 1s linear infinite;
}

/* 刷新按钮专用的旋转动画 */
@keyframes refreshSpin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 清除按钮 */
.clear-btn {
    position: absolute;
    right: 50px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 2px;
    border-radius: 3px;
    transition: all 0.2s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
}

.clear-btn:hover {
    color: #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
}

/* 下拉箭头 */
.dropdown-arrow {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    pointer-events: none;
    transition: all 0.2s ease-in-out;
}

.search-dropdown-container.open .dropdown-arrow {
    transform: translateY(-50%) rotate(180deg);
    color: #3b82f6;
}

/* 下拉选项列表 */
.search-dropdown-list {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff;
    border: 1px solid #dee2e6;
    border-top: none;
    border-radius: 0 0 0.375rem 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    max-height: 300px;
    overflow-y: auto;
    z-index: 1050;
    display: none;
    animation: slideDown 0.2s ease-out;
}

.search-dropdown-container.open .search-dropdown-list {
    display: block;
}

/* 下拉动画 */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 发布企业下拉框专用样式 - 使用ID选择器避免与其他dropdown组件样式冲突 */
#purchaser-dropdown-list .dropdown-item {
    padding: 0.5rem 0.75rem;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
    transition: all 0.15s ease-in-out;
    display: flex;
    align-items: center;
    font-size: 0.875rem;
}

#purchaser-dropdown-list .dropdown-item:last-child {
    border-bottom: none;
}

#purchaser-dropdown-list .dropdown-item:hover {
    background-color: #f8f9fa;
}

#purchaser-dropdown-list .dropdown-item.active {
    background-color: #e3f2fd;
    color: #1976d2;
}

#purchaser-dropdown-list .dropdown-item.selected {
    background-color: #3b82f6;
    color: #fff;
}

#purchaser-dropdown-list .dropdown-item.no-results {
    cursor: default;
    color: #6c757d;
    font-style: italic;
}

#purchaser-dropdown-list .dropdown-item.loading-item {
    cursor: default;
    color: #6c757d;
}


/* 高亮匹配文本 */
.highlight {
    background-color: #fff3cd;
    color: #856404;
    font-weight: 600;
    padding: 0 2px;
    border-radius: 2px;
}

/* 操作按钮样式已移动到 custom.css 的公共按钮样式 */

/* 移动端搜索下拉框优化 */
.search-dropdown-list {
    max-height: 250px;
}

#purchaser-dropdown-list .dropdown-item {
    padding: 0.75rem;
    font-size: 1rem;
    min-height: 44px;
}

.clear-btn {
    padding: 8px;
    right: 55px;
}

.refresh-btn {
    right: 35px;
    width: 32px;
    height: 32px;
    padding: 6px;
}

/* ========== 搜索下拉框响应式和可访问性优化 ========== */

/* 发布企业下拉框键盘导航支持 */
#purchaser-dropdown-list .dropdown-item:focus {
    outline: 2px solid #3b82f6;
    outline-offset: -2px;
    background-color: #e3f2fd;
}


/* 滚动条样式优化 */
.search-dropdown-list::-webkit-scrollbar {
    width: 6px;
}

.search-dropdown-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.search-dropdown-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.search-dropdown-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 加载状态优化 */
.search-dropdown-container.loading .search-input {
    background-repeat: no-repeat;
    background-position: right 50px center;
    background-size: 16px 16px;
}

/* 错误状态 */
.search-dropdown-container.error .search-input {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* 成功状态 */
.search-dropdown-container.success .search-input {
    border-color: #198754;
    box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
}

/* 操作列样式已移动到 custom.css 的公共表格样式 */

/* 客户名称列样式优化 */
.table th:nth-child(7),
.table td:nth-child(7) {
    max-width: 90px;
    min-width: 70px;
}

/* 响应式优化 */
@media (max-width: 992px) {
    .table th:nth-child(7),
    .table td:nth-child(7) {
        max-width: 80px;
        min-width: 60px;
    }

    .table td:nth-child(7) .text-truncate {
        max-width: 80px !important;
    }
}

@media (max-width: 576px) {
    .table th:nth-child(7),
    .table td:nth-child(7) {
        max-width: 70px;
        min-width: 50px;
    }

    .table td:nth-child(7) .text-truncate {
        max-width: 70px !important;
    }
}

/* 日期时间范围选择器样式 */
.datetime-range-picker {
    position: relative;
}

.datetime-range-input {
    cursor: pointer;
    background-color: #fff;
}

.datetime-range-input:focus {
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.datetime-range-panel {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1050;
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    padding: 1rem;
    margin-top: 0.25rem;
    display: none;
}

.datetime-range-panel.show {
    display: block;
}

.datetime-range-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #dee2e6;
}

.datetime-range-content {
    display: flex;
    gap: 1rem;
}

.datetime-range-section {
    flex: 1;
}

.datetime-range-section h6 {
    margin-bottom: 0.5rem;
    color: #495057;
    font-size: 0.875rem;
    font-weight: 600;
}

.datetime-range-section .form-control {
    margin-bottom: 0.5rem;
}

.datetime-range-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
    padding-top: 0.5rem;
    border-top: 1px solid #dee2e6;
}

.datetime-range-shortcuts {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.datetime-range-shortcuts .btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.datetime-range-actions {
    display: flex;
    gap: 0.5rem;
}

/* 紧凑型查询表单样式 */
.query-card .card-body {
    padding: 1rem 1.5rem;
}

.compact-form .mb-2 {
    margin-bottom: 0.75rem !important;
}

.compact-form .form-text {
    font-size: 0.75rem;
    line-height: 1.2;
    margin-top: 0.25rem;
}

.compact-form .form-label {
    margin-bottom: 0.375rem;
    font-weight: 500;
}

.compact-form .form-control,
.compact-form .form-select {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* 分页控件样式 */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-top: 1px solid #dee2e6;
    background-color: #f8f9fa;
}

.pagination-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.875rem;
    color: #6c757d;
}

.pagination-size-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pagination-size-selector select {
    width: auto;
    min-width: 80px;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pagination-status {
    font-weight: 500;
    color: #495057;
}

/* 表格样式已移动到 custom.css 的公共表格样式 */

/* ========== 表格文本省略号统一样式 ========== */

/* 表格单元格文本省略号样式 - 单行显示+省略号 */
.table-cell-ellipsis {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

/* 比价单名称列专用省略号样式 */
.table-cell-ellipsis.sheet-name {
    max-width: 300px;
}

/* 客户名称列专用省略号样式 */
.table-cell-ellipsis.customer-name {
    max-width: 220px;
}

/* 客户单位列专用省略号样式 */
.table-cell-ellipsis.customer-unit {
    max-width: 220px;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .table-cell-ellipsis.sheet-name {
        max-width: 180px;
    }

    .table-cell-ellipsis.customer-name {
        max-width: 160px;
    }

    .table-cell-ellipsis.customer-unit {
        max-width: 160px;
    }
}

/* 响应式优化 */
@media (max-width: 768px) {
    .query-card .card-body {
        padding: 0.75rem 1rem;
    }

    .compact-form .mb-2 {
        margin-bottom: 0.5rem !important;
    }

    .datetime-range-content {
        flex-direction: column;
        gap: 0.5rem;
    }

    .datetime-range-shortcuts {
        justify-content: center;
    }

    .datetime-range-panel {
        left: -1rem;
        right: -1rem;
    }

    .pagination-container {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .pagination-info {
        justify-content: center;
        flex-wrap: wrap;
    }

    .pagination-controls {
        justify-content: center;
    }
}
