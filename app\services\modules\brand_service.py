"""
品牌库数据查询服务
"""

import aiohttp
import asyncio
from typing import Dict, Any, Optional, List
from app.core.logging import logger
from app.core.config import get_settings
from app.services.base_electron_service import BaseElectronService

settings = get_settings()


class BrandService(BaseElectronService):
    """品牌库数据查询服务类"""

    def __init__(self):
        super().__init__()
        # 品牌库查询
        self.query_api_url = "https://eshop.eavic.com/api/commodity/supplier/brand_management/list"
        # 品牌审核查询
        self.approval_api_url = "https://eshop.eavic.com/api/commodity/supplier/brand_management/approve_list"
        # 文件上传
        self.upload_api_url = "https://eshop.eavic.com/api/system/base_file/upload"
        # 新增品牌
        self.create_api_url = "https://eshop.eavic.com/api/commodity/supplier/brand_management/create"
    
    async def query_brand_list(
        self,
        access_token: str,
        client_ip: Optional[str] = None,
        code: Optional[str] = None,
        name: Optional[str] = None,
        status: Optional[str] = None,
        current: int = 1,
        limit: int = 20
    ) -> Dict[str, Any]:
        """
        查询品牌库列表

        Args:
            access_token: ELECTRON平台访问令牌
            code: 品牌编号（可选）
            name: 品牌名称（可选）
            status: 启用状态（可选）
            current: 当前页码
            limit: 每页数量

        Returns:
            包含查询结果的字典
        """
        try:
            logger.info(f"开始品牌库查询 - 品牌编号: {code}, 品牌名称: {name}, 状态: {status}")
            
            # 构建请求体
            request_body = {
                "code": code,
                "name": name,
                "status": None,  # 固定为null，按照API规范
                "current": current,
                "limit": limit
            }
            
            logger.debug(f"发送API请求到: {self.query_api_url}")
            logger.debug(f"请求体: {request_body}")

            # 使用基类的统一请求方法
            response_data = await self._post_request(
                url=self.query_api_url,
                access_token=access_token,
                client_ip=client_ip,
                json_data=request_body
            )

            # 检查API响应状态
            if response_data.get("code") != "200":
                error_msg = response_data.get("msg", "未知错误")
                logger.error(f"API返回错误: {error_msg}")
                return {
                    'success': False,
                    'message': f'查询失败: {error_msg}',
                    'data': []
                }
            
            # 提取数据部分
            data_section = response_data.get("data", {})
            brand_list = data_section.get("list", [])
            total_count = data_section.get("total", "0")
            
            logger.info(f"查询成功，共找到 {len(brand_list)} 条品牌记录，总计 {total_count} 条")
            
            # 处理品牌数据
            processed_brands = []
            for i, brand in enumerate(brand_list):
                logger.debug(f"处理第 {i+1} 条原始数据: {brand}")
                processed_brand = self._process_brand_item(brand)
                logger.debug(f"处理后的数据: {processed_brand}")
                processed_brands.append(processed_brand)
            
            return {
                'success': True,
                'message': f'查询成功，找到 {len(processed_brands)} 条记录',
                'data': processed_brands,
                'total': int(total_count) if total_count.isdigit() else len(processed_brands)
            }
            
        except asyncio.TimeoutError:
            logger.error("品牌库查询请求超时")
            return {
                'success': False,
                'message': '请求超时，请稍后重试',
                'data': []
            }
        except aiohttp.ClientError as e:
            logger.error(f"网络请求错误: {str(e)}")
            return {
                'success': False,
                'message': '网络连接失败，请检查网络设置',
                'data': []
            }
        except Exception as e:
            logger.error(f"品牌库查询异常: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': f'查询过程中发生错误: {str(e)}',
                'data': []
            }

    async def query_brand_approval_list(
        self,
        access_token: str,
        client_ip: Optional[str] = None,
        name: Optional[str] = None,
        approval_status: Optional[str] = None,
        current: int = 1,
        limit: int = 20
    ) -> Dict[str, Any]:
        """
        查询品牌审核列表

        Args:
            access_token: ELECTRON平台访问令牌
            name: 品牌名称（可选）
            approval_status: 审核状态（可选）
            current: 当前页码
            limit: 每页数量

        Returns:
            包含查询结果的字典
        """
        try:
            logger.info(f"开始品牌审核查询 - 品牌名称: {name}, 审核状态: {approval_status}")

            # 构建请求体
            request_body = {
                "name": name,
                "approvalStatus": approval_status,  # 使用传入的审核状态参数
                "supplierId": None,      # 固定为null
                "current": current,
                "limit": limit
            }

            logger.debug(f"发送审核API请求到: {self.approval_api_url}")
            logger.debug(f"请求体: {request_body}")

            # 使用基类的统一请求方法
            response_data = await self._post_request(
                url=self.approval_api_url,
                access_token=access_token,
                client_ip=client_ip,
                json_data=request_body
            )

            # 检查API响应状态
            if response_data.get("code") != "200":
                error_msg = response_data.get("msg", "未知错误")
                logger.error(f"审核API返回错误: {error_msg}")
                return {
                    'success': False,
                    'message': f'查询失败: {error_msg}',
                    'data': []
                }

            # 提取数据部分
            data_section = response_data.get("data", {})
            brand_list = data_section.get("list", [])
            total_count = data_section.get("total", "0")

            logger.info(f"审核查询成功，共找到 {len(brand_list)} 条品牌记录，总计 {total_count} 条")

            # 处理品牌审核数据
            processed_brands = []
            for i, brand in enumerate(brand_list):
                logger.debug(f"处理第 {i+1} 条审核数据: {brand}")
                processed_brand = self._process_brand_approval_item(brand)
                logger.debug(f"处理后的审核数据: {processed_brand}")
                processed_brands.append(processed_brand)

            return {
                'success': True,
                'message': f'查询成功，找到 {len(processed_brands)} 条记录',
                'data': processed_brands,
                'total': int(total_count) if total_count.isdigit() else len(processed_brands)
            }

        except asyncio.TimeoutError:
            logger.error("品牌审核查询请求超时")
            return {
                'success': False,
                'message': '请求超时，请稍后重试',
                'data': []
            }
        except aiohttp.ClientError as e:
            logger.error(f"审核查询网络请求错误: {str(e)}")
            return {
                'success': False,
                'message': '网络连接失败，请检查网络设置',
                'data': []
            }
        except Exception as e:
            logger.error(f"品牌审核查询异常: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': f'查询过程中发生错误: {str(e)}',
                'data': []
            }

    def _process_brand_item(self, brand_item: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理单个品牌数据项

        Args:
            brand_item: 原始品牌数据

        Returns:
            处理后的品牌数据
        """
        try:
            # 品牌类型映射
            brand_origin_map = {
                'DOMESTIC': '国产',
                'IMPORT': '进口'
            }

            # 状态映射
            status_map = {
                'ENABLE': '启用',
                'DISABLE': '禁用'
            }

            # 安全获取字段值，处理None值
            def safe_get(key: str, default: str = '') -> str:
                """安全获取字段值，将None转换为空字符串"""
                value = brand_item.get(key)
                return value if value is not None else default

            brand_origin = safe_get('brandOrigin')
            status = safe_get('status')

            # 构建处理后的数据项
            processed_item = {
                'id': safe_get('id'),
                'code': safe_get('code'),
                'name': safe_get('name'),
                'nameEn': safe_get('nameEn'),
                'logoUrl': safe_get('logoUrl'),
                'logoOwner': safe_get('logoOwner'),
                'brandOrigin': brand_origin,
                'brandOriginDisplay': brand_origin_map.get(brand_origin, brand_origin) if brand_origin else '',
                'status': status,
                'statusDisplay': status_map.get(status, status) if status else ''
            }

            # 记录调试信息
            logger.debug(f"品牌数据处理完成 - ID: {processed_item['id']}, "
                        f"名称: {processed_item['name']}, "
                        f"状态: {processed_item['status']} -> {processed_item['statusDisplay']}, "
                        f"品牌类型: {processed_item['brandOrigin']} -> {processed_item['brandOriginDisplay']}")

            return processed_item

        except Exception as e:
            logger.error(f"处理品牌数据项失败: {str(e)}", exc_info=True)
            logger.error(f"原始数据: {brand_item}")

            # 返回安全的基础数据结构，避免程序崩溃
            def safe_fallback_get(key: str) -> str:
                """异常情况下的安全获取方法"""
                try:
                    value = brand_item.get(key) if isinstance(brand_item, dict) else None
                    return str(value) if value is not None else ''
                except:
                    return ''

            return {
                'id': safe_fallback_get('id'),
                'code': safe_fallback_get('code'),
                'name': safe_fallback_get('name'),
                'nameEn': safe_fallback_get('nameEn'),
                'logoUrl': safe_fallback_get('logoUrl'),
                'logoOwner': safe_fallback_get('logoOwner'),
                'brandOrigin': safe_fallback_get('brandOrigin'),
                'brandOriginDisplay': safe_fallback_get('brandOrigin'),
                'status': safe_fallback_get('status'),
                'statusDisplay': safe_fallback_get('status')
            }

    def _process_brand_approval_item(self, brand_item: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理单个品牌审核数据项

        Args:
            brand_item: 原始品牌审核数据

        Returns:
            处理后的品牌审核数据
        """
        try:
            # 品牌类型映射（完整映射，包含IMPORT）
            brand_origin_map = {
                'DOMESTIC': '国产',
                'CINCINNATI': '进口',
                'IMPORT': '进口'  # 添加IMPORT映射
            }

            # 审核状态映射
            approval_status_map = {
                'APPROVE': '待审核',
                'REJECT': '驳回',
                'APPROVED': '审核通过'
            }

            # 安全获取字段值，处理None值
            def safe_get(key: str, default: str = '') -> str:
                """安全获取字段值，将None转换为空字符串"""
                value = brand_item.get(key)
                return value if value is not None else default

            brand_origin = safe_get('brandOrigin')
            approval_status = safe_get('approvalStatus')

            # 构建处理后的数据项
            processed_item = {
                'id': safe_get('id'),
                'code': safe_get('code'),
                'name': safe_get('name'),
                'nameEn': safe_get('nameEn'),
                'logoUrl': safe_get('logoUrl'),
                'logoOwner': safe_get('logoOwner'),
                'brandOrigin': brand_origin,
                'brandOriginDisplay': brand_origin_map.get(brand_origin, brand_origin) if brand_origin else '',
                'approvalStatus': approval_status,
                'approvalStatusDisplay': approval_status_map.get(approval_status, approval_status) if approval_status else ''
            }

            # 记录调试信息
            logger.debug(f"品牌审核数据处理完成 - ID: {processed_item['id']}, "
                        f"名称: {processed_item['name']}, "
                        f"审核状态: {processed_item['approvalStatus']} -> {processed_item['approvalStatusDisplay']}, "
                        f"品牌类型: {processed_item['brandOrigin']} -> {processed_item['brandOriginDisplay']}")

            return processed_item

        except Exception as e:
            logger.error(f"处理品牌审核数据项失败: {str(e)}", exc_info=True)
            logger.error(f"原始数据: {brand_item}")

            # 返回安全的基础数据结构，避免程序崩溃
            def safe_fallback_get(key: str) -> str:
                """异常情况下的安全获取方法"""
                try:
                    value = brand_item.get(key) if isinstance(brand_item, dict) else None
                    return str(value) if value is not None else ''
                except:
                    return ''

            return {
                'id': safe_fallback_get('id'),
                'code': safe_fallback_get('code'),
                'name': safe_fallback_get('name'),
                'nameEn': safe_fallback_get('nameEn'),
                'logoUrl': safe_fallback_get('logoUrl'),
                'logoOwner': safe_fallback_get('logoOwner'),
                'brandOrigin': safe_fallback_get('brandOrigin'),
                'brandOriginDisplay': safe_fallback_get('brandOrigin'),
                'approvalStatus': safe_fallback_get('approvalStatus'),
                'approvalStatusDisplay': safe_fallback_get('approvalStatus')
            }

    async def upload_brand_file(
        self,
        access_token: str,
        file_content: bytes,
        file_name: str,
        file_type: str,
        client_ip: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        上传品牌相关文件（LOGO或商标网截图）

        Args:
            access_token: ELECTRON平台访问令牌
            file_content: 文件二进制内容
            file_name: 文件名
            file_type: 文件MIME类型
            client_ip: 客户端IP地址

        Returns:
            包含上传结果的字典
        """
        try:
            logger.info(f"开始上传品牌文件 - 文件名: {file_name}, 类型: {file_type}, 大小: {len(file_content)} 字节")

            # 构建multipart/form-data数据
            form_data = aiohttp.FormData()
            form_data.add_field('fileGroup', 'COMMODITY')
            form_data.add_field('publiclyBucket', 'BUCKET_PUBLICLY')
            form_data.add_field('file', file_content, filename=file_name, content_type=file_type)

            logger.debug(f"发送文件上传请求到: {self.upload_api_url}")
            logger.debug(f"表单数据: fileGroup=COMMODITY, publiclyBucket=BUCKET_PUBLICLY, file={file_name}")

            # 使用基类的统一文件上传方法
            response_data = await self._upload_file_request(
                url=self.upload_api_url,
                access_token=access_token,
                form_data=form_data,
                client_ip=client_ip,
                timeout=60
            )

            # 检查API响应状态
            if response_data.get("code") != "200":
                error_msg = response_data.get("msg", "未知错误")
                logger.error(f"文件上传API返回错误: {error_msg}")

                # 检查是否为权限相关错误
                permission_keywords = ["没有权限", "权限不足", "未授权", "token失效", "登录失效", "会话过期", "无权限", "权限错误"]
                is_permission_error = any(keyword in error_msg for keyword in permission_keywords)

                return {
                    'success': False,
                    'message': f'上传失败: {error_msg}',
                    'permission_error': is_permission_error
                }

            # 提取上传结果数据
            data_section = response_data.get("data", [])
            if not data_section or len(data_section) == 0:
                logger.error("文件上传响应中没有数据")
                return {
                    'success': False,
                    'message': '上传响应数据异常'
                }

            # 获取第一个文件的上传结果
            file_result = data_section[0]
            file_id = file_result.get("id", "")
            public_url = file_result.get("publicUrl", "")
            file_size = file_result.get("fileSize", "")
            file_suffix = file_result.get("suffix", "")
            uploaded_file_name = file_result.get("fileName", file_name)

            logger.debug(f"文件上传成功 - ID: {file_id}, URL: {public_url}, 大小: {file_size} 字节")

            return {
                'success': True,
                'message': '文件上传成功',
                'file_id': file_id,
                'file_name': uploaded_file_name,
                'file_size': file_size,
                'file_suffix': file_suffix,
                'public_url': public_url
            }

        except asyncio.TimeoutError:
            logger.error("文件上传请求超时")
            return {
                'success': False,
                'message': '上传超时，请稍后重试'
            }
        except aiohttp.ClientError as e:
            logger.error(f"文件上传网络请求错误: {str(e)}")
            return {
                'success': False,
                'message': '网络连接失败，请检查网络设置'
            }
        except Exception as e:
            logger.error(f"文件上传异常: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': f'上传过程中发生错误: {str(e)}'
            }

    async def create_brand(
        self,
        access_token: str,
        brand_data: Dict[str, Any],
        client_ip: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        新增品牌

        Args:
            access_token: ELECTRON平台访问令牌
            brand_data: 品牌数据
            client_ip: 客户端IP地址

        Returns:
            包含创建结果的字典
        """
        try:
            logger.info(f"开始新增品牌 - 品牌名称: {brand_data.get('name', '')}")

            # 构建请求体
            request_body = {
                "id": None,
                "code": None,
                "name": brand_data.get('name', ''),
                "nameEn": brand_data.get('nameEn', ''),
                "logo": brand_data.get('logo', ''),
                "logoUrl": brand_data.get('logoUrl', ''),
                "logoOwner": brand_data.get('logoOwner', ''),
                "markUrl": brand_data.get('markUrl', ''),
                "tags": None,
                "interCategory": brand_data.get('interCategory', ''),
                "status": None,
                "brandOrigin": brand_data.get('brandOrigin', ''),
                "approvalStatus": None
            }

            logger.debug(f"发送新增品牌请求到: {self.create_api_url}")
            logger.debug(f"请求体: {request_body}")

            # 使用基类的统一请求方法
            response_data = await self._post_request(
                url=self.create_api_url,
                access_token=access_token,
                client_ip=client_ip,
                json_data=request_body
            )

            # 检查API响应状态
            if response_data.get("code") != "200":
                error_msg = response_data.get("msg", "未知错误")
                logger.error(f"新增品牌API返回错误: {error_msg}")

                # 检查是否为权限相关错误
                permission_keywords = ["没有权限", "权限不足", "未授权", "token失效", "登录失效", "会话过期", "无权限", "权限错误"]
                is_permission_error = any(keyword in error_msg for keyword in permission_keywords)

                return {
                    'success': False,
                    'message': f'新增失败: {error_msg}',
                    'permission_error': is_permission_error
                }

            # 提取创建结果数据
            data_section = response_data.get("data", {})
            biz_id = data_section.get("bizId", "")

            logger.info(f"品牌新增成功 - 业务ID: {biz_id}")

            return {
                'success': True,
                'message': '品牌新增成功',
                'data': {
                    'bizId': biz_id
                }
            }

        except Exception as e:
            logger.error(f"新增品牌异常: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': f'新增过程中发生错误: {str(e)}'
            }


# 创建全局服务实例
brand_service = BrandService()
