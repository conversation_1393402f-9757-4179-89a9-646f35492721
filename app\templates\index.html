{% extends "base.html" %}

{% block title %}{{ app_name }} - 首页{% endblock %}

{% block content %}
<div class="page-header d-print-none">
    <div class="container-fluid">
        <div class="row g-2 align-items-center">
            <div class="col">
                <h2 class="page-title">
                    数据采集控制台
                </h2>
                <div class="text-muted mt-1">
                    版本 {{ app_version }}
                </div>
            </div>
        </div>
    </div>
</div>

<div class="page-body">
    <div class="container-fluid">
        <!-- 登录提示 -->
        <div class="row" id="login-prompt">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <h3>欢迎使用新航发航空数据采集器</h3>
                        <p class="text-muted">请先登录到电子超市以开始使用数据采集功能</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#login-modal">
                            立即登录
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要功能区域 -->
        <div id="main-content" style="display: none;">
            <!-- 需求凭证查询模块 -->
            <div class="mb-5">
                <h4 class="mb-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M14 3v4a1 1 0 0 0 1 1h4"></path>
                        <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"></path>
                        <path d="M9 9l1 0"></path>
                        <path d="M9 13l6 0"></path>
                        <path d="M9 17l6 0"></path>
                    </svg>
                    需求凭证查询
                </h4>

                <!-- 查询输入卡片 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">查询条件</h5>
                        <div class="card-actions">
                            <button class="btn btn-sm btn-outline-secondary" onclick="clearQueryCondition('xqpz')">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M4 7l16 0"></path>
                                    <path d="M10 11l0 6"></path>
                                    <path d="M14 11l0 6"></path>
                                    <path d="M5 7l1 -4l4 0l1 4"></path>
                                    <path d="M9 7l6 0"></path>
                                </svg>
                                清空条件
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <form id="xqpz-form">
                            <div class="mb-3">
                                <textarea class="form-control" name="query_text" rows="5"
                                    placeholder="请输入需求凭证查询条件...&#10;支持多行输入，可以输入多个查询参数"></textarea>
                            </div>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"></path>
                                        <path d="M21 21l-6 -6"></path>
                                    </svg>
                                    开始查询
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 数据结果卡片 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">查询结果</h5>
                        <div class="card-actions">
                            <span class="badge bg-secondary" id="xqpz-count">0 条记录</span>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div id="xqpz-results"></div>
                    </div>
                </div>
            </div>

            <!-- 需求单查询模块 -->
            <div class="mb-5">
                <h4 class="mb-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2"></path>
                        <path d="M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z"></path>
                        <path d="M9 12l2 2l4 -4"></path>
                    </svg>
                    需求单查询
                </h4>

                <!-- 查询输入卡片 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">查询条件</h5>
                        <div class="card-actions">
                            <button class="btn btn-sm btn-outline-secondary" onclick="clearQueryCondition('xqd')">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M4 7l16 0"></path>
                                    <path d="M10 11l0 6"></path>
                                    <path d="M14 11l0 6"></path>
                                    <path d="M5 7l1 -4l4 0l1 4"></path>
                                    <path d="M9 7l6 0"></path>
                                </svg>
                                清空条件
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <form id="xqd-form">
                            <div class="mb-3">
                                <textarea class="form-control" name="query_text" rows="5"
                                    placeholder="请输入需求单查询条件...&#10;支持多行输入，可以输入多个查询参数"></textarea>
                            </div>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"></path>
                                        <path d="M21 21l-6 -6"></path>
                                    </svg>
                                    开始查询
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 数据结果卡片 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">查询结果</h5>
                        <div class="card-actions">
                            <span class="badge bg-secondary" id="xqd-count">0 条记录</span>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div id="xqd-results"></div>
                    </div>
                </div>
            </div>

            <!-- 商品状态查询模块 -->
            <div class="mb-5">
                <h4 class="mb-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M7 4v16l13 0v-16"></path>
                        <path d="M7 4l4 0l0 4"></path>
                        <path d="M11 4l4 0"></path>
                        <path d="M15 4l0 4l-4 0"></path>
                        <path d="M7 8l13 0"></path>
                        <path d="M7 12l13 0"></path>
                        <path d="M7 16l13 0"></path>
                    </svg>
                    商品状态查询
                </h4>

                <!-- 查询输入卡片 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">查询条件</h5>
                        <div class="card-actions">
                            <button class="btn btn-sm btn-outline-secondary" onclick="clearQueryCondition('product')">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M4 7l16 0"></path>
                                    <path d="M10 11l0 6"></path>
                                    <path d="M14 11l0 6"></path>
                                    <path d="M5 7l1 -4l4 0l1 4"></path>
                                    <path d="M9 7l6 0"></path>
                                </svg>
                                清空条件
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <form id="product-form">
                            <div class="mb-3">
                                <textarea class="form-control" name="query_text" rows="5"
                                    placeholder="请输入商品状态查询条件...&#10;支持多行输入，可以输入多个查询参数"></textarea>
                            </div>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"></path>
                                        <path d="M21 21l-6 -6"></path>
                                    </svg>
                                    开始查询
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 数据结果卡片 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">查询结果</h5>
                        <div class="card-actions">
                            <span class="badge bg-secondary" id="product-count">0 条记录</span>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div id="product-results"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="/static/js/data-collection.js?ver={{ app_version }}"></script>
{% endblock %}
