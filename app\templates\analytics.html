{% extends "base.html" %}

{% block title %}数据分析 - 航发数据采集器{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="/static/css/modules/analytics.css">
{% endblock %}

{% block content %}
<div class="page-wrapper">
    <!-- Page header -->
    <div class="page-header d-print-none">
        <div class="container-xl">
            <div class="row g-2 align-items-center">
                <div class="col">
                    <div class="page-pretitle">
                        数据分析
                    </div>
                    <h2 class="page-title">
                        系统统计分析
                    </h2>
                </div>
                <div class="col-auto ms-auto d-print-none">
                    <div class="btn-list">
                        <button type="button" class="btn btn-primary" id="refreshDataBtn">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"/>
                                <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"/>
                            </svg>
                            刷新数据
                        </button>
                        <button type="button" class="btn btn-outline-primary" id="exportDataBtn">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                <path d="M14 3v4a1 1 0 0 0 1 1h4"/>
                                <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"/>
                                <path d="M9 9l1 0"/>
                                <path d="M9 13l6 0"/>
                                <path d="M9 17l6 0"/>
                            </svg>
                            导出报告
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Page body -->
    <div class="page-body">
        <div class="container-xl">
            <!-- 系统健康状态 -->
            <div class="row row-deck row-cards mb-3">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">系统健康状态</h3>
                            <div class="card-actions">
                                <span class="badge bg-success" id="systemHealthBadge">优秀</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="h1 mb-0" id="healthScore">100</div>
                                        <div class="text-muted">健康评分</div>
                                    </div>
                                </div>
                                <div class="col-md-9">
                                    <div id="healthIssues" class="text-muted">
                                        系统运行正常，无发现问题
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 统计概览 -->
            <div class="row row-deck row-cards mb-3">
                <div class="col-sm-6 col-lg-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="subheader">24小时查询总数</div>
                            </div>
                            <div class="h1 mb-3" id="totalQueries24h">0</div>
                            <div class="d-flex mb-2">
                                <div class="text-muted">成功率</div>
                                <div class="ms-auto">
                                    <span class="text-green d-inline-flex align-items-center lh-1" id="successRate24h">
                                        100%
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6 col-lg-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="subheader">本周查询总数</div>
                            </div>
                            <div class="h1 mb-3" id="totalQueriesWeek">0</div>
                            <div class="d-flex mb-2">
                                <div class="text-muted">较上周</div>
                                <div class="ms-auto">
                                    <span class="text-green d-inline-flex align-items-center lh-1" id="weeklyChange">
                                        +0%
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6 col-lg-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="subheader">平均响应时间</div>
                            </div>
                            <div class="h1 mb-3" id="avgResponseTime">0ms</div>
                            <div class="d-flex mb-2">
                                <div class="text-muted">峰值时段</div>
                                <div class="ms-auto">
                                    <span class="text-muted" id="peakHour">--:--</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6 col-lg-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="subheader">活跃模块数</div>
                            </div>
                            <div class="h1 mb-3" id="activeModules">5</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 趋势图表和模块统计 -->
            <div class="row row-deck row-cards">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">24小时查询趋势</h3>
                            <div class="card-actions">
                                <div class="dropdown">
                                    <a href="#" class="btn-action dropdown-toggle" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <circle cx="12" cy="12" r="1"/>
                                            <circle cx="12" cy="19" r="1"/>
                                            <circle cx="12" cy="5" r="1"/>
                                        </svg>
                                    </a>
                                    <div class="dropdown-menu dropdown-menu-end">
                                        <a class="dropdown-item" href="#" data-hours="24">24小时</a>
                                        <a class="dropdown-item" href="#" data-hours="48">48小时</a>
                                        <a class="dropdown-item" href="#" data-hours="72">72小时</a>
                                        <a class="dropdown-item" href="#" data-hours="168">7天</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="chart-container" style="height: 300px;">
                                <canvas id="trendsChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">模块调用统计</h3>
                        </div>
                        <div class="card-body">
                            <div id="moduleStatsContainer">
                                <!-- 模块统计将通过JavaScript动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 详细数据表格 -->
            <div class="row row-deck row-cards mt-3">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">详细统计数据</h3>
                            <div class="card-actions">
                                <div class="input-group input-group-flat">
                                    <input type="text" class="form-control" placeholder="搜索模块..." id="searchInput">
                                    <span class="input-group-text">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <circle cx="10" cy="10" r="7"/>
                                            <path d="m21 21-6-6"/>
                                        </svg>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-vcenter card-table" id="detailsTable">
                                <thead>
                                    <tr>
                                        <th>模块名称</th>
                                        <th>本周调用</th>
                                        <th>上周调用</th>
                                        <th>变化</th>
                                        <th>成功率</th>
                                        <th>响应时间</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="detailsTableBody">
                                    <!-- 数据将通过JavaScript动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 最后更新时间 -->
            <div class="row mt-3">
                <div class="col-12">
                    <div class="text-muted text-center">
                        最后更新时间: <span id="lastUpdateTime">--</span>
                        <span class="mx-2">|</span>
                        自动刷新: 开
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 模块详情模态框 -->
<div class="modal modal-blur fade" id="moduleDetailModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-full-width modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="moduleDetailTitle">模块详细信息</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- 模块汇总统计 -->
                <div class="row row-cards mb-3">
                    <div class="col-sm-6 col-lg-3">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="subheader">总请求数</div>
                                </div>
                                <div class="h1 mb-3" id="detailTotalRequests">0</div>
                                <div class="d-flex mb-2">
                                    <div class="text-muted">成功请求</div>
                                    <div class="ms-auto">
                                        <span class="text-green" id="detailSuccessRequests">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6 col-lg-3">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="subheader">成功率</div>
                                </div>
                                <div class="h1 mb-3" id="detailSuccessRate">100%</div>
                                <div class="d-flex mb-2">
                                    <div class="text-muted">失败请求</div>
                                    <div class="ms-auto">
                                        <span class="text-red" id="detailFailedRequests">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6 col-lg-3">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="subheader">平均响应时间</div>
                                </div>
                                <div class="h1 mb-3" id="detailAvgResponseTime">0ms</div>
                                <div class="d-flex mb-2">
                                    <div class="text-muted">最大响应时间</div>
                                    <div class="ms-auto">
                                        <span class="text-muted" id="detailMaxResponseTime">0ms</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6 col-lg-3">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="subheader">独立用户数</div>
                                </div>
                                <div class="h1 mb-3" id="detailUniqueUsers">0</div>
                                <div class="d-flex mb-2">
                                    <div class="text-muted">独立IP数</div>
                                    <div class="ms-auto">
                                        <span class="text-muted" id="detailUniqueIps">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 筛选和搜索 -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <label class="form-label">开始日期</label>
                        <input type="date" class="form-control" id="detailStartDate">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">结束日期</label>
                        <input type="date" class="form-control" id="detailEndDate">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">状态筛选</label>
                        <select class="form-select" id="detailStatusFilter">
                            <option value="all">全部</option>
                            <option value="success">成功</option>
                            <option value="failed">失败</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">搜索</label>
                        <input type="text" class="form-control" id="detailSearchInput" placeholder="搜索用户、错误信息...">
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <button type="button" class="btn btn-primary d-block" id="detailSearchBtn">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                <circle cx="10" cy="10" r="7"/>
                                <path d="m21 21-6-6"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- 详细日志表格 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">详细日志记录</h3>
                        <div class="card-actions">
                            <button type="button" class="btn btn-outline-primary btn-sm" id="exportDetailBtn">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                    <path d="M14 3v4a1 1 0 0 0 1 1h4"/>
                                    <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"/>
                                    <path d="M9 9l1 0"/>
                                    <path d="M9 13l6 0"/>
                                    <path d="M9 17l6 0"/>
                                </svg>
                                导出
                            </button>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-vcenter card-table" id="detailLogsTable">
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>用户</th>
                                    <th>状态</th>
                                    <th>响应时间</th>
                                    <th>查询内容</th>
                                    <th>错误信息</th>
                                </tr>
                            </thead>
                            <tbody id="detailLogsTableBody">
                                <!-- 数据将通过JavaScript动态生成 -->
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <div class="card-footer d-flex align-items-center">
                        <p class="m-0 text-muted">
                            显示 <span id="detailShowingStart">1</span> 到 <span id="detailShowingEnd">50</span> 条，
                            共 <span id="detailTotalCount">0</span> 条记录
                        </p>
                        <ul class="pagination m-0 ms-auto" id="detailPagination">
                            <!-- 分页按钮将通过JavaScript动态生成 -->
                        </ul>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="/static/js/analytics.js"></script>
{% endblock %}
