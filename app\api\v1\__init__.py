"""
API v1 路由模块
"""
from fastapi import APIRouter

from app.api.v1 import auth, analytics, changelog
from app.api.v1.modules import product, xqpz, xqd, brand
from app.api.v1.scraper import colipu, officemate, comix, xfs, lxwl, lxwl_new, xhgj

# 创建API路由器
api_router = APIRouter()

# 包含各个模块的路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
api_router.include_router(product.router, prefix="/data", tags=["商品状态查询"])
api_router.include_router(xqpz.router, prefix="/data", tags=["需求凭证查询"])
api_router.include_router(xqd.router, prefix="/data", tags=["需求单查询"])
api_router.include_router(brand.router, prefix="/data", tags=["品牌库采集"])
api_router.include_router(analytics.router, prefix="/analytics", tags=["分析统计"])
api_router.include_router(changelog.router, prefix="/system", tags=["系统信息"])

# 友商数据采集模块路由
api_router.include_router(colipu.router, prefix="/scraper", tags=["友商数据采集-晨光"])
api_router.include_router(officemate.router, prefix="/scraper", tags=["友商数据采集-欧菲斯"])
api_router.include_router(comix.router, prefix="/scraper", tags=["友商数据采集-齐心"])
api_router.include_router(xfs.router, prefix="/scraper", tags=["友商数据采集-鑫方盛"])
api_router.include_router(lxwl.router, prefix="/scraper", tags=["友商数据采集-领先未来"])
api_router.include_router(lxwl_new.router, prefix="/scraper", tags=["友商数据采集-领先未来(新)"])
api_router.include_router(xhgj.router, prefix="/scraper", tags=["友商数据采集-咸亨"])
