/**
 * 新航发航空数据采集器 - 自定义样式
 * 基于Tabler.io框架的扩展样式
 */

/* 全局样式 */
body {
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 600;
    color: #1e293b !important;
}

.navbar-brand a {
    color: inherit !important;
    transition: all 0.2s ease;
}

.navbar-brand a:hover {
    color: #3b82f6 !important;
    transform: translateY(-1px);
}

.navbar-brand .icon {
    color: #3b82f6;
    transition: all 0.2s ease;
}

.navbar-brand a:hover .icon {
    color: #2563eb;
    transform: scale(1.1);
}

/* 卡片样式增强 */
.card {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    border: 1px solid #e2e8f0;
}

.card-header {
    background-color: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

/* 表单样式 */
.form-control:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
}

textarea.form-control {
    resize: vertical;
    min-height: 120px;
}

/* 按钮样式 */
.btn-primary {
    background-color: #3b82f6;
    border-color: #3b82f6;
}

.btn-primary:hover {
    background-color: #2563eb;
    border-color: #2563eb;
}

.btn-outline-primary {
    color: #3b82f6;
    border-color: #3b82f6;
}

.btn-outline-primary:hover {
    background-color: #3b82f6;
    border-color: #3b82f6;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    font-weight: 600;
    color: #374151;
    background-color: #f9fafb;
    border-bottom: 2px solid #e5e7eb;
}

.table th.sortable:hover {
    background-color: #f3f4f6;
    cursor: pointer;
}

.table td {
    vertical-align: middle;
    border-bottom: 1px solid #e5e7eb;
}

.table tbody tr:hover {
    background-color: #f9fafb;
}

/* 表格行选择状态样式 */
.table-active {
    background-color: rgba(13, 110, 253, 0.1) !important;
    border-color: rgba(13, 110, 253, 0.2) !important;
}

.table-active td {
    background-color: rgba(13, 110, 253, 0.1) !important;
    border-color: rgba(13, 110, 253, 0.2) !important;
}

/* 确保选中行的hover效果 */
.table tbody tr.table-active:hover {
    background-color: rgba(13, 110, 253, 0.15) !important;
}

.table tbody tr.table-active:hover td {
    background-color: rgba(13, 110, 253, 0.15) !important;
}

/* 分页样式 */
.pagination .page-link {
    color: #374151;
    border-color: #d1d5db;
}

.pagination .page-item.active .page-link {
    background-color: #3b82f6;
    border-color: #3b82f6;
}

.pagination .page-link:hover {
    color: #1f2937;
    background-color: #f3f4f6;
    border-color: #d1d5db;
}

/* 徽章样式 */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
}

/* 空状态样式 */
.empty {
    text-align: center;
    padding: 3rem 1rem;
}

.empty-header {
    font-size: 1.5rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
}

.empty-title {
    font-size: 1.125rem;
    color: #6b7280;
    margin-bottom: 0.5rem;
}

.empty-subtitle {
    color: #9ca3af;
}

/* 加载状态样式 */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* 通知样式 */
.alert {
    border: none;
    border-radius: 0.5rem;
}

.alert-success {
    background-color: #dcfce7;
    color: #166534;
}

.alert-danger {
    background-color: #fee2e2;
    color: #991b1b;
}

.alert-warning {
    background-color: #fef3c7;
    color: #92400e;
}

.alert-info {
    background-color: #dbeafe;
    color: #1e40af;
}

/* 通知容器样式 - 右下角位置 */
#notification-container {
    display: flex;
    flex-direction: column-reverse;
    align-items: flex-end;
    gap: 0.5rem;
}

/* 通知弹入动画 - 从下方滑入 */
@keyframes slideInFromBottom {
    from {
        opacity: 0;
        transform: translateY(100%) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 通知弹出动画 - 向下滑出 */
@keyframes slideOutToBottom {
    from {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    to {
        opacity: 0;
        transform: translateY(100%) scale(0.95);
    }
}

/* 通知项样式 */
#notification-container .alert {
    animation: slideInFromBottom 0.3s ease-out;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(8px);
    margin-bottom: 0;
}

/* 通知移除动画 */
#notification-container .alert.removing {
    animation: slideOutToBottom 0.3s ease-in forwards;
}

/* 移动端通知优化 */
@media (max-width: 768px) {
    #notification-container {
        left: 10px;
        right: 10px;
        bottom: 10px;
        width: auto;
    }

    #notification-container .alert {
        min-width: auto;
        max-width: none;
        width: 100%;
        font-size: 0.875rem;
    }
}

@media (max-width: 576px) {
    #notification-container {
        left: 5px;
        right: 5px;
        bottom: 5px;
    }

    #notification-container .alert {
        font-size: 0.8125rem;
        padding: 0.75rem;
    }
}

/* 用户头像样式 */
.avatar {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}



/* 令牌剩余时间样式 */
#token-remaining {
    font-family: 'Courier New', monospace;
    font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .card-header .card-actions {
        margin-top: 0.5rem;
    }
    
    .d-flex.gap-2 {
        flex-direction: column;
    }
    
    .d-flex.gap-2 .btn {
        margin-bottom: 0.5rem;
    }
    
    .table-responsive {
        border: none;
    }
    
    .pagination {
        justify-content: center;
    }
}

/* 卡片操作按钮 */
.card-actions .btn {
    font-size: 0.875rem;
}

/* 表格排序图标 */
.sortable {
    position: relative;
}

.sort-asc,
.sort-desc {
    font-weight: bold;
    color: #3b82f6;
}

/* 登录模态框样式 */
.modal-content {
    border-radius: 0.75rem;
    border: none;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.modal-header {
    border-bottom: 1px solid #e5e7eb;
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

/* 切换账号按钮组样式 */
#switch-account-btn {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

#quick-switch-btn {
    font-size: 0.875rem;
    padding: 0.375rem 0.5rem;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: none;
}

/* 快速切换下拉菜单样式 */
#quick-switch-dropdown {
    min-width: 320px;
    overflow-y: auto;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 快速切换账号项样式 */
.quick-switch-item {
    padding: 0.75rem 1rem;
    transition: all 0.2s ease;
    border-radius: 0;
}

.quick-switch-item:hover {
    background-color: #f8fafc;
}

.quick-switch-item .avatar {
    width: 2.25rem;
    height: 2.25rem;
    font-size: 0.6rem;
    font-weight: 600;
    line-height: 1;
    letter-spacing: 0.02em;
}

.quick-switch-item .font-weight-medium {
    font-weight: 600;
    color: #1f2937;
    font-size: 0.875rem;
}

.quick-switch-item .text-muted.small {
    font-size: 0.75rem;
    color: #6b7280;
}

/* 快速切换加载状态 */
.quick-switch-item.loading {
    pointer-events: none;
    opacity: 0.7;
}

.quick-switch-item.loading .spinner-border-sm {
    width: 1rem;
    height: 1rem;
    border-width: 0.125rem;
}

/* 平台分组标题样式 */
#quick-switch-dropdown .dropdown-header {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: #374151;
    padding: 0.5rem 1rem 0.25rem;
    background-color: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
}

/* 快速切换全屏遮罩层 */
.quick-switch-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    backdrop-filter: blur(2px);
}

.quick-switch-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* 遮罩层内容容器 */
.quick-switch-overlay-content {
    background: white;
    border-radius: 0.75rem;
    padding: 2rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    text-align: center;
    min-width: 320px;
    max-width: 400px;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.quick-switch-overlay.show .quick-switch-overlay-content {
    transform: scale(1);
}

/* 遮罩层加载动画 */
.quick-switch-overlay .spinner-border {
    width: 3rem;
    height: 3rem;
    border-width: 0.3rem;
    color: #3b82f6;
    margin-bottom: 1.5rem;
}

/* 遮罩层标题 */
.quick-switch-overlay-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

/* 遮罩层描述 */
.quick-switch-overlay-description {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 1rem;
}

/* 遮罩层账号信息 */
.quick-switch-overlay-account {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background-color: #f8fafc;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
}

.quick-switch-overlay-account .avatar {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    flex-shrink: 0;
}

.quick-switch-overlay-account-info {
    text-align: left;
    flex: 1;
}

.quick-switch-overlay-account-name {
    font-weight: 600;
    color: #1f2937;
    font-size: 0.875rem;
    margin-bottom: 0.125rem;
}

.quick-switch-overlay-account-platform {
    font-size: 0.75rem;
    color: #6b7280;
}

/* 遮罩层取消按钮 */
.quick-switch-overlay-cancel {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    backdrop-filter: blur(4px);
    color: #6b7280;
}

.quick-switch-overlay-cancel:hover {
    background: rgba(255, 255, 255, 1);
    border-color: #cbd5e1;
    color: #374151;
    transform: scale(1.05);
}

/* 禁用页面滚动 */
body.quick-switch-loading {
    overflow: hidden;
}

/* 响应式快速切换 */
@media (max-width: 768px) {
    #quick-switch-dropdown {
        min-width: 280px;
        max-height: 300px;
    }

    .quick-switch-item {
        padding: 0.625rem 0.75rem;
    }

    .quick-switch-item .avatar {
        width: 2rem;
        height: 2rem;
        font-size: 0.55rem;
        line-height: 1;
        letter-spacing: 0.02em;
    }

    .quick-switch-item .font-weight-medium {
        font-size: 0.8125rem;
    }

    .quick-switch-item .text-muted.small {
        font-size: 0.6875rem;
    }

    #quick-switch-dropdown .dropdown-header {
        font-size: 0.6875rem;
        padding: 0.375rem 0.75rem 0.25rem;
    }

    /* 移动端隐藏按钮文字，只显示图标 */
    #switch-account-btn .d-none.d-sm-inline {
        display: none !important;
    }

    /* 移动端快速切换按钮组样式优化 */
    .navbar-nav .btn-group {
        flex-direction: row !important;
    }

    .navbar-nav .btn-group .btn {
        border-radius: 0 !important;
        margin-bottom: 0 !important;
        min-width: 44px;
        min-height: 44px;
    }

    .navbar-nav #switch-account-btn {
        border-top-left-radius: 0.375rem !important;
        border-bottom-left-radius: 0.375rem !important;
        border-top-right-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
    }

    .navbar-nav #quick-switch-btn {
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
        border-top-right-radius: 0.375rem !important;
        border-bottom-right-radius: 0.375rem !important;
        border-left: none !important;
    }

    /* 移动端遮罩层优化 */
    .quick-switch-overlay-content {
        min-width: 280px;
        max-width: 320px;
        padding: 1.5rem;
        margin: 1rem;
    }

    .quick-switch-overlay .spinner-border {
        width: 2.5rem;
        height: 2.5rem;
        border-width: 0.25rem;
        margin-bottom: 1rem;
    }

    .quick-switch-overlay-title {
        font-size: 1rem;
    }

    .quick-switch-overlay-description {
        font-size: 0.8125rem;
    }

    .quick-switch-overlay-account .avatar {
        width: 2rem;
        height: 2rem;
        font-size: 0.75rem;
    }

    .quick-switch-overlay-account-name {
        font-size: 0.8125rem;
    }

    .quick-switch-overlay-account-platform {
        font-size: 0.6875rem;
    }
}

@media (max-width: 576px) {
    #quick-switch-dropdown {
        min-width: calc(100vw - 40px);
        left: 20px !important;
        right: 20px !important;
        transform: none !important;
    }

    .quick-switch-item {
        padding: 0.5rem;
    }

    .quick-switch-item .avatar {
        width: 1.75rem;
        height: 1.75rem;
        font-size: 0.5rem;
        line-height: 1;
        letter-spacing: 0.02em;
    }

    /* 小屏幕设备按钮组样式保持一致 */
    .navbar-nav .btn-group {
        flex-direction: row !important;
    }

    .navbar-nav .btn-group .btn {
        border-radius: 0 !important;
        margin-bottom: 0 !important;
        min-width: 44px;
        min-height: 44px;
        font-size: 0.875rem;
        padding: 0.375rem 0.5rem;
    }

    .navbar-nav #switch-account-btn {
        border-top-left-radius: 0.375rem !important;
        border-bottom-left-radius: 0.375rem !important;
        border-top-right-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
        padding: 0.375rem 0.625rem;
    }

    .navbar-nav #quick-switch-btn {
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
        border-top-right-radius: 0.375rem !important;
        border-bottom-right-radius: 0.375rem !important;
        border-left: none !important;
        padding: 0.375rem 0.5rem;
    }
}

/* 数据表格容器 */
.card-table {
    margin-bottom: 0;
}

.card-footer {
    background-color: #f8fafc;
    border-top: 1px solid #e2e8f0;
}

/* 查询条件文本框 */
textarea[name="query_text"] {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.active {
    background-color: #10b981;
}

.status-indicator.pending {
    background-color: #f59e0b;
}

.status-indicator.failed {
    background-color: #ef4444;
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

/* 页面标题样式 */
.page-title {
    color: #1f2937;
    font-weight: 600;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* 垂直布局优化 */
.module-section {
    margin-bottom: 3rem;
}

.module-title {
    color: #1f2937;
    font-weight: 600;
    border-bottom: 2px solid #e5e7eb;
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
}

/* 查询输入卡片样式 */
.query-card {
    border-left: 4px solid #3b82f6;
}

.query-card .card-header {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

/* 结果卡片样式 */
.results-card {
    border-left: 4px solid #10b981;
}

.results-card .card-header {
    background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
}

/* 账号管理相关样式 */
.account-list {
    max-height: 300px;
    overflow-y: auto;
}

.account-list .card {
    transition: all 0.2s ease;
}

.account-list .card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.empty-state {
    color: #6b7280;
}

.empty-icon {
    opacity: 0.5;
}

/* 登录模态框增强 */
.modal-dialog {
    max-width: 500px;
}

.hr-text {
    position: relative;
    text-align: center;
    margin: 1.5rem 0;
    color: #6b7280;
    font-size: 0.875rem;
}

.hr-text::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e5e7eb;
    z-index: 1;
}

.hr-text::after {
    content: attr(data-content);
    position: relative;
    background: white;
    padding: 0 1rem;
    z-index: 2;
}

/* 密码输入框增强 */
.input-group .btn {
    border-left: none;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

/* 默认状态下统一边框颜色 */
.input-group .form-control {
    border-right: none;
}

.input-group .btn-outline-secondary {
    border-color: #dee2e6;
}


.input-group .form-control:focus + .btn {
    border-color: #3b82f6;
}

/* 记录数量徽章 */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.badge.bg-primary {
    background-color: #3b82f6 !important;
}

.badge.bg-secondary {
    background-color: #6b7280 !important;
}

.badge.bg-danger {
    background-color: #ef4444 !important;
}

/* 模块图标样式 */
.icon-tabler {
    color: #3b82f6;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .module-section {
        margin-bottom: 2rem;
    }

    .modal-dialog {
        margin: 1rem;
        max-width: none;
    }

    .account-list {
        max-height: 200px;
    }

    .card-actions {
        margin-top: 0.5rem;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeIn 0.3s ease-out;
}

/* 表格容器优化 */
.card-body.p-0 {
    overflow: hidden;
}

.table-responsive {
    border-radius: 0 0 0.5rem 0.5rem;
}

/* 下拉菜单图标 */
.dropdown-item-icon {
    width: 1rem;
    height: 1rem;
    margin-right: 0.5rem;
}

/* 面包屑导航 */
.breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
    font-size: 0.875rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: #6b7280;
}

.breadcrumb-item a {
    color: #6b7280;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: #3b82f6;
}

.breadcrumb-item.active {
    color: #1f2937;
}

/* 模块卡片链接 */
.card-link {
    text-decoration: none;
    color: inherit;
    transition: all 0.2s ease;
}

.card-link:hover {
    color: inherit;
    text-decoration: none;
}

.card-link-pop:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
}

.card-icon {
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 平台选择按钮组 */
.btn-group .btn-check:checked + .btn {
    background-color: #3b82f6;
    border-color: #3b82f6;
    color: white;
}

.btn-group .btn-outline-primary {
    border-color: #d1d5db;
    color: #6b7280;
}

.btn-group .btn-outline-primary:hover {
    background-color: #f3f4f6;
    border-color: #3b82f6;
    color: #3b82f6;
}

/* 查询类型按钮组 */
.btn-group .btn-check:checked + .btn-outline-warning {
    background-color: #f59e0b;
    border-color: #f59e0b;
    color: white;
}

.btn-group .btn-outline-warning {
    border-color: #d1d5db;
    color: #6b7280;
}

.btn-group .btn-outline-warning:hover {
    background-color: #fef3c7;
    border-color: #f59e0b;
    color: #f59e0b;
}

/* 审核状态特殊样式 */
.badge.bg-danger {
    background-color: #dc2626 !important;
}

.badge.bg-warning {
    background-color: #f59e0b !important;
    color: white !important;
}

.badge.bg-success {
    background-color: #059669 !important;
}

/* 查询和结果卡片特殊样式 */
.query-card {
    border-left: 4px solid #3b82f6;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.results-card {
    border-left: 4px solid #10b981;
    background: linear-gradient(135deg, #ffffff 0%, #f0fdf4 100%);
}

.query-card .card-header {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    border-bottom: 1px solid #bfdbfe;
}

.results-card .card-header {
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    border-bottom: 1px solid #a7f3d0;
}

/* 导航菜单活跃状态 */
.dropdown-item.active {
    background-color: #3b82f6;
    color: white;
}

.dropdown-item.active .dropdown-item-icon {
    color: white;
}

/* 统计卡片 */
.subheader {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: #6b7280;
}

/* 页面标题图标 */
.page-title .icon-tabler {
    vertical-align: middle;
}

/* 仪表板图表样式 */
#query-trend-chart {
    position: relative;
    width: 100%;
    min-height: 240px;
    height: 100%;
}

#queryTrendCanvas {
    width: 100% !important;
    height: 100% !important;
}

/* 仪表板统计卡片样式 */
.dashboard-stats-card {
    transition: all 0.2s ease;
    min-height: 400px;
}

.dashboard-stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dashboard-stats-card .card-body {
    padding: 1.5rem;
}

.dashboard-stats-card .card-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.dashboard-stats-card .card-subtitle {
    color: #6b7280;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* 模块统计列表样式 */
#module-stats-list {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
}

.module-stat-item {
    padding: 1rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    flex: 1;
    align-items: center !important;
}

.module-stat-item:last-child {
    border-bottom: none;
}

.module-stat-item .font-weight-medium {
    font-weight: 600;
    color: #1f2937;
    font-size: 0.9rem;
}

.module-stat-item .text-muted.small {
    font-size: 0.8rem;
    color: #6b7280;
    margin-top: 0.25rem;
}

.module-stat-item .avatar {
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 趋势指示器样式 */
.text-success.small::before {
    content: "↗ ";
    font-weight: bold;
}

.text-danger.small::before {
    content: "↘ ";
    font-weight: bold;
}

/* 图表底部统计样式 */
.dashboard-stats-card .row.mt-auto {
    margin-top: auto !important;
    padding-top: 1rem;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.dashboard-stats-card .row.mt-auto .h4 {
    font-size: 1.25rem;
    font-weight: 600;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        border-radius: 0.375rem !important;
        margin-bottom: 0.5rem;
    }

    .card-link-pop:hover {
        transform: none;
    }

    .page-title {
        font-size: 1.5rem;
    }

    .breadcrumb {
        font-size: 0.8rem;
    }

    /* 仪表板移动端优化 */
    #query-trend-chart {
        height: 150px;
    }

    .dashboard-stats-card:hover {
        transform: none;
    }

    #module-stats-list .d-flex {
        padding: 0.5rem 0;
    }
}

/* 加载动画优化 */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.loading-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 表单增强 */
.form-text {
    font-size: 0.8rem;
    color: #6b7280;
    margin-top: 0.25rem;
}

/* 按钮组增强 */
.d-flex.gap-2 .btn {
    margin-right: 0.5rem;
}

.d-flex.gap-2 .btn:last-child {
    margin-right: 0;
}

/* 用户信息显示优化 */
.dropdown-menu {
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.dropdown-item-text .row {
    font-size: 0.875rem;
}

.dropdown-item-text .small {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.dropdown-item-text .fw-bold {
    font-weight: 600;
    color: #1f2937;
}

/* 平台标识avatar样式优化 - 确保中文字符正确显示 */
.avatar.avatar-sm {
    font-size: 0.65rem;
    line-height: 1;
    letter-spacing: 0.05em;
    min-width: 2rem;
    min-height: 2rem;
}

/* 用户详情显示 */
#user-details {
    font-size: 0.75rem;
    line-height: 1.2;
}

/* ELECTRON访问状态 */
#electron-access-status .badge {
    font-size: 0.7rem;
    font-weight: 500;
}

/* ELECTRON权限不足提示卡片 */
.card.border-warning {
    border-width: 2px;
}

.bg-warning-lt {
    background-color: rgba(255, 193, 7, 0.1) !important;
}

.text-warning {
    color: #f59e0b !important;
}

.avatar.bg-warning-lt {
    background-color: rgba(255, 193, 7, 0.1) !important;
    color: #f59e0b !important;
}

/* 警告提示样式增强 */
.alert-info {
    background-color: #eff6ff;
    border-color: #bfdbfe;
    color: #1e40af;
}

.alert-title {
    color: #1e40af;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* 用户头像容器 */
.avatar {
    transition: all 0.2s ease;
}

.avatar:hover {
    transform: scale(1.05);
}

/* 响应式用户信息 */
@media (max-width: 1200px) {
    #user-details {
        display: none !important;
    }

    .dropdown-menu {
        min-width: 250px !important;
    }
}

@media (max-width: 768px) {
    .dropdown-menu {
        min-width: 220px !important;
    }

    .dropdown-item-text .row {
        font-size: 0.8rem;
    }

    /* 移动端avatar样式优化 */
    .avatar.avatar-sm {
        font-size: 0.6rem;
        min-width: 1.8rem;
        min-height: 1.8rem;
    }
}

/* 状态指示器动画 */
@keyframes pulse-success {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

@keyframes pulse-danger {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

.badge.bg-success {
    animation: pulse-success 2s ease-in-out infinite;
}

.badge.bg-danger {
    animation: pulse-danger 2s ease-in-out infinite;
}

/* 权限提示卡片动画 */
@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

#electron-access-denied {
    animation: slideInDown 0.5s ease-out;
}

/* 数据筛选器样式 */
#filter-panel {
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

#filter-panel .card-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid #cbd5e0;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

#filter-panel .card-header:hover {
    background: linear-gradient(135deg, #f1f5f9 0%, #d1d5db 100%);
}

#filter-panel .card-body {
    background-color: #fafafa;
}

/* 筛选条件行样式 */
.filter-row {
    background-color: white;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    transition: all 0.2s ease;
}

.filter-row:hover {
    border-color: #3b82f6;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

/* 筛选器表单控件 */
.form-select-sm, .form-control-sm {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}

/* 筛选器按钮组 */
.filter-actions .btn-group .btn {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}

.filter-actions .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* 筛选器统计信息 */
#filter-stats {
    font-size: 0.875rem;
    font-weight: 500;
}

#filter-count {
    font-size: 0.75rem;
    font-weight: 600;
}

/* 筛选器折叠图标动画 */
#collapse-icon {
    transition: transform 0.2s ease;
}

.collapsed #collapse-icon {
    transform: rotate(180deg);
}

/* 筛选器空状态 */
.filter-empty-state {
    text-align: center;
    padding: 2rem;
    color: #6b7280;
}

.filter-empty-state .icon {
    width: 3rem;
    height: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* 筛选器加载状态 */
.filter-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.filter-loading .spinner-border {
    width: 1.5rem;
    height: 1.5rem;
}

/* 筛选器结果高亮 */
.filter-highlight {
    background-color: #fef3c7;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
}

/* 筛选器响应式设计 */
@media (max-width: 768px) {
    #filter-panel .card-header {
        padding: 0.75rem;
    }

    #filter-panel .card-body {
        padding: 0.75rem;
    }

    .filter-row {
        padding: 0.5rem;
    }

    .filter-actions .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .filter-actions .btn {
        margin-bottom: 0.25rem;
        border-radius: 0.375rem !important;
    }

    .filter-actions .btn:last-child {
        margin-bottom: 0;
    }

    #filter-stats {
        font-size: 0.8rem;
    }

    .form-select-sm, .form-control-sm {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }
}

@media (max-width: 576px) {
    .filter-row .row > .col-md-3,
    .filter-row .row > .col-md-2,
    .filter-row .row > .col-md-5 {
        margin-bottom: 0.5rem;
    }

    .filter-row .row > .col-md-2:last-child {
        margin-bottom: 0;
    }
}

/* 筛选器动画效果 */
@keyframes filterSlideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.filter-row {
    animation: filterSlideIn 0.3s ease-out;
}

/* 筛选器成功/错误状态 */
.filter-success {
    border-left: 4px solid #10b981;
    background-color: #ecfdf5;
}

.filter-error {
    border-left: 4px solid #ef4444;
    background-color: #fef2f2;
}

/* 筛选器工具提示 */
.filter-tooltip {
    position: relative;
}

.filter-tooltip:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #1f2937;
    color: white;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 1000;
}

/* 筛选器性能优化 */
.filter-virtualized {
    height: 400px;
    overflow-y: auto;
}

.filter-virtualized .table-responsive {
    height: 100%;
}

/* 分页控件样式 */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background-color: #f8fafc;
    border-top: 1px solid #e2e8f0;
    border-radius: 0 0 0.5rem 0.5rem;
}

.pagination-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.pagination-status {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

.pagination-size-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.pagination-size-selector .form-select {
    width: auto;
    min-width: 80px;
}

.pagination-controls {
    display: flex;
    align-items: center;
}

/* 响应式分页 */
@media (max-width: 768px) {
    .pagination-container {
        flex-direction: column;
        gap: 1rem;
        padding: 0.75rem;
    }

    .pagination-info {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .pagination-status {
        font-size: 0.8rem;
    }

    .pagination-size-selector {
        font-size: 0.8rem;
    }

    .pagination .page-link {
        padding: 0.375rem 0.5rem;
        font-size: 0.8rem;
    }
}

@media (max-width: 576px) {
    .pagination-container {
        padding: 0.5rem;
    }

    .pagination-info {
        gap: 0.25rem;
    }

    .pagination .page-item {
        margin: 0 1px;
    }

    .pagination .page-link {
        padding: 0.25rem 0.375rem;
        font-size: 0.75rem;
    }

    /* 隐藏部分页码在小屏幕上 */
    .pagination .page-item:not(.active):not(:first-child):not(:last-child):not(:nth-child(2)):not(:nth-last-child(2)) {
        display: none;
    }
}

/* 按钮加载状态样式 */
.btn .spinner-border-sm {
    width: 0.875rem;
    height: 0.875rem;
    border-width: 0.125rem;
}

.btn:disabled {
    opacity: 0.65;
    cursor: not-allowed;
}

/* 分页动画效果 */
.pagination-container {
    transition: all 0.3s ease;
}

.pagination .page-link {
    transition: all 0.2s ease;
}

.pagination .page-link:hover {
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    transform: none;
}

/* ===== 更新日志模态框样式 ===== */

/* 更新日志模态框整体样式 */
#changelog-modal .modal-dialog {
    max-width: 800px;
    height: 90vh;
    max-height: 700px;
    margin: 1.75rem auto;
}

/* 模态框内容容器 - 使用flex布局 */
.changelog-modal-content {
    border-radius: 0.75rem;
    border: none;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 固定头部样式 */
.changelog-modal-header {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e2e8f0;
    border-radius: 0.75rem 0.75rem 0 0;
    flex-shrink: 0;
    position: relative;
    z-index: 10;
    padding: 1.25rem 1.5rem;
}

.changelog-modal-header .modal-title {
    color: #1e293b;
    font-weight: 600;
    display: flex;
    align-items: center;
    margin-bottom: 0;
}

.changelog-modal-header .modal-title .icon {
    color: #3b82f6;
}

/* 关闭按钮固定样式 */
.changelog-close-btn {
    position: absolute;
    top: 1.25rem;
    right: 1.5rem;
    z-index: 20;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    backdrop-filter: blur(4px);
}

.changelog-close-btn:hover {
    background: rgba(255, 255, 255, 1);
    border-color: #cbd5e1;
    transform: scale(1.05);
}

.changelog-close-btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
}

/* 可滚动内容区域 */
.changelog-modal-body {
    flex: 1;
    overflow: hidden;
    padding: 0;
    position: relative;
}

.changelog-scroll-container {
    height: 100%;
    overflow-y: auto;
    padding: 1.5rem;
    scroll-behavior: smooth;
}

/* 固定底部样式 */
.changelog-modal-footer {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-top: 1px solid #e2e8f0;
    border-radius: 0 0 0.75rem 0.75rem;
    flex-shrink: 0;
    padding: 1rem 1.5rem;
}

/* 滚动条样式优化 */
.changelog-scroll-container::-webkit-scrollbar {
    width: 8px;
}

.changelog-scroll-container::-webkit-scrollbar-track {
    background: #f8fafc;
    border-radius: 4px;
    margin: 8px 0;
}

.changelog-scroll-container::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #cbd5e1, #94a3b8);
    border-radius: 4px;
    border: 1px solid #e2e8f0;
}

.changelog-scroll-container::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #94a3b8, #64748b);
}

/* 滚动区域边界指示器 */
.changelog-scroll-container::before {
    content: '';
    position: sticky;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, #e2e8f0, transparent);
    z-index: 5;
    margin-bottom: -1px;
}

.changelog-scroll-container::after {
    content: '';
    position: sticky;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, #e2e8f0, transparent);
    z-index: 5;
    margin-top: -1px;
}

/* 版本卡片样式 - 紧凑布局 */
#changelog-content .card {
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    margin-bottom: 0.75rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

#changelog-content .card-header {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e2e8f0;
    padding: 0.75rem 1rem;
}

#changelog-content .card-title {
    margin-bottom: 0;
    display: flex;
    align-items: center;
    font-size: 1.125rem;
    font-weight: 600;
    gap: 0.375rem;
}

#changelog-content .card-body {
    padding: 0.875rem 1rem;
}

/* 统一徽章基础样式 */
#changelog-content .badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    line-height: 1.2;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-height: 1.25rem;
    white-space: nowrap;
    vertical-align: baseline;
}

/* 版本号徽章样式 */
#changelog-content .badge.bg-primary {
    background-color: #3b82f6 !important;
    color: white;
}

#changelog-content .badge.bg-secondary {
    background-color: #6b7280 !important;
    color: white;
}

/* "最新"徽章样式 */
#changelog-content .badge.bg-success {
    background-color: #10b981 !important;
    color: white;
    margin-left: 0.375rem;
}

/* 更新条目列表样式 - 紧凑布局 */
#changelog-content .list-unstyled {
    margin-bottom: 0;
}

#changelog-content .list-unstyled li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f1f5f9;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    line-height: 1.4;
}

#changelog-content .list-unstyled li:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

/* 更新类型徽章统一样式 */
.changelog-type-badge {
    font-size: 0.6875rem;
    font-weight: 500;
    padding: 0.1875rem 0.4375rem;
    border-radius: 0.1875rem;
    line-height: 1.2;
    min-width: 2.75rem;
    height: 1.25rem;
    text-align: center;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    flex-shrink: 0;
    vertical-align: baseline;
}

/* 更新类型颜色方案 */
.changelog-type-badge.bg-success {
    background-color: #10b981 !important;
    color: white;
}

.changelog-type-badge.bg-warning {
    background-color: #f59e0b !important;
    color: white;
}

.changelog-type-badge.bg-info {
    background-color: #3b82f6 !important;
    color: white;
}

.changelog-type-badge.bg-danger {
    background-color: #ef4444 !important;
    color: white;
}

.changelog-type-badge.bg-cyan {
    background-color: #17a2b8 !important;
    color: white;
}

.changelog-type-badge.bg-secondary {
    background-color: #6b7280 !important;
    color: white;
}

/* 更新内容文本样式 */
#changelog-content .flex-grow-1 {
    color: #374151;
    line-height: 1.4;
    font-size: 0.875rem;
    flex: 1;
    align-self: center;
}

/* 加载状态样式 */
#changelog-loading {
    color: #6b7280;
}

#changelog-loading .spinner-border {
    width: 2rem;
    height: 2rem;
    border-width: 0.2rem;
}

/* 错误状态样式 */
#changelog-error .alert {
    border: none;
    border-radius: 0.5rem;
    background-color: #fee2e2;
    border-left: 4px solid #ef4444;
}

#changelog-error .alert-title {
    color: #991b1b;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

#changelog-error .text-muted {
    color: #dc2626 !important;
}

/* 加载和错误状态样式优化 */
#changelog-loading {
    color: #6b7280;
    padding: 3rem 1rem;
}

#changelog-loading .spinner-border {
    width: 2.5rem;
    height: 2.5rem;
    border-width: 0.25rem;
}

#changelog-error {
    padding: 2rem 1rem;
}

#changelog-error .alert {
    border: none;
    border-radius: 0.5rem;
    background-color: #fee2e2;
    border-left: 4px solid #ef4444;
    margin-bottom: 0;
}

#changelog-error .alert-title {
    color: #991b1b;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

#changelog-error .text-muted {
    color: #dc2626 !important;
}

/* 响应式优化 */
@media (max-width: 992px) {
    #changelog-modal .modal-dialog {
        max-width: 90vw;
        height: 85vh;
        margin: 2.5vh auto;
    }
}

@media (max-width: 768px) {
    #changelog-modal .modal-dialog {
        max-width: 95vw;
        height: 90vh;
        margin: 1rem;
    }

    .changelog-modal-header {
        padding: 1rem 1.25rem;
    }

    .changelog-close-btn {
        top: 1rem;
        right: 1.25rem;
        width: 28px;
        height: 28px;
    }

    .changelog-scroll-container {
        padding: 0.75rem;
    }

    .changelog-modal-footer {
        padding: 0.625rem 1rem;
    }

    #changelog-content .card {
        margin-bottom: 0.5rem;
    }

    #changelog-content .card-header {
        padding: 0.625rem 0.75rem;
    }

    #changelog-content .card-body {
        padding: 0.625rem 0.75rem;
    }

    #changelog-content .badge {
        font-size: 0.6875rem;
        padding: 0.1875rem 0.375rem;
        min-height: 1.125rem;
    }

    #changelog-content .list-unstyled li {
        padding: 0.375rem 0;
        gap: 0.375rem;
    }

    .changelog-type-badge {
        font-size: 0.625rem;
        padding: 0.125rem 0.375rem;
        min-width: 2.5rem;
        height: 1.125rem;
    }

    #changelog-loading {
        padding: 2rem 1rem;
    }

    #changelog-loading .spinner-border {
        width: 2rem;
        height: 2rem;
        border-width: 0.2rem;
    }
}

@media (max-width: 576px) {
    #changelog-modal .modal-dialog {
        height: 95vh;
        margin: 0.5rem;
    }

    .changelog-modal-header {
        padding: 0.75rem 1rem;
    }

    .changelog-close-btn {
        top: 0.75rem;
        right: 1rem;
        width: 24px;
        height: 24px;
    }

    .changelog-scroll-container {
        padding: 0.75rem;
    }

    .changelog-modal-footer {
        padding: 0.5rem 1rem;
    }

    #changelog-content .card {
        margin-bottom: 0.5rem;
    }

    #changelog-content .card-header {
        padding: 0.5rem 0.625rem;
    }

    #changelog-content .card-body {
        padding: 0.5rem 0.625rem;
    }

    #changelog-content .card-title {
        font-size: 1rem;
        gap: 0.25rem;
    }

    #changelog-content .list-unstyled li {
        padding: 0.25rem 0;
        gap: 0.25rem;
    }

    .changelog-type-badge {
        font-size: 0.5625rem;
        padding: 0.125rem 0.3125rem;
        min-width: 2.25rem;
        height: 1rem;
    }

    #changelog-content .badge {
        font-size: 0.5625rem;
        padding: 0.125rem 0.3125rem;
        min-height: 1rem;
    }

    #changelog-content .flex-grow-1 {
        font-size: 0.8125rem;
        line-height: 1.3;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .changelog-scroll-container::-webkit-scrollbar {
        width: 12px;
    }

    .changelog-scroll-container::-webkit-scrollbar-thumb {
        background: #94a3b8;
        border-radius: 6px;
    }

    /* 触摸设备上的徽章间距优化 */
    #changelog-content .list-unstyled li {
        gap: 0.5rem;
        padding: 0.375rem 0;
    }

    .changelog-type-badge {
        min-width: 2.5rem;
        height: 1.125rem;
    }
}

/* ========== 公共表格样式 ========== */

/* 表格文本选择和交互样式 */
.table td {
    user-select: text;
}

/* 表格行hover效果 */
.table tbody tr:hover {
    background-color: #f8f9fa;
}

.table tbody tr.table-active:hover {
    background-color: rgba(13, 110, 253, 0.15) !important;
}

/* 操作列样式优化 */
.table td:last-child {
    white-space: nowrap;
}

/* ========== 公共按钮样式 ========== */

/* 操作按钮列表样式 */
.btn-list .btn {
    min-width: 32px;
    min-height: 32px;
    padding: 0.375rem 0.5rem;
}

.btn-list .btn .icon {
    margin: 0;
}

/* 移动端触摸优化 */
@media (max-width: 768px) {
    .btn-list .btn {
        min-width: 44px;
        min-height: 44px;
    }
}

/* ========== 公共弹窗组件样式 ========== */

/* 通用弹窗容器 */
.popover-component,
.delist-reason-popover {
    position: fixed;
    z-index: 1060;
    max-width: 350px;
    min-width: 250px;
    opacity: 0;
    transform: scale(0.95);
    transition: opacity 0.2s ease, transform 0.2s ease;
    pointer-events: none;
}

.popover-component.show,
.delist-reason-popover.show {
    opacity: 1;
    transform: scale(1);
    pointer-events: auto;
}

/* 弹窗内容容器 */
.popover-component .popover-content,
.delist-reason-popover .popover-content {
    background: var(--tblr-bg-surface, #fff);
    border: 1px solid var(--tblr-border-color, #dee2e6);
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    overflow: hidden;
}

/* 弹窗头部 */
.popover-component .popover-header,
.delist-reason-popover .popover-header {
    background: var(--tblr-bg-surface-secondary, #f8f9fa);
    border-bottom: 1px solid var(--tblr-border-color, #dee2e6);
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.popover-component .popover-title,
.delist-reason-popover .popover-title {
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--tblr-body-color, #495057);
    margin: 0;
}

/* 弹窗关闭按钮 */
.popover-component .btn-close,
.delist-reason-popover .btn-close {
    width: 1.5rem;
    height: 1.5rem;
    padding: 0;
    background: transparent;
    border: none;
    border-radius: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    line-height: 1;
    color: var(--tblr-body-color, #495057);
    opacity: 0.6;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
    position: relative;
}

/* 添加关闭图标（×符号） */
.popover-component .btn-close::before,
.delist-reason-popover .btn-close::before {
    content: "×";
    font-weight: 700;
    font-size: 1.25rem;
    line-height: 1;
}

.popover-component .btn-close:hover,
.delist-reason-popover .btn-close:hover {
    opacity: 1;
    background: var(--tblr-bg-surface-secondary, rgba(0, 0, 0, 0.05));
    transform: scale(1.1);
}

.popover-component .btn-close:focus,
.delist-reason-popover .btn-close:focus {
    outline: none;
    opacity: 1;
    background: var(--tblr-bg-surface-secondary, rgba(0, 0, 0, 0.05));
    box-shadow: 0 0 0 0.2rem rgba(var(--tblr-primary-rgb, 59, 130, 246), 0.25);
}

.popover-component .btn-close:active,
.delist-reason-popover .btn-close:active {
    transform: scale(0.95);
}

/* 弹窗内容区域 */
.popover-component .popover-body,
.delist-reason-popover .popover-body {
    padding: 1rem;
    max-height: 200px;
    overflow-y: auto;
}

.popover-component .popover-body p,
.delist-reason-popover .popover-body p {
    font-size: 0.875rem;
    line-height: 1.5;
    color: var(--tblr-body-color, #495057);
    word-wrap: break-word;
    white-space: pre-wrap;
}

/* 响应式弹窗 */
@media (max-width: 575.98px) {
    .popover-component,
    .delist-reason-popover {
        max-width: calc(100vw - 40px);
        min-width: calc(100vw - 40px);
        left: 20px !important;
        right: 20px !important;
    }

    /* 移动端触摸优化 - 确保关闭按钮有足够的触摸区域 */
    .popover-component .btn-close,
    .delist-reason-popover .btn-close {
        width: 2.75rem;
        height: 2.75rem;
        min-width: 44px;
        min-height: 44px;
        font-size: 1.5rem;
    }

    .popover-component .btn-close::before,
    .delist-reason-popover .btn-close::before {
        font-size: 1.5rem;
    }
}


/* ========== 表格列宽调整样式 ========== */

/* 列调整手柄 */
.column-resize-handle {
    position: absolute;
    top: 0;
    right: -3px;
    width: 6px;
    height: 100%;
    cursor: col-resize;
    background: transparent;
    border-right: 2px solid transparent;
    transition: border-color 0.2s ease;
    z-index: 10;
}

.column-resize-handle:hover {
    border-right-color: var(--tblr-primary, #206bc4);
}

/* 调整中的状态 */
.column-resizing {
    user-select: none;
    cursor: col-resize !important;
}

.column-resizing * {
    cursor: col-resize !important;
}

/* 调整预览线 */
.column-resize-preview {
    position: fixed;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--tblr-primary, #206bc4);
    z-index: 9999;
    pointer-events: none;
    opacity: 0.8;
}

/* 表格列调整优化 */
.table th {
    position: relative;
}

/* 移动端禁用列调整 */
@media (max-width: 768px) {
    .column-resize-handle {
        display: none;
    }
}


/* ========== 公共幽灵按钮样式 ========== */

/* 幽灵次要按钮样式 */
.btn-ghost-secondary {
    color: #6c757d;
    background: transparent;
    border: 1px solid transparent;
    padding: 0.25rem 0.375rem;
    border-radius: 0.25rem;
    transition: all 0.15s ease-in-out;
}

.btn-ghost-secondary:hover {
    color: #495057;
    background: #f8f9fa;
    border-color: #dee2e6;
}

.btn-ghost-secondary:focus {
    box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.25);
}

/* ========== 版本更新通知模态框样式 ========== */

/* 版本更新模态框整体样式 */
#version-update-modal .modal-dialog {
    max-width: 600px;
}

#version-update-modal .modal-content {
    border-radius: 0.75rem;
    border: none;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    overflow: hidden;
}

/* 版本更新模态框头部样式 */
#version-update-modal .modal-header {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border-bottom: none;
    padding: 1.25rem 1.5rem;
}

#version-update-modal .modal-header .modal-title {
    color: white;
    font-weight: 600;
    display: flex;
    align-items: center;
    margin-bottom: 0;
}

#version-update-modal .modal-header .icon {
    color: white;
}

/* 版本更新模态框内容样式 */
#version-update-modal .modal-body {
    padding: 1.5rem;
    background-color: #f8fafc;
}

#version-update-modal .changelog-content {
    background-color: white;
    border-radius: 0.5rem;
    padding: 1rem;
    border: 1px solid #e2e8f0;
    max-height: 400px;
    overflow-y: auto;
}

/* 更新类型徽章样式 */
#version-update-modal .changelog-content .badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    font-weight: 500;
    min-width: 60px;
    text-align: center;
    flex-shrink: 0;
}

#version-update-modal .changelog-content li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f1f5f9;
}

#version-update-modal .changelog-content li:last-child {
    border-bottom: none;
}

#version-update-modal .changelog-content .d-flex {
    gap: 0.75rem;
}

#version-update-modal .changelog-content .flex-1 {
    flex: 1;
    line-height: 1.5;
    color: #374151;
}

/* 版本更新模态框底部样式 */
#version-update-modal .modal-footer {
    background-color: #f8fafc;
    border-top: 1px solid #e2e8f0;
    padding: 1rem 1.5rem;
    justify-content: center;
}

#version-update-modal .btn-close-update {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

#version-update-modal .btn-close-update:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

/* 暗色主题适配 */
.dark #version-update-modal .modal-body {
    background-color: #1e293b;
}

.dark #version-update-modal .changelog-content {
    background-color: #334155;
    border-color: #475569;
}

.dark #version-update-modal .changelog-content .flex-1 {
    color: #e2e8f0;
}

.dark #version-update-modal .changelog-content li {
    border-bottom-color: #475569;
}

.dark #version-update-modal .modal-footer {
    background-color: #1e293b;
    border-top-color: #475569;
}

/* 移动端适配 */
@media (max-width: 768px) {
    #version-update-modal .modal-dialog {
        max-width: 95%;
        margin: 1rem auto;
    }

    #version-update-modal .modal-header {
        padding: 1rem 1.25rem;
    }

    #version-update-modal .modal-body {
        padding: 1.25rem;
    }

    #version-update-modal .changelog-content {
        max-height: 300px;
        padding: 0.75rem;
    }

    #version-update-modal .changelog-content .badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
        min-width: 50px;
    }

    #version-update-modal .changelog-content .d-flex {
        gap: 0.5rem;
    }

    #version-update-modal .modal-footer {
        padding: 0.75rem 1.25rem;
    }

    #version-update-modal .btn-close-update {
        padding: 0.625rem 1.5rem;
        font-size: 0.875rem;
    }
}
