"""
需求单查询API路由
"""

from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
import time
from app.core.auth import get_current_user
from app.services.modules.xqd_service import xqd_service
from app.services.analytics_service import analytics_service
from app.core.logging import logger
import io


router = APIRouter()


class XqdQueryRequest(BaseModel):
    """需求单查询请求模型"""
    askSheetCode: Optional[str] = Field(None, description="比价单号")
    askSheetName: Optional[str] = Field(None, description="比价单名称")
    answerBeginTimeStart: Optional[str] = Field(None, description="报价开始时间起始")
    answerBeginTimeEnd: Optional[str] = Field(None, description="报价开始时间结束")
    answerEndTimeStart: Optional[str] = Field(None, description="报价截止时间起始")
    answerEndTimeEnd: Optional[str] = Field(None, description="报价截止时间结束")
    askSheetStatus: Optional[str] = Field(None, description="询价单状态")
    prodName: Optional[str] = Field(None, description="采购清单信息")
    purchaserName: Optional[str] = Field(None, description="采购方名称")


class XqdQueryResponse(BaseModel):
    """需求单查询响应模型"""
    success: bool = Field(..., description="查询是否成功")
    message: str = Field(..., description="响应消息")
    data: list = Field(default_factory=list, description="查询结果数据")
    total: Optional[int] = Field(None, description="总记录数")
    permission_error: Optional[bool] = Field(None, description="是否为权限相关错误")


class XqdDownloadRequest(BaseModel):
    """报价单下载请求模型"""
    askSheetCode: str = Field(..., description="比价单号")
    askSheetId: str = Field(..., description="比价单ID")


class XqdDetailRequest(BaseModel):
    """需求单详情请求模型"""
    askSheetCode: str = Field(..., description="比价单号")
    askSheetId: str = Field(..., description="比价单ID")


class XqdDetailResponse(BaseModel):
    """需求单详情响应模型"""
    success: bool = Field(..., description="查询是否成功")
    message: str = Field(..., description="响应消息")
    data: dict = Field(default_factory=dict, description="详情数据")
    permission_error: Optional[bool] = Field(None, description="是否为权限相关错误")


class XqdDetailListResponse(BaseModel):
    """需求单产品列表响应模型"""
    success: bool = Field(..., description="查询是否成功")
    message: str = Field(..., description="响应消息")
    data: list = Field(default_factory=list, description="产品列表数据")
    permission_error: Optional[bool] = Field(None, description="是否为权限相关错误")


class PurchaserListResponse(BaseModel):
    """采购方列表响应模型"""
    success: bool = Field(..., description="查询是否成功")
    message: str = Field(..., description="响应消息")
    data: list = Field(default_factory=list, description="采购方列表数据")
    permission_error: Optional[bool] = Field(None, description="是否为权限相关错误")


@router.post("/xqd", response_model=XqdQueryResponse)
async def query_xqd(
    request: XqdQueryRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    查询需求单数据

    需要ELECTRON平台访问权限
    """
    start_time = time.time()

    try:
        logger.info(f"用户 {current_user.get('username')} 请求需求单查询")
        
        # 检查用户是否有ELECTRON访问权限
        if not current_user.get('electron_access'):
            logger.warning(f"用户 {current_user.get('username')} 没有ELECTRON访问权限")
            raise HTTPException(
                status_code=403,
                detail="需要电子超市平台访问权限才能查询需求单"
            )
        
        # 获取ELECTRON访问令牌
        electron_token = current_user.get('electron_token')
        if not electron_token:
            logger.warning(f"用户 {current_user.get('username')} 缺少ELECTRON访问令牌")
            raise HTTPException(
                status_code=401,
                detail="缺少电子超市平台访问令牌"
            )
        
        # 验证查询参数（至少需要一个条件）
        query_params = request.dict()
        has_condition = any(value for value in query_params.values() if value)
        if not has_condition:
            raise HTTPException(
                status_code=400,
                detail="请至少提供一个查询条件"
            )
        
        # 执行需求单查询
        result = await xqd_service.query_ask_sheet_list(
            access_token=electron_token,
            client_ip=current_user.get('client_ip'),
            **query_params
        )
        
        logger.info(f"需求单查询完成 - 用户: {current_user.get('username')}, 结果: {result.get('success')}")

        # 记录查询统计（异步，不影响响应）
        try:
            # 计算响应时间
            response_time = (time.time() - start_time) * 1000

            await analytics_service.record_query_log(
                module='xqd',
                user=current_user.get('username', 'unknown'),
                platform=current_user.get('platform', 'ELECTRON'),
                success=result.get('success', False),
                response_time=response_time,
                status_code=200 if result.get('success') else 500,
                error_message=result.get('message') if not result.get('success') else None,
                query_params=query_params,
                result_count=result.get('total', 0)
            )
        except Exception as e:
            logger.warning(f"记录需求单查询统计失败: {str(e)}")

        return XqdQueryResponse(
            success=result['success'],
            message=result['message'],
            data=result['data'],
            total=result.get('total'),
            permission_error=result.get('permission_error')
        )
        
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"需求单查询API异常: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"查询过程中发生内部错误: {str(e)}"
        )


@router.post("/xqd/download")
async def download_xqd_report(
    request: XqdDownloadRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    下载需求单报价单

    需要ELECTRON平台访问权限
    """
    try:
        logger.info(f"用户 {current_user.get('username')} 请求下载报价单: {request.askSheetCode}")

        # 检查用户是否有ELECTRON访问权限
        if not current_user.get('electron_access'):
            logger.warning(f"用户 {current_user.get('username')} 没有ELECTRON访问权限")
            raise HTTPException(
                status_code=403,
                detail="需要电子超市平台访问权限才能下载报价单"
            )

        # 获取ELECTRON访问令牌
        electron_token = current_user.get('electron_token')
        if not electron_token:
            logger.warning(f"用户 {current_user.get('username')} 缺少ELECTRON访问令牌")
            raise HTTPException(
                status_code=401,
                detail="缺少电子超市平台访问令牌"
            )

        # 执行报价单下载
        file_content = await xqd_service.download_ask_sheet_template(
            access_token=electron_token,
            ask_sheet_code=request.askSheetCode,
            ask_sheet_id=request.askSheetId,
            client_ip=current_user.get('client_ip')
        )

        if not file_content:
            raise HTTPException(
                status_code=404,
                detail="报价单文件不存在或下载失败"
            )

        # 创建文件流响应
        file_stream = io.BytesIO(file_content)

        logger.info(f"报价单下载成功 - 用户: {current_user.get('username')}, 文件: {request.askSheetCode}.xlsx")

        return StreamingResponse(
            io.BytesIO(file_content),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={
                "Content-Disposition": f"attachment; filename={request.askSheetCode}.xlsx"
            }
        )

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"报价单下载API异常: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"下载过程中发生内部错误: {str(e)}"
        )


@router.post("/xqd/detail", response_model=XqdDetailResponse)
async def get_xqd_detail(
    request: XqdDetailRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取需求单基础信息详情

    需要ELECTRON平台访问权限
    """
    try:
        logger.info(f"用户 {current_user.get('username')} 请求需求单详情: {request.askSheetCode}")

        # 检查用户是否有ELECTRON访问权限
        if not current_user.get('electron_access'):
            logger.warning(f"用户 {current_user.get('username')} 没有ELECTRON访问权限")
            raise HTTPException(
                status_code=403,
                detail="需要电子超市平台访问权限才能查看需求单详情"
            )

        # 获取ELECTRON访问令牌
        electron_token = current_user.get('electron_token')
        if not electron_token:
            logger.warning(f"用户 {current_user.get('username')} 缺少ELECTRON访问令牌")
            raise HTTPException(
                status_code=401,
                detail="缺少电子超市平台访问令牌"
            )

        # 执行需求单详情查询
        result = await xqd_service.get_ask_sheet_detail(
            access_token=electron_token,
            ask_sheet_code=request.askSheetCode,
            ask_sheet_id=request.askSheetId,
            client_ip=current_user.get('client_ip')
        )

        logger.info(f"需求单详情查询完成 - 用户: {current_user.get('username')}, 结果: {result.get('success')}")

        return XqdDetailResponse(
            success=result['success'],
            message=result['message'],
            data=result['data'],
            permission_error=result.get('permission_error')
        )

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"需求单详情查询API异常: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"查询过程中发生内部错误: {str(e)}"
        )


@router.post("/xqd/detail-list", response_model=XqdDetailListResponse)
async def get_xqd_detail_list(
    request: XqdDetailRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取需求单产品列表详情

    需要ELECTRON平台访问权限
    """
    try:
        logger.info(f"用户 {current_user.get('username')} 请求需求单产品列表: {request.askSheetCode}")

        # 检查用户是否有ELECTRON访问权限
        if not current_user.get('electron_access'):
            logger.warning(f"用户 {current_user.get('username')} 没有ELECTRON访问权限")
            raise HTTPException(
                status_code=403,
                detail="需要电子超市平台访问权限才能查看需求单产品列表"
            )

        # 获取ELECTRON访问令牌
        electron_token = current_user.get('electron_token')
        if not electron_token:
            logger.warning(f"用户 {current_user.get('username')} 缺少ELECTRON访问令牌")
            raise HTTPException(
                status_code=401,
                detail="缺少电子超市平台访问令牌"
            )

        # 执行需求单产品列表查询
        result = await xqd_service.get_ask_sheet_detail_list(
            access_token=electron_token,
            ask_sheet_code=request.askSheetCode,
            ask_sheet_id=request.askSheetId,
            client_ip=current_user.get('client_ip')
        )

        logger.info(f"需求单产品列表查询完成 - 用户: {current_user.get('username')}, 结果: {result.get('success')}")

        return XqdDetailListResponse(
            success=result['success'],
            message=result['message'],
            data=result['data'],
            permission_error=result.get('permission_error')
        )

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"需求单产品列表查询API异常: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"查询过程中发生内部错误: {str(e)}"
        )


@router.get("/xqd/purchaser-list", response_model=PurchaserListResponse)
async def get_purchaser_list(
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    获取采购方列表

    需要ELECTRON平台访问权限
    """
    try:
        logger.info(f"用户 {current_user.get('username')} 请求采购方列表")

        # 检查用户权限
        if not current_user.get('electron_access'):
            logger.warning(f"用户 {current_user.get('username')} 没有ELECTRON访问权限")
            raise HTTPException(
                status_code=403,
                detail="需要ELECTRON平台访问权限"
            )

        # 获取用户的ELECTRON令牌
        electron_token = current_user.get('electron_token')
        if not electron_token:
            logger.warning(f"用户 {current_user.get('username')} 缺少ELECTRON令牌")
            raise HTTPException(
                status_code=401,
                detail="缺少ELECTRON平台访问令牌，请重新登录"
            )

        # 执行采购方列表查询
        result = await xqd_service.get_purchaser_list(
            access_token=electron_token,
            client_ip=current_user.get('client_ip')
        )

        logger.info(f"采购方列表查询完成 - 用户: {current_user.get('username')}, 结果: {result.get('success')}")

        # 记录查询统计（异步，不影响响应）
        try:
            await analytics_service.record_query_log(
                module='xqd',
                user=current_user.get('username', 'unknown'),
                platform=current_user.get('platform', 'ELECTRON'),
                success=result.get('success', False),
                response_time=result.get('response_time'),
                status_code=200 if result.get('success') else 500,
                error_message=result.get('message') if not result.get('success') else None,
                query_params={'query_type': 'purchaser_list'},
                result_count=len(result.get('data', []))
            )
        except Exception as e:
            logger.warning(f"记录采购方列表查询统计失败: {str(e)}")

        return PurchaserListResponse(
            success=result['success'],
            message=result['message'],
            data=result['data'],
            permission_error=result.get('permission_error')
        )

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"采购方列表查询API异常: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"查询过程中发生内部错误: {str(e)}"
        )
