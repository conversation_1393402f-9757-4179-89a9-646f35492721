{% extends "base.html" %}

{% block title %}{{ module_name }} - {{ app_name }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="/static/css/modules/product-detail.css?ver={{ app_version }}">
{% endblock %}

{% block content %}
<div class="page-wrapper">
    <!-- 页面头部 -->
    <div class="page-header d-print-none">
        <div class="container-xl">
            <div class="row g-2 align-items-center">
                <div class="col">
                    <!-- 面包屑导航 -->
                    <div class="page-pretitle">
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="/">首页</a></li>
                                <li class="breadcrumb-item"><a href="/modules/product">商品状态查询</a></li>
                                <li class="breadcrumb-item active" aria-current="page">商品详情</li>
                            </ol>
                        </nav>
                    </div>
                    <h2 class="page-title">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M7 8h10"></path>
                            <path d="M7 12h4l1 8h4l1 -8h4"></path>
                            <path d="M17 4v4a1 1 0 0 1 -1 1h-8a1 1 0 0 1 -1 -1v-4a3 3 0 0 1 6 0"></path>
                        </svg>
                        商品详情
                    </h2>
                    <div class="text-muted mt-1" id="detail-subtitle">
                        查看商品的详细信息和规格参数
                    </div>
                </div>
                <div class="col-auto ms-auto d-print-none">
                    <div class="btn-list">
                        <a href="/modules/product" class="btn btn-outline-secondary">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M5 12l14 0"></path>
                                <path d="M5 12l6 6"></path>
                                <path d="M5 12l6 -6"></path>
                            </svg>
                            返回列表
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页面内容 -->
    <div class="page-body">
        <div class="container-xl">
            <!-- 登录提示 -->
            <div class="row" id="login-prompt" style="display: none;">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center">
                            <h3>需要登录</h3>
                            <p class="text-muted">请先登录到电子超市以查看商品详情</p>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#login-modal">
                                立即登录
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- ELECTRON权限不足提示 -->
            <div class="row" id="electron-access-denied" style="display: none;">
                <div class="col-12">
                    <div class="card border-warning">
                        <div class="card-body text-center">
                            <div class="text-warning mb-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-lg" width="48" height="48" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M12 9v2m0 4v.01"></path>
                                    <path d="M5 19h14a2 2 0 0 0 1.84 -2.75l-7.1 -12.25a2 2 0 0 0 -3.5 0l-7.1 12.25a2 2 0 0 0 1.75 2.75"></path>
                                </svg>
                            </div>
                            <h3 class="text-warning">电子超市平台权限不足</h3>
                            <p class="text-muted">您当前的账号没有电子超市平台访问权限，无法查看商品详情。</p>
                            <p class="text-muted">请联系管理员开通权限或使用具有相应权限的账号登录。</p>
                            <div class="btn-list">
                                <button class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#login-modal">
                                    重新登录
                                </button>
                                <a href="/modules/product" class="btn btn-outline-secondary">
                                    返回列表
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div id="main-content" style="display: none;">

            <!-- 卡片1：基础信息 -->
            <div class="card detail-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M14 3v4a1 1 0 0 0 1 1h4"></path>
                            <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"></path>
                        </svg>
                        基础信息
                    </h3>
                </div>
                <div class="card-body position-relative" id="basic-info-container">
                    <!-- 加载状态 -->
                    <div class="loading-overlay" id="basic-loading">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>

                    <!-- 基础信息内容 -->
                    <div id="basic-content" style="display: none;">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="detail-field">
                                    <div class="label">业务分类</div>
                                    <div class="value" id="detail-businessTypeName">-</div>
                                </div>
                                <div class="detail-field">
                                    <div class="label">品目</div>
                                    <div class="value" id="detail-categoryPath">-</div>
                                </div>
                                <div class="detail-field">
                                    <div class="label">品目产品词</div>
                                    <div class="value" id="detail-wordName">-</div>
                                </div>
                                <div class="detail-field">
                                    <div class="label">品牌</div>
                                    <div class="value" id="detail-brandName">-</div>
                                </div>
                                <div class="detail-field">
                                    <div class="label">品牌资质</div>
                                    <div class="value">暂无</div>
                                </div>
                                <div class="detail-field">
                                    <div class="label">行业资质</div>
                                    <div class="value">暂无</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="detail-field">
                                    <div class="label">商品编码（SKU）</div>
                                    <div class="value" id="detail-sku">-</div>
                                </div>
                                <div class="detail-field">
                                    <div class="label">商品名称</div>
                                    <div class="value" id="detail-name">-</div>
                                </div>
                                <div class="detail-field">
                                    <div class="label">商品属性</div>
                                    <div class="value" id="detail-properties">-</div>
                                </div>
                                <div class="detail-field">
                                    <div class="label">库存</div>
                                    <div class="value" id="detail-stock">-</div>
                                </div>
                                <div class="detail-field">
                                    <div class="label">出货日</div>
                                    <div class="value" id="detail-shipmentDate">-</div>
                                </div>
                                <div class="detail-field">
                                    <div class="label">国际编码</div>
                                    <div class="value">暂无</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="detail-field">
                                    <div class="label">商品主图</div>
                                    <div class="value" id="detail-mainImage">-</div>
                                </div>
                                <div class="detail-field">
                                    <div class="label">商品子图</div>
                                    <div class="value" id="detail-subImages">-</div>
                                </div>
                                <div class="detail-field">
                                    <div class="label">质量标准</div>
                                    <div class="value" id="detail-qualitiesType">-</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 错误状态 -->
                    <div id="basic-error" style="display: none;">
                        <div class="alert alert-danger">
                            <h4>加载失败</h4>
                            <p class="mb-0" id="basic-error-message">无法加载基础信息</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 卡片2：价格信息 -->
            <div class="card detail-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M12 6h7a2 2 0 0 1 2 2v10a2 2 0 0 1 -2 2h-7m0 -14h-7a2 2 0 0 0 -2 2v10a2 2 0 0 0 2 2h7m0 -14v14"></path>
                        </svg>
                        价格信息
                    </h3>
                </div>
                <div class="card-body position-relative" id="price-info-container">
                    <!-- 加载状态 -->
                    <div class="loading-overlay" id="price-loading">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>

                    <!-- 价格信息内容 -->
                    <div id="price-content" style="display: none;">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="detail-field">
                                    <div class="label">价格凭证类型</div>
                                    <div class="value" id="detail-priceVoucherType">-</div>
                                </div>
                                <div class="detail-field">
                                    <div class="label">比价结果凭证编号</div>
                                    <div class="value" id="detail-bidResultVoucherNum">-</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="detail-field">
                                    <div class="label">市场价（元）</div>
                                    <div class="value" id="detail-mallPrice">-</div>
                                </div>
                                <div class="detail-field">
                                    <div class="label">协议价（元）</div>
                                    <div class="text-success fw-bold" id="detail-price">-</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="detail-field">
                                    <div class="label">组成凭证价格</div>
                                    <div class="value" id="detail-costPrice">-</div>
                                </div>
                                <div class="detail-field">
                                    <div class="label">报价单</div>
                                    <div class="value" id="detail-quotationImages">-</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="detail-field">
                                    <div class="label">利润率</div>
                                    <div class="value" id="detail-profitRate">-</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 错误状态 -->
                    <div id="price-error" style="display: none;">
                        <div class="alert alert-danger">
                            <h4>加载失败</h4>
                            <p class="mb-0" id="price-error-message">无法加载价格信息</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 卡片3：厂商信息 -->
            <div class="card detail-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M19 7.5v3a2 2 0 0 0 2 2v1a2 2 0 0 0 -2 2v3a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-3a2 2 0 0 0 -2 -2v-1a2 2 0 0 0 2 -2v-3a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2z"></path>
                        </svg>
                        厂商信息
                    </h3>
                </div>
                <div class="card-body position-relative" id="manufacturer-info-container">
                    <!-- 加载状态 -->
                    <div class="loading-overlay" id="manufacturer-loading">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>

                    <!-- 厂商信息内容 -->
                    <div id="manufacturer-content" style="display: none;">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="detail-field">
                                    <div class="label">商品制造商</div>
                                    <div class="value" id="detail-manufacturer">-</div>
                                </div>
                                <div class="detail-field">
                                    <div class="label">制造商统一社会信用代码</div>
                                    <div class="value" id="detail-manufacturerCode">-</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="detail-field">
                                    <div class="label">商品用途说明</div>
                                    <div class="value" id="detail-useDesc">-</div>
                                </div>
                                <div class="detail-field">
                                    <div class="label">商品资质</div>
                                    <div class="value" id="detail-productEligibilityImages">-</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 错误状态 -->
                    <div id="manufacturer-error" style="display: none;">
                        <div class="alert alert-danger">
                            <h4>加载失败</h4>
                            <p class="mb-0" id="manufacturer-error-message">无法加载厂商信息</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 卡片4：辅助信息 -->
            <div class="card detail-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M9 7h-3a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-3"></path>
                            <path d="M9 15h3l8.5 -8.5a1.5 1.5 0 0 0 -3 -3l-8.5 8.5v3"></path>
                            <path d="M16 5l3 3"></path>
                        </svg>
                        辅助信息
                    </h3>
                </div>
                <div class="card-body position-relative" id="auxiliary-info-container">
                    <!-- 加载状态 -->
                    <div class="loading-overlay" id="auxiliary-loading">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>

                    <!-- 辅助信息内容 -->
                    <div id="auxiliary-content" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="detail-field">
                                    <div class="label">搜索关键词</div>
                                    <div class="value">暂无</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="detail-field">
                                    <div class="label">其他凭证</div>
                                    <div class="value">暂无</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 错误状态 -->
                    <div id="auxiliary-error" style="display: none;">
                        <div class="alert alert-danger">
                            <h4>加载失败</h4>
                            <p class="mb-0" id="auxiliary-error-message">无法加载辅助信息</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 卡片5：审核历史 -->
            <div class="card detail-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M9 12l2 2 4 -4"></path>
                            <path d="M21 12c-1 0 -3 -1 -3 -3s2 -3 3 -3 3 1 3 3 -2 3 -3 3"></path>
                            <path d="M3 12c1 0 3 -1 3 -3s-2 -3 -3 -3 -3 1 -3 3 2 3 3 3"></path>
                        </svg>
                        审核历史
                    </h3>
                </div>
                <div class="card-body position-relative" id="approval-history-container">
                    <!-- 加载状态 -->
                    <div class="loading-overlay" id="approval-loading">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>

                    <!-- 审核历史内容 -->
                    <div id="approval-content" style="display: none;">
                        <div id="approval-history-list">
                            <!-- 审核历史记录将在这里动态生成 -->
                        </div>
                    </div>

                    <!-- 空状态 -->
                    <div id="approval-empty" style="display: none;">
                        <div class="empty">
                            <div class="empty-img">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-xl text-muted" width="128" height="128" viewBox="0 0 24 24" stroke-width="1" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M9 12l2 2 4 -4"></path>
                                    <path d="M21 12c-1 0 -3 -1 -3 -3s2 -3 3 -3 3 1 3 3 -2 3 -3 3"></path>
                                    <path d="M3 12c1 0 3 -1 3 -3s-2 -3 -3 -3 -3 1 -3 3 2 3 3 3"></path>
                                </svg>
                            </div>
                            <p class="empty-title">暂无审核记录</p>
                            <p class="empty-subtitle text-muted">该商品暂时没有审核历史记录</p>
                        </div>
                    </div>

                    <!-- 错误状态 -->
                    <div id="approval-error" style="display: none;">
                        <div class="alert alert-danger">
                            <h4>加载失败</h4>
                            <p class="mb-0" id="approval-error-message">无法加载审核历史信息</p>
                        </div>
                    </div>
                </div>
            </div>
            </div> <!-- 关闭 main-content -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="/static/js/modules/product-detail.js?ver={{ app_version }}"></script>
{% endblock %}
