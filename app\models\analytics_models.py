"""
分析统计数据库模型
支持详细的API调用统计、性能监控和用户行为分析
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean, Float, Text, JSON, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from datetime import datetime
from typing import Dict, Any, Optional

Base = declarative_base()


class QueryLog(Base):
    """
    查询日志表 - 记录所有模块的API调用详情
    """
    __tablename__ = 'query_logs'

    # 主键和基本信息
    id = Column(Integer, primary_key=True, autoincrement=True)
    log_id = Column(String(100), unique=True, nullable=False, comment='日志唯一标识')
    
    # 时间信息
    timestamp = Column(DateTime, default=func.now(), nullable=False, comment='调用时间')
    created_at = Column(DateTime, default=func.now(), nullable=False, comment='记录创建时间')
    
    # 模块和用户信息
    module = Column(String(50), nullable=False, comment='模块名称')
    user = Column(String(100), nullable=False, comment='用户名')
    platform = Column(String(20), nullable=False, comment='平台类型')
    
    # 请求信息
    success = Column(Boolean, nullable=False, default=True, comment='请求是否成功')
    response_time = Column(Float, nullable=True, comment='响应时间(毫秒)')
    status_code = Column(Integer, nullable=True, comment='HTTP状态码')
    error_message = Column(Text, nullable=True, comment='错误信息')
    
    # 网络信息
    client_ip = Column(String(45), nullable=True, comment='客户端IP地址')
    user_agent = Column(String(500), nullable=True, comment='用户代理')
    
    # 查询参数和结果
    query_params = Column(JSON, nullable=True, comment='查询参数')
    result_count = Column(Integer, nullable=True, comment='返回结果数量')
    
    # 时间维度字段（用于快速聚合查询）
    hour = Column(Integer, nullable=False, comment='小时(0-23)')
    date = Column(String(10), nullable=False, comment='日期(YYYY-MM-DD)')
    week = Column(Integer, nullable=False, comment='周数')
    month = Column(Integer, nullable=False, comment='月份')
    year = Column(Integer, nullable=False, comment='年份')
    weekday = Column(Integer, nullable=False, comment='星期几(0-6)')
    
    # 索引定义
    __table_args__ = (
        Index('idx_module_date', 'module', 'date'),
        Index('idx_user_timestamp', 'user', 'timestamp'),
        Index('idx_platform_success', 'platform', 'success'),
        Index('idx_timestamp', 'timestamp'),
        Index('idx_date_hour', 'date', 'hour'),
        Index('idx_week_year', 'week', 'year'),
        Index('idx_module_success_date', 'module', 'success', 'date'),
        {'comment': '查询日志表 - 记录所有API调用的详细信息'}
    )

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'log_id': self.log_id,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'module': self.module,
            'user': self.user,
            'platform': self.platform,
            'success': self.success,
            'response_time': self.response_time,
            'status_code': self.status_code,
            'error_message': self.error_message,
            'client_ip': self.client_ip,
            'user_agent': self.user_agent,
            'query_params': self.query_params,
            'result_count': self.result_count,
            'hour': self.hour,
            'date': self.date,
            'week': self.week,
            'month': self.month,
            'year': self.year,
            'weekday': self.weekday
        }


class ModuleStats(Base):
    """
    模块统计表 - 预聚合的模块统计数据
    """
    __tablename__ = 'module_stats'

    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # 统计维度
    module = Column(String(50), nullable=False, comment='模块名称')
    date = Column(String(10), nullable=False, comment='统计日期(YYYY-MM-DD)')
    hour = Column(Integer, nullable=True, comment='小时(0-23)，NULL表示全天统计')
    
    # 统计数据
    total_calls = Column(Integer, default=0, comment='总调用次数')
    success_calls = Column(Integer, default=0, comment='成功调用次数')
    failed_calls = Column(Integer, default=0, comment='失败调用次数')
    avg_response_time = Column(Float, nullable=True, comment='平均响应时间(毫秒)')
    max_response_time = Column(Float, nullable=True, comment='最大响应时间(毫秒)')
    min_response_time = Column(Float, nullable=True, comment='最小响应时间(毫秒)')
    
    # 用户统计
    unique_users = Column(Integer, default=0, comment='独立用户数')
    unique_ips = Column(Integer, default=0, comment='独立IP数')
    
    # 时间信息
    created_at = Column(DateTime, default=func.now(), nullable=False, comment='创建时间')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 索引定义
    __table_args__ = (
        Index('idx_module_date_hour', 'module', 'date', 'hour'),
        Index('idx_date', 'date'),
        Index('idx_module_date', 'module', 'date'),
        {'comment': '模块统计表 - 预聚合的统计数据'}
    )

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'module': self.module,
            'date': self.date,
            'hour': self.hour,
            'total_calls': self.total_calls,
            'success_calls': self.success_calls,
            'failed_calls': self.failed_calls,
            'success_rate': round((self.success_calls / self.total_calls * 100) if self.total_calls > 0 else 100.0, 2),
            'avg_response_time': self.avg_response_time,
            'max_response_time': self.max_response_time,
            'min_response_time': self.min_response_time,
            'unique_users': self.unique_users,
            'unique_ips': self.unique_ips,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class SystemHealth(Base):
    """
    系统健康状态表 - 记录系统整体健康指标
    """
    __tablename__ = 'system_health'

    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # 时间信息
    timestamp = Column(DateTime, default=func.now(), nullable=False, comment='检查时间')
    date = Column(String(10), nullable=False, comment='日期(YYYY-MM-DD)')
    hour = Column(Integer, nullable=False, comment='小时(0-23)')
    
    # 系统指标
    total_requests = Column(Integer, default=0, comment='总请求数')
    success_rate = Column(Float, default=100.0, comment='成功率(%)')
    avg_response_time = Column(Float, nullable=True, comment='平均响应时间(毫秒)')
    active_users = Column(Integer, default=0, comment='活跃用户数')
    active_modules = Column(Integer, default=0, comment='活跃模块数')
    
    # 错误统计
    error_count = Column(Integer, default=0, comment='错误数量')
    timeout_count = Column(Integer, default=0, comment='超时数量')
    
    # 性能指标
    peak_hour_requests = Column(Integer, default=0, comment='峰值小时请求数')
    peak_hour = Column(Integer, nullable=True, comment='峰值小时')
    
    # 索引定义
    __table_args__ = (
        Index('idx_date_hour', 'date', 'hour'),
        Index('idx_timestamp', 'timestamp'),
        {'comment': '系统健康状态表'}
    )

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'date': self.date,
            'hour': self.hour,
            'total_requests': self.total_requests,
            'success_rate': self.success_rate,
            'avg_response_time': self.avg_response_time,
            'active_users': self.active_users,
            'active_modules': self.active_modules,
            'error_count': self.error_count,
            'timeout_count': self.timeout_count,
            'peak_hour_requests': self.peak_hour_requests,
            'peak_hour': self.peak_hour
        }


class AnalyticsConfig(Base):
    """
    分析配置表 - 存储分析统计的配置信息
    """
    __tablename__ = 'analytics_config'

    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # 配置信息
    config_key = Column(String(100), unique=True, nullable=False, comment='配置键')
    config_value = Column(Text, nullable=True, comment='配置值')
    config_type = Column(String(20), default='string', comment='配置类型')
    description = Column(String(500), nullable=True, comment='配置描述')
    
    # 时间信息
    created_at = Column(DateTime, default=func.now(), nullable=False, comment='创建时间')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 索引定义
    __table_args__ = (
        Index('idx_config_key', 'config_key'),
        {'comment': '分析配置表'}
    )

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'config_key': self.config_key,
            'config_value': self.config_value,
            'config_type': self.config_type,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
