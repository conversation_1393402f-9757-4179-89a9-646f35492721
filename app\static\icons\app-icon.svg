<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <!-- 定义渐变 -->
  <defs>
    <radialGradient id="bgGradient" cx="50%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#4a90e2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#206bc4;stop-opacity:1" />
    </radialGradient>
    <linearGradient id="planeGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- 背景圆形 -->
  <circle cx="256" cy="256" r="240" fill="url(#bgGradient)" stroke="none"/>

  <!-- 内层圆形边框 -->
  <circle cx="256" cy="256" r="220" fill="none" stroke="#ffffff" stroke-width="2" opacity="0.3"/>

  <!-- 飞机主体 -->
  <g transform="translate(256, 256)">
    <!-- 飞机机身 -->
    <ellipse cx="0" cy="0" rx="85" ry="22" fill="url(#planeGradient)" transform="rotate(-15)" filter="url(#glow)"/>

    <!-- 飞机机翼 -->
    <ellipse cx="-15" cy="0" rx="65" ry="10" fill="#ffffff" transform="rotate(-15)" opacity="0.95"/>
    <ellipse cx="15" cy="0" rx="45" ry="8" fill="#ffffff" transform="rotate(-15)" opacity="0.9"/>

    <!-- 飞机尾翼 -->
    <path d="M -65 -6 L -85 -18 L -80 0 L -85 18 L -65 6 Z" fill="#ffffff" transform="rotate(-15)" opacity="0.95"/>

    <!-- 飞机头部 -->
    <ellipse cx="70" cy="0" rx="10" ry="6" fill="#ffffff" transform="rotate(-15)"/>

    <!-- 飞机窗户 -->
    <ellipse cx="20" cy="0" rx="8" ry="4" fill="#206bc4" transform="rotate(-15)" opacity="0.6"/>
    <ellipse cx="0" cy="0" rx="8" ry="4" fill="#206bc4" transform="rotate(-15)" opacity="0.6"/>
    <ellipse cx="-20" cy="0" rx="8" ry="4" fill="#206bc4" transform="rotate(-15)" opacity="0.6"/>
  </g>
  
  <!-- 数据图表元素 -->
  <g transform="translate(180, 350)">
    <!-- 柱状图 -->
    <rect x="0" y="-30" width="12" height="30" fill="#ffffff" opacity="0.8"/>
    <rect x="16" y="-45" width="12" height="45" fill="#ffffff" opacity="0.9"/>
    <rect x="32" y="-25" width="12" height="25" fill="#ffffff" opacity="0.8"/>
    <rect x="48" y="-40" width="12" height="40" fill="#ffffff" opacity="0.9"/>
    <rect x="64" y="-35" width="12" height="35" fill="#ffffff" opacity="0.8"/>
    
    <!-- 基线 -->
    <line x1="-5" y1="0" x2="85" y2="0" stroke="#ffffff" stroke-width="2" opacity="0.6"/>
  </g>
  
  <!-- 齿轮元素 -->
  <g transform="translate(350, 180)">
    <!-- 大齿轮 -->
    <circle cx="0" cy="0" r="25" fill="none" stroke="#ffffff" stroke-width="3" opacity="0.7"/>
    <circle cx="0" cy="0" r="15" fill="#ffffff" opacity="0.3"/>
    
    <!-- 齿轮齿 -->
    <g opacity="0.7">
      <rect x="-2" y="-28" width="4" height="6" fill="#ffffff"/>
      <rect x="-2" y="22" width="4" height="6" fill="#ffffff"/>
      <rect x="22" y="-2" width="6" height="4" fill="#ffffff"/>
      <rect x="-28" y="-2" width="6" height="4" fill="#ffffff"/>
      <rect x="16" y="-18" width="4" height="4" fill="#ffffff" transform="rotate(45)"/>
      <rect x="-20" y="-18" width="4" height="4" fill="#ffffff" transform="rotate(-45)"/>
      <rect x="16" y="14" width="4" height="4" fill="#ffffff" transform="rotate(-45)"/>
      <rect x="-20" y="14" width="4" height="4" fill="#ffffff" transform="rotate(45)"/>
    </g>
    
    <!-- 小齿轮 -->
    <g transform="translate(35, 35)">
      <circle cx="0" cy="0" r="15" fill="none" stroke="#ffffff" stroke-width="2" opacity="0.6"/>
      <circle cx="0" cy="0" r="8" fill="#ffffff" opacity="0.2"/>
      <rect x="-1" y="-17" width="2" height="4" fill="#ffffff" opacity="0.6"/>
      <rect x="-1" y="13" width="2" height="4" fill="#ffffff" opacity="0.6"/>
      <rect x="13" y="-1" width="4" height="2" fill="#ffffff" opacity="0.6"/>
      <rect x="-17" y="-1" width="4" height="2" fill="#ffffff" opacity="0.6"/>
    </g>
  </g>
  
  <!-- 数据流线条 -->
  <g opacity="0.4">
    <path d="M 120 200 Q 180 180 240 200 Q 300 220 360 200" 
          fill="none" stroke="#ffffff" stroke-width="2" stroke-dasharray="5,5"/>
    <path d="M 140 320 Q 200 300 260 320 Q 320 340 380 320" 
          fill="none" stroke="#ffffff" stroke-width="2" stroke-dasharray="5,5"/>
    <path d="M 100 260 Q 160 240 220 260 Q 280 280 340 260" 
          fill="none" stroke="#ffffff" stroke-width="2" stroke-dasharray="5,5"/>
  </g>
  
  <!-- 中心装饰圆点 -->
  <circle cx="256" cy="256" r="4" fill="#ffffff" opacity="0.8"/>
  
  <!-- 外层装饰环 -->
  <circle cx="256" cy="256" r="250" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.2"/>
</svg>
