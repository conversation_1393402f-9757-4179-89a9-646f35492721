/**
 * 数据分析页面管理器
 * 提供完整的数据分析和可视化功能
 */

class AnalyticsPageManager {
    constructor() {
        this.trendsChart = null;
        this.refreshInterval = null;
        this.refreshIntervalMs = 600000; // 30秒刷新一次
        this.currentHours = 24; // 默认显示24小时
        
        // API端点
        this.apiEndpoints = {
            hourlyTrends: '/api/v1/analytics/hourly-trends',
            weeklyStats: '/api/v1/analytics/weekly-module-stats',
            realtimeDashboard: '/api/v1/analytics/realtime-dashboard',
            moduleDetailLogs: '/api/v1/analytics/module-detail-logs',
            moduleSummaryStats: '/api/v1/analytics/module-summary-stats'
        };

        // 详情模态框相关
        this.currentModule = null;
        this.currentModuleName = null;
        this.currentPage = 1;
        this.currentFilters = {
            startDate: null,
            endDate: null,
            statusFilter: 'all',
            searchTerm: null
        };
        
        // 初始化
        this.init();
    }

    /**
     * 初始化页面
     */
    async init() {
        try {
            // 绑定事件
            this.bindEvents();
            
            // 初始化图表
            this.initializeChart();
            
            // 加载初始数据
            await this.loadAllData();
            
            // 启动定时刷新
            this.startAutoRefresh();
            
            console.log('数据分析页面初始化完成');
        } catch (error) {
            console.error('页面初始化失败:', error);
            this.showError('页面初始化失败，请刷新页面重试');
        }
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 刷新按钮
        const refreshBtn = document.getElementById('refreshDataBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.loadAllData());
        }

        // 导出按钮
        const exportBtn = document.getElementById('exportDataBtn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => this.exportData());
        }

        // 时间范围选择
        document.querySelectorAll('[data-hours]').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const hours = parseInt(e.target.dataset.hours);
                this.changeTimeRange(hours);
            });
        });

        // 搜索功能
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filterTable(e.target.value);
            });
        }

        // 页面可见性变化时刷新数据
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.loadAllData();
            }
        });

        // 详情模态框事件绑定
        this.bindDetailModalEvents();
    }

    /**
     * 初始化趋势图表
     */
    initializeChart() {
        const canvas = document.getElementById('trendsChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        
        this.trendsChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '查询次数',
                    data: [],
                    borderColor: '#206bc4',
                    backgroundColor: 'rgba(32, 107, 196, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#206bc4',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 4,
                    pointHoverRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#206bc4',
                        borderWidth: 1,
                        cornerRadius: 6,
                        displayColors: false,
                        callbacks: {
                            title: function(context) {
                                return `时间: ${context[0].label}`;
                            },
                            label: function(context) {
                                return `查询次数: ${context.parsed.y}`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        display: true,
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#6c757d',
                            maxTicksLimit: 12
                        }
                    },
                    y: {
                        display: true,
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        ticks: {
                            color: '#6c757d',
                            stepSize: 1
                        }
                    }
                },
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                }
            }
        });
    }

    /**
     * 加载所有数据
     */
    async loadAllData() {
        try {
            this.showLoading(true);
            
            // 并行加载数据
            const [trendsResponse, statsResponse] = await Promise.all([
                fetch(`${this.apiEndpoints.hourlyTrends}?hours=${this.currentHours}`),
                fetch(this.apiEndpoints.weeklyStats)
            ]);

            if (!trendsResponse.ok || !statsResponse.ok) {
                throw new Error('API请求失败');
            }

            const trendsData = await trendsResponse.json();
            const statsData = await statsResponse.json();

            // 更新各个组件
            if (trendsData.success) {
                this.updateTrendsChart(trendsData.data);
                this.updateOverviewStats(trendsData.data);
            }

            if (statsData.success) {
                this.updateModuleStats(statsData.data);
                this.updateDetailsTable(statsData.data);
                this.updateSystemHealth(statsData.data);
            }

            // 更新最后刷新时间
            this.updateLastRefreshTime();

        } catch (error) {
            console.error('加载数据失败:', error);
            this.showError('数据加载失败，请检查网络连接');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 更新趋势图表
     */
    updateTrendsChart(trendsData) {
        if (!this.trendsChart || !trendsData) return;

        try {
            this.trendsChart.data.labels = trendsData.hour_labels || [];
            this.trendsChart.data.datasets[0].data = trendsData.hourly_data || [];
            this.trendsChart.update('none');
        } catch (error) {
            console.error('更新趋势图表失败:', error);
        }
    }

    /**
     * 更新概览统计
     */
    updateOverviewStats(trendsData) {
        const elements = {
            totalQueries24h: document.getElementById('totalQueries24h'),
            successRate24h: document.getElementById('successRate24h'),
            avgResponseTime: document.getElementById('avgResponseTime'),
            peakHour: document.getElementById('peakHour')
        };

        if (elements.totalQueries24h) {
            elements.totalQueries24h.textContent = trendsData.total_queries || 0;
        }
        if (elements.successRate24h) {
            const rate = trendsData.success_rate || 100;
            elements.successRate24h.textContent = `${rate}%`;
            elements.successRate24h.className = rate >= 95 ? 'text-success' : rate >= 90 ? 'text-warning' : 'text-danger';
        }
        if (elements.avgResponseTime) {
            elements.avgResponseTime.textContent = `${trendsData.avg_response_time || 0}ms`;
        }
        if (elements.peakHour) {
            elements.peakHour.textContent = trendsData.peak_hour || '--:--';
        }
    }

    /**
     * 更新模块统计
     */
    updateModuleStats(statsData) {
        const container = document.getElementById('moduleStatsContainer');
        if (!container || !statsData.modules) return;

        const totalThisWeek = document.getElementById('totalQueriesWeek');
        const weeklyChange = document.getElementById('weeklyChange');
        
        if (totalThisWeek) {
            totalThisWeek.textContent = statsData.total_this_week || 0;
        }
        
        if (weeklyChange) {
            const change = ((statsData.total_this_week || 0) - (statsData.total_last_week || 0));
            const changePercent = statsData.total_last_week > 0 ? 
                Math.round((change / statsData.total_last_week) * 100) : 0;
            
            weeklyChange.textContent = `${change >= 0 ? '+' : ''}${change} (${changePercent >= 0 ? '+' : ''}${changePercent}%)`;
            weeklyChange.className = change >= 0 ? 'text-success' : 'text-danger';
        }

        // 生成模块统计HTML
        let html = '';
        Object.entries(statsData.modules).forEach(([moduleKey, moduleStats]) => {
            const change = moduleStats.change || 0;
            const changeClass = change >= 0 ? 'text-success' : 'text-danger';
            const changeIcon = change >= 0 ? '↗' : '↘';
            
            html += `
                <div class="module-stat-item">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="module-stat-name">${moduleStats.name}</div>
                            <div class="module-stat-success-rate">成功率: ${moduleStats.success_rate}%</div>
                        </div>
                        <div class="text-end">
                            <div class="module-stat-count">${moduleStats.this_week}</div>
                            <div class="module-stat-change ${changeClass}">
                                ${changeIcon} ${Math.abs(change)}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;
    }

    /**
     * 更新详细数据表格
     */
    updateDetailsTable(statsData) {
        const tbody = document.getElementById('detailsTableBody');
        if (!tbody || !statsData.modules) return;

        let html = '';
        Object.entries(statsData.modules).forEach(([moduleKey, moduleStats]) => {
            const change = moduleStats.change || 0;
            const changePercent = moduleStats.change_percent || 0;
            const changeClass = change >= 0 ? 'text-success' : 'text-danger';
            const statusClass = moduleStats.success_rate >= 95 ? 'status-healthy' : 
                               moduleStats.success_rate >= 90 ? 'status-warning' : 'status-error';

            html += `
                <tr>
                    <td>
                        <div class="d-flex align-items-center">
                            <div class="${statusClass}">
                                ${moduleStats.name}
                            </div>
                        </div>
                    </td>
                    <td><strong>${moduleStats.this_week}</strong></td>
                    <td>${moduleStats.last_week}</td>
                    <td>
                        <span class="${changeClass}">
                            ${change >= 0 ? '+' : ''}${change} (${changePercent >= 0 ? '+' : ''}${changePercent}%)
                        </span>
                    </td>
                    <td>
                        <span class="${moduleStats.success_rate >= 95 ? 'text-success' : moduleStats.success_rate >= 90 ? 'text-warning' : 'text-danger'}">
                            ${moduleStats.success_rate}%
                        </span>
                    </td>
                    <td>${moduleStats.avg_response_time || 0}ms</td>
                    <td>
                        <span class="status-indicator ${statusClass}">
                            <div class="status-dot"></div>
                        </span>
                    </td>
                    <td>
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="analyticsManager.showModuleDetail('${moduleKey}', '${moduleStats.name}')">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"/>
                                <path d="M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6"/>
                            </svg>
                            详情
                        </button>
                    </td>
                </tr>
            `;
        });

        tbody.innerHTML = html;
    }

    /**
     * 更新系统健康状态
     */
    updateSystemHealth(statsData) {
        // 计算系统健康评分
        let healthScore = 100;
        const issues = [];

        // 检查成功率
        Object.values(statsData.modules || {}).forEach(moduleStats => {
            if (moduleStats.success_rate < 90) {
                healthScore -= 20;
                issues.push(`${moduleStats.name}模块成功率偏低`);
            } else if (moduleStats.success_rate < 95) {
                healthScore -= 10;
                issues.push(`${moduleStats.name}模块需要关注`);
            }
        });

        // 更新显示
        const healthScoreEl = document.getElementById('healthScore');
        const healthBadgeEl = document.getElementById('systemHealthBadge');
        const healthIssuesEl = document.getElementById('healthIssues');

        if (healthScoreEl) {
            healthScoreEl.textContent = Math.max(0, healthScore);
        }

        if (healthBadgeEl) {
            let status, statusText, statusClass;
            if (healthScore >= 90) {
                status = 'excellent';
                statusText = '优秀';
                statusClass = 'bg-success';
            } else if (healthScore >= 75) {
                status = 'good';
                statusText = '良好';
                statusClass = 'bg-info';
            } else if (healthScore >= 60) {
                status = 'warning';
                statusText = '警告';
                statusClass = 'bg-warning';
            } else {
                status = 'critical';
                statusText = '严重';
                statusClass = 'bg-danger';
            }

            healthBadgeEl.textContent = statusText;
            healthBadgeEl.className = `badge ${statusClass}`;
        }

        if (healthIssuesEl) {
            if (issues.length > 0) {
                healthIssuesEl.innerHTML = issues.map(issue => `<div class="text-warning">⚠ ${issue}</div>`).join('');
            } else {
                healthIssuesEl.innerHTML = '<div class="text-success">✓ 系统运行正常，无发现问题</div>';
            }
        }
    }

    /**
     * 改变时间范围
     */
    async changeTimeRange(hours) {
        this.currentHours = hours;

        try {
            const response = await fetch(`${this.apiEndpoints.hourlyTrends}?hours=${hours}`);
            if (!response.ok) throw new Error('API请求失败');

            const data = await response.json();
            if (data.success) {
                this.updateTrendsChart(data.data);
                this.updateOverviewStats(data.data);
            }
        } catch (error) {
            console.error('更新时间范围失败:', error);
            this.showError('更新时间范围失败');
        }
    }

    /**
     * 过滤表格
     */
    filterTable(searchTerm) {
        const tbody = document.getElementById('detailsTableBody');
        if (!tbody) return;

        const rows = tbody.querySelectorAll('tr');
        const term = searchTerm.toLowerCase();

        rows.forEach(row => {
            const moduleName = row.querySelector('td:first-child').textContent.toLowerCase();
            const shouldShow = moduleName.includes(term);
            row.style.display = shouldShow ? '' : 'none';
        });
    }

    /**
     * 导出数据
     */
    async exportData() {
        try {
            const response = await fetch(this.apiEndpoints.realtimeDashboard);
            if (!response.ok) throw new Error('获取数据失败');

            const data = await response.json();
            if (!data.success) throw new Error('数据格式错误');

            // 生成CSV内容
            const csvContent = this.generateCSV(data.data);

            // 下载文件
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);

            link.setAttribute('href', url);
            link.setAttribute('download', `analytics_report_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            this.showSuccess('数据导出成功');
        } catch (error) {
            console.error('导出数据失败:', error);
            this.showError('导出数据失败');
        }
    }

    /**
     * 生成CSV内容
     */
    generateCSV(data) {
        const headers = ['模块名称', '本周调用', '上周调用', '变化', '成功率(%)', '平均响应时间(ms)'];
        let csv = headers.join(',') + '\n';

        if (data.module_stats && data.module_stats.modules) {
            Object.entries(data.module_stats.modules).forEach(([key, stats]) => {
                const row = [
                    `"${stats.name}"`,
                    stats.this_week || 0,
                    stats.last_week || 0,
                    stats.change || 0,
                    stats.success_rate || 100,
                    stats.avg_response_time || 0
                ];
                csv += row.join(',') + '\n';
            });
        }

        return csv;
    }

    /**
     * 启动自动刷新
     */
    startAutoRefresh() {
        this.stopAutoRefresh();
        this.refreshInterval = setInterval(() => {
            this.loadAllData();
        }, this.refreshIntervalMs);
    }

    /**
     * 停止自动刷新
     */
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    /**
     * 更新最后刷新时间
     */
    updateLastRefreshTime() {
        const element = document.getElementById('lastUpdateTime');
        if (element) {
            element.textContent = new Date().toLocaleTimeString();
        }
    }

    /**
     * 显示加载状态
     */
    showLoading(show) {
        const refreshBtn = document.getElementById('refreshDataBtn');
        if (refreshBtn) {
            if (show) {
                refreshBtn.disabled = true;
                refreshBtn.innerHTML = '<div class="loading-spinner"></div> 加载中...';
            } else {
                refreshBtn.disabled = false;
                refreshBtn.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                        <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"/>
                        <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"/>
                    </svg>
                    刷新数据
                `;
            }
        }
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        console.error(message);
        // 这里可以集成Toast通知组件
        alert(`错误: ${message}`);
    }

    /**
     * 显示成功信息
     */
    showSuccess(message) {
        console.log(message);
        // 这里可以集成Toast通知组件
        alert(`成功: ${message}`);
    }

    /**
     * 绑定详情模态框事件
     */
    bindDetailModalEvents() {
        // 搜索按钮
        const searchBtn = document.getElementById('detailSearchBtn');
        if (searchBtn) {
            searchBtn.addEventListener('click', () => this.searchDetailLogs());
        }

        // 搜索输入框回车事件
        const searchInput = document.getElementById('detailSearchInput');
        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.searchDetailLogs();
                }
            });
        }

        // 日期筛选变化事件
        const startDateInput = document.getElementById('detailStartDate');
        const endDateInput = document.getElementById('detailEndDate');
        const statusFilter = document.getElementById('detailStatusFilter');

        if (startDateInput) {
            startDateInput.addEventListener('change', () => this.searchDetailLogs());
        }
        if (endDateInput) {
            endDateInput.addEventListener('change', () => this.searchDetailLogs());
        }
        if (statusFilter) {
            statusFilter.addEventListener('change', () => this.searchDetailLogs());
        }

        // 导出按钮
        const exportBtn = document.getElementById('exportDetailBtn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => this.exportDetailLogs());
        }
    }

    /**
     * 显示模块详情
     */
    async showModuleDetail(moduleKey, moduleName) {
        try {
            this.currentModule = moduleKey;
            this.currentModuleName = moduleName;
            this.currentPage = 1;

            // 设置模态框标题
            const modalTitle = document.getElementById('moduleDetailTitle');
            if (modalTitle) {
                modalTitle.textContent = `${moduleName} - 详细信息`;
            }

            // 设置默认日期范围（最近7天）
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(endDate.getDate() - 7);

            const startDateInput = document.getElementById('detailStartDate');
            const endDateInput = document.getElementById('detailEndDate');

            if (startDateInput) {
                startDateInput.value = startDate.toISOString().split('T')[0];
            }
            if (endDateInput) {
                endDateInput.value = endDate.toISOString().split('T')[0];
            }

            // 重置筛选条件
            const statusFilter = document.getElementById('detailStatusFilter');
            const searchInput = document.getElementById('detailSearchInput');

            if (statusFilter) {
                statusFilter.value = 'all';
            }
            if (searchInput) {
                searchInput.value = '';
            }

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('moduleDetailModal'));
            modal.show();

            // 加载数据
            await this.loadModuleSummaryStats();
            await this.loadModuleDetailLogs();

        } catch (error) {
            console.error('显示模块详情失败:', error);
            this.showError('显示模块详情失败');
        }
    }

    /**
     * 加载模块汇总统计
     */
    async loadModuleSummaryStats() {
        try {
            const startDate = document.getElementById('detailStartDate')?.value;
            const endDate = document.getElementById('detailEndDate')?.value;

            const params = new URLSearchParams();
            if (startDate) params.append('start_date', startDate);
            if (endDate) params.append('end_date', endDate);

            const response = await fetch(`${this.apiEndpoints.moduleSummaryStats}/${this.currentModule}?${params}`);
            if (!response.ok) throw new Error('API请求失败');

            const result = await response.json();
            if (result.success) {
                this.updateModuleSummaryDisplay(result.data);
            } else {
                throw new Error(result.message || '获取汇总统计失败');
            }

        } catch (error) {
            console.error('加载模块汇总统计失败:', error);
            this.showError('加载汇总统计失败');
        }
    }

    /**
     * 更新模块汇总统计显示
     */
    updateModuleSummaryDisplay(data) {
        const elements = {
            detailTotalRequests: document.getElementById('detailTotalRequests'),
            detailSuccessRequests: document.getElementById('detailSuccessRequests'),
            detailFailedRequests: document.getElementById('detailFailedRequests'),
            detailSuccessRate: document.getElementById('detailSuccessRate'),
            detailAvgResponseTime: document.getElementById('detailAvgResponseTime'),
            detailMaxResponseTime: document.getElementById('detailMaxResponseTime'),
            detailUniqueUsers: document.getElementById('detailUniqueUsers'),
        };

        if (data.summary) {
            const summary = data.summary;

            if (elements.detailTotalRequests) {
                elements.detailTotalRequests.textContent = summary.total_requests || 0;
            }
            if (elements.detailSuccessRequests) {
                elements.detailSuccessRequests.textContent = summary.success_requests || 0;
            }
            if (elements.detailFailedRequests) {
                elements.detailFailedRequests.textContent = summary.failed_requests || 0;
            }
            if (elements.detailSuccessRate) {
                elements.detailSuccessRate.textContent = `${summary.success_rate || 100}%`;
            }
            if (elements.detailUniqueUsers) {
                elements.detailUniqueUsers.textContent = summary.unique_users || 0;
            }
        }

        if (data.performance) {
            const performance = data.performance;

            if (elements.detailAvgResponseTime) {
                elements.detailAvgResponseTime.textContent = `${performance.avg_response_time || 0}ms`;
            }
            if (elements.detailMaxResponseTime) {
                elements.detailMaxResponseTime.textContent = `${performance.max_response_time || 0}ms`;
            }
        }
    }

    /**
     * 加载模块详细日志
     */
    async loadModuleDetailLogs(page = 1) {
        try {
            this.currentPage = page;

            const params = new URLSearchParams();
            params.append('page', page);
            params.append('page_size', 50);

            // 添加筛选条件
            const startDate = document.getElementById('detailStartDate')?.value;
            const endDate = document.getElementById('detailEndDate')?.value;
            const statusFilter = document.getElementById('detailStatusFilter')?.value;
            const searchTerm = document.getElementById('detailSearchInput')?.value;

            if (startDate) params.append('start_date', startDate);
            if (endDate) params.append('end_date', endDate);
            if (statusFilter && statusFilter !== 'all') params.append('status_filter', statusFilter);
            if (searchTerm) params.append('search_term', searchTerm);

            const response = await fetch(`${this.apiEndpoints.moduleDetailLogs}/${this.currentModule}?${params}`);
            if (!response.ok) throw new Error('API请求失败');

            const result = await response.json();
            if (result.success) {
                this.updateDetailLogsTable(result.data);
                this.updateDetailPagination(result.data.pagination);
            } else {
                throw new Error(result.message || '获取详细日志失败');
            }

        } catch (error) {
            console.error('加载模块详细日志失败:', error);
            this.showError('加载详细日志失败');
        }
    }

    /**
     * 更新详细日志表格
     */
    updateDetailLogsTable(data) {
        const tbody = document.getElementById('detailLogsTableBody');
        if (!tbody || !data.logs) return;

        let html = '';
        data.logs.forEach(log => {
            const timestamp = new Date(log.timestamp).toLocaleString();
            const statusClass = log.success ? 'text-success' : 'text-danger';
            const statusText = log.success ? '成功' : '失败';

            // 处理响应时间显示和颜色
            let responseTime = '-';
            let responseTimeClass = 'text-muted';
            if (log.response_time && log.response_time > 0) {
                responseTime = `${Math.round(log.response_time)}ms`;
                if (log.response_time <= 1000) {
                    responseTimeClass = 'text-success response-time-fast';
                } else if (log.response_time <= 3000) {
                    responseTimeClass = 'text-warning response-time-medium';
                } else {
                    responseTimeClass = 'text-danger response-time-slow';
                }
            }

            // 处理错误信息显示
            let errorMessage = '-';
            let errorMessageClass = 'text-muted';
            if (!log.success && log.error_message) {
                errorMessage = log.error_message;
                errorMessageClass = 'text-danger';
            } else if (log.success) {
                errorMessage = '-';
                errorMessageClass = 'text-muted';
            }

            const querySummary = log.query_summary || '-';

            html += `
                <tr>
                    <td>
                        <div class="text-muted">${timestamp}</div>
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            <div>
                                <div class="fw-bold">${log.user}</div>
                                <div class="text-muted small">${log.module_display_name}</div>
                            </div>
                        </div>
                    </td>
                    <td>
                        <span class="badge ${log.success ? 'bg-success' : 'bg-danger'}">
                            ${statusText}
                        </span>
                    </td>
                    <td>
                        <span class="${responseTimeClass}" title="响应时间: ${responseTime}">
                            ${responseTime}
                        </span>
                    </td>
                    <td>
                        <div class="text-truncate" style="max-width: 200px;" title="${querySummary}">
                            ${querySummary}
                        </div>
                    </td>
                    <td>
                        <div class="text-truncate ${errorMessageClass}" style="max-width: 200px;" title="${errorMessage}">
                            ${errorMessage}
                        </div>
                    </td>
                </tr>
            `;
        });

        tbody.innerHTML = html;
    }

    /**
     * 更新详细日志分页
     */
    updateDetailPagination(pagination) {
        // 更新显示信息
        const showingStart = document.getElementById('detailShowingStart');
        const showingEnd = document.getElementById('detailShowingEnd');
        const totalCount = document.getElementById('detailTotalCount');

        if (showingStart) {
            showingStart.textContent = ((pagination.current_page - 1) * pagination.page_size) + 1;
        }
        if (showingEnd) {
            const end = Math.min(pagination.current_page * pagination.page_size, pagination.total_count);
            showingEnd.textContent = end;
        }
        if (totalCount) {
            totalCount.textContent = pagination.total_count;
        }

        // 更新分页按钮
        const paginationContainer = document.getElementById('detailPagination');
        if (!paginationContainer) return;

        let html = '';

        // 上一页按钮
        if (pagination.has_prev) {
            html += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="analyticsManager.loadModuleDetailLogs(${pagination.current_page - 1})">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <polyline points="15,6 9,12 15,18"/>
                        </svg>
                        上一页
                    </a>
                </li>
            `;
        }

        // 页码按钮
        const startPage = Math.max(1, pagination.current_page - 2);
        const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === pagination.current_page ? 'active' : '';
            html += `
                <li class="page-item ${activeClass}">
                    <a class="page-link" href="#" onclick="analyticsManager.loadModuleDetailLogs(${i})">${i}</a>
                </li>
            `;
        }

        // 下一页按钮
        if (pagination.has_next) {
            html += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="analyticsManager.loadModuleDetailLogs(${pagination.current_page + 1})">
                        下一页
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <polyline points="9,6 15,12 9,18"/>
                        </svg>
                    </a>
                </li>
            `;
        }

        paginationContainer.innerHTML = html;
    }

    /**
     * 搜索详细日志
     */
    async searchDetailLogs() {
        // 重置到第一页
        this.currentPage = 1;
        await this.loadModuleDetailLogs(1);
        // 同时刷新汇总统计
        await this.loadModuleSummaryStats();
    }

    /**
     * 导出详细日志
     */
    async exportDetailLogs() {
        try {
            const params = new URLSearchParams();
            params.append('page', 1);
            params.append('page_size', 10000); // 导出更多数据

            // 添加筛选条件
            const startDate = document.getElementById('detailStartDate')?.value;
            const endDate = document.getElementById('detailEndDate')?.value;
            const statusFilter = document.getElementById('detailStatusFilter')?.value;
            const searchTerm = document.getElementById('detailSearchInput')?.value;

            if (startDate) params.append('start_date', startDate);
            if (endDate) params.append('end_date', endDate);
            if (statusFilter && statusFilter !== 'all') params.append('status_filter', statusFilter);
            if (searchTerm) params.append('search_term', searchTerm);

            const response = await fetch(`${this.apiEndpoints.moduleDetailLogs}/${this.currentModule}?${params}`);
            if (!response.ok) throw new Error('API请求失败');

            const result = await response.json();
            if (!result.success) throw new Error(result.message || '获取数据失败');

            // 生成CSV内容
            const csvContent = this.generateDetailLogsCSV(result.data.logs);

            // 下载文件
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);

            link.setAttribute('href', url);
            link.setAttribute('download', `${this.currentModuleName}_详细日志_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            this.showSuccess('详细日志导出成功');

        } catch (error) {
            console.error('导出详细日志失败:', error);
            this.showError('导出详细日志失败');
        }
    }

    /**
     * 生成详细日志CSV内容
     */
    generateDetailLogsCSV(logs) {
        const headers = ['时间', '用户', '模块', '状态', '响应时间(ms)', '查询内容', '错误信息'];
        let csv = headers.join(',') + '\n';

        logs.forEach(log => {
            const row = [
                `"${new Date(log.timestamp).toLocaleString()}"`,
                `"${log.user}"`,
                `"${log.module_display_name}"`,
                `"${log.success ? '成功' : '失败'}"`,
                log.response_time || 0,
                `"${(log.query_summary || '').replace(/"/g, '""')}"`,
                `"${(log.error_message || '').replace(/"/g, '""')}"`
            ];
            csv += row.join(',') + '\n';
        });

        return csv;
    }
}

// 全局变量
let analyticsManager = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 确保Chart.js已加载
    if (typeof Chart !== 'undefined') {
        analyticsManager = new AnalyticsPageManager();
    } else {
        console.error('Chart.js未加载，分析页面功能将不可用');
    }
});
