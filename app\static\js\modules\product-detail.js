/**
 * 商品详情页面 JavaScript
 * 使用模块化设计模式，提供完整的详情查看、权限检查、数据展示功能
 */

// 商品详情模块对象
const ProductDetailModule = {
    // 模块初始化
    init() {
        console.log('商品详情页面已加载');

        // 从URL参数获取商品信息，与xqd详情页面保持一致的参数获取方式
        const urlParams = new URLSearchParams(window.location.search);
        const sku = urlParams.get('sku') || '';
        const productId = urlParams.get('productId') || '';

        if (!productId && !sku) {
            this.showError('basic', '缺少必要的参数信息');
            this.showError('price', '缺少必要的参数信息');
            this.showError('manufacturer', '缺少必要的参数信息');
            this.showError('auxiliary', '缺少必要的参数信息');
            return;
        }

        // 更新页面标题
        this.updatePageTitle(sku || productId);

        // 初始化认证检查
        this.initAuthCheck(sku, productId);
    },

    // 更新页面标题
    updatePageTitle(identifier) {
        const subtitle = document.getElementById('detail-subtitle');
        if (subtitle) {
            subtitle.textContent = `商品编码: ${identifier}`;
        }
    },

    // 初始化认证检查
    async initAuthCheck(sku, productId) {
        try {
            // 检查认证管理器是否可用
            if (typeof authManager === 'undefined') {
                console.error('认证管理器未加载');
                this.showLoginPrompt();
                return;
            }

            // 检查认证状态
            const isAuthenticated = await authManager.checkAuthStatus();

            if (isAuthenticated) {
                // 已认证，检查ELECTRON权限
                this.checkElectronAccessAndLoadData(sku, productId);
            } else {
                // 未认证，显示登录提示
                this.showLoginPrompt();
            }
        } catch (error) {
            console.error('认证检查失败:', error);
            this.showLoginPrompt();
        }
    },

    // 检查ELECTRON访问权限并加载数据
    checkElectronAccessAndLoadData(sku, productId) {
        if (!authManager.userInfo) {
            this.showLoginPrompt();
            return;
        }

        if (!authManager.userInfo.electron_access) {
            // 没有ELECTRON权限，显示权限不足提示
            this.showElectronAccessDenied();
            return;
        }

        // 有权限，显示主要内容并加载数据
        this.showMainContent();
        this.loadDetailData(sku, productId);
    },

    // 显示登录提示
    showLoginPrompt() {
        const loginPrompt = document.getElementById('login-prompt');
        const mainContent = document.getElementById('main-content');
        const electronAccessDenied = document.getElementById('electron-access-denied');

        if (loginPrompt) loginPrompt.style.display = 'block';
        if (mainContent) mainContent.style.display = 'none';
        if (electronAccessDenied) electronAccessDenied.style.display = 'none';
    },

    // 显示ELECTRON权限不足提示
    showElectronAccessDenied() {
        const loginPrompt = document.getElementById('login-prompt');
        const mainContent = document.getElementById('main-content');
        const electronAccessDenied = document.getElementById('electron-access-denied');

        if (loginPrompt) loginPrompt.style.display = 'none';
        if (mainContent) mainContent.style.display = 'none';
        if (electronAccessDenied) electronAccessDenied.style.display = 'block';
    },

    // 显示主要内容
    showMainContent() {
        const loginPrompt = document.getElementById('login-prompt');
        const mainContent = document.getElementById('main-content');
        const electronAccessDenied = document.getElementById('electron-access-denied');

        if (loginPrompt) loginPrompt.style.display = 'none';
        if (mainContent) mainContent.style.display = 'block';
        if (electronAccessDenied) electronAccessDenied.style.display = 'none';
    },

    // 加载商品详情数据
    async loadDetailData(sku, productId) {
        try {
            this.showLoading('basic');
            this.showLoading('price');
            this.showLoading('manufacturer');
            this.showLoading('auxiliary');
            this.showLoading('approval');

            const response = await fetch('/api/v1/data/product/detail', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authManager.token}`
                },
                body: JSON.stringify({
                    id: productId || sku
                })
            });

            if (!response.ok) {
                let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
                try {
                    const errorData = await response.json();
                    errorMessage = errorData.message || errorData.detail || errorMessage;
                } catch (e) {
                    console.warn('无法解析错误响应为JSON');
                }
                throw new Error(errorMessage);
            }

            const result = await response.json();

            if (!result.success) {
                // 检查是否为权限相关错误
                if (result.permission_error) {
                    authManager.showNotification('权限已失效，正在重新登录...', 'warning');
                    authManager.handleTokenExpiry();
                    return;
                }
                throw new Error(result.message || '获取商品详情失败');
            }

            // 显示详情数据
            this.displayDetailData(result.data);

        } catch (error) {
            console.error('加载商品详情错误:', error);

            // 显示错误信息
            this.showError('basic', error.message);
            this.showError('price', error.message);
            this.showError('manufacturer', error.message);
            this.showError('auxiliary', error.message);
            this.showError('approval', error.message);

            // 显示重试按钮
            this.showRetryButton(sku, productId);
        }
    },

    // 显示详情数据
    displayDetailData(data) {
        try {
            const skuDto = data.skuDto || {};
            const skuPropValList = data.skuPropValDtoList || [];
            const approvalHistory = data.approvalHistory || [];

            // 显示基础信息
            this.displayBasicInfo(skuDto, skuPropValList);

            // 显示价格信息
            this.displayPriceInfo(skuDto);

            // 显示厂商信息
            this.displayManufacturerInfo(skuDto);

            // 显示辅助信息
            this.displayAuxiliaryInfo(skuDto);

            // 显示审核历史
            this.displayApprovalHistory(approvalHistory);

        } catch (error) {
            console.error('显示商品详情数据错误:', error);
            this.showError('basic', '数据显示错误');
            this.showError('price', '数据显示错误');
            this.showError('manufacturer', '数据显示错误');
            this.showError('auxiliary', '数据显示错误');
            this.showError('approval', '数据显示错误');
        }
    },

    // 显示基础信息
    displayBasicInfo(skuDto, skuPropValList) {
        this.hideLoading('basic');

        // 构建品目路径
        const categoryPath = [
            skuDto.firstCategoryName,
            skuDto.secondCategoryName,
            skuDto.categoryName
        ].filter(Boolean).join(' / ');

        // 构建商品属性字符串
        const properties = skuPropValList.map(prop =>
            `${prop.propName}: ${prop.propValue}`
        ).join('; ') || '暂无';

        // 更新基础信息字段
        this.updateField('detail-businessTypeName', skuDto.businessTypeName);
        this.updateField('detail-categoryPath', categoryPath);
        this.updateField('detail-wordName', skuDto.wordName);
        this.updateField('detail-brandName', skuDto.brandName);
        this.updateField('detail-sku', skuDto.sku);
        this.updateField('detail-name', skuDto.name);
        this.updateField('detail-properties', properties);
        this.updateField('detail-stock', skuDto.stock || '暂无');
        this.updateField('detail-shipmentDate', skuDto.shipmentDate || '暂无');
        this.updateField('detail-qualitiesType', this.formatQualitiesType(skuDto.qualitiesType));

        // 处理商品主图
        this.displayMainImage(skuDto.imagePathUrl);

        // 处理商品子图
        this.displaySubImages(skuDto.imageMinorUrlList);

        // 显示内容
        const content = document.getElementById('basic-content');
        if (content) content.style.display = 'block';
    },

    // 显示价格信息
    displayPriceInfo(skuDto) {
        this.hideLoading('price');

        // 更新价格信息字段
        this.updateField('detail-priceVoucherType', this.formatPriceVoucherType(skuDto.priceVoucherType));
        this.updateField('detail-mallPrice', this.formatPrice(skuDto.mallPrice));
        this.updateField('detail-price', this.formatPrice(skuDto.price));
        this.updateField('detail-bidResultVoucherNum', skuDto.bidResultVoucherNum);
        this.updateField('detail-costPrice', this.formatPrice(skuDto.costPrice));
        this.updateField('detail-profitRate', skuDto.profitRate ? `${skuDto.profitRate}%` : '-');

        // 处理报价单图片
        this.displayQuotationImages(skuDto.quotationUrlList);

        // 显示内容
        const content = document.getElementById('price-content');
        if (content) content.style.display = 'block';
    },

    // 显示厂商信息
    displayManufacturerInfo(skuDto) {
        this.hideLoading('manufacturer');

        // 更新厂商信息字段
        this.updateField('detail-manufacturer', skuDto.manufacturer);
        this.updateField('detail-manufacturerCode', skuDto.manufacturerCode);
        this.updateField('detail-useDesc', skuDto.useDesc);

        // 处理商品资质图片
        this.displayProductEligibilityImages(skuDto.productEligibilityUrls);

        // 显示内容
        const content = document.getElementById('manufacturer-content');
        if (content) content.style.display = 'block';
    },

    // 显示辅助信息
    displayAuxiliaryInfo(skuDto) {
        this.hideLoading('auxiliary');

        // 辅助信息目前都是固定的"暂无"，直接显示内容
        const content = document.getElementById('auxiliary-content');
        if (content) content.style.display = 'block';
    },

    // 显示审核历史
    displayApprovalHistory(approvalHistory) {
        this.hideLoading('approval');

        if (!approvalHistory || !Array.isArray(approvalHistory) || approvalHistory.length === 0) {
            // 显示空状态
            const emptyElement = document.getElementById('approval-empty');
            if (emptyElement) emptyElement.style.display = 'block';
            return;
        }

        // 生成审核历史列表HTML
        const historyListElement = document.getElementById('approval-history-list');
        if (!historyListElement) return;

        const historyHtml = approvalHistory.map((item, index) => {
            return `
                <div class="approval-record ${index === 0 ? 'latest' : ''}">
                    <div class="approval-header">
                        <div class="approval-phase">
                            <span class="badge bg-secondary">${this.formatApprovalPhase(item.approvalPhase)}</span>
                        </div>
                        <div class="approval-status">
                            ${this.formatApprovalStatus(item.approvalStatus)}
                        </div>
                        <div class="approval-time">
                            <small class="text-muted">${this.formatDateTime(item.createTime)}</small>
                        </div>
                    </div>
                    <div class="approval-body">
                        <div class="approval-content">
                            <strong>审核内容：</strong>
                            <span class="approval-text">${this.escapeHtml(item.approvalContent || '无')}</span>
                        </div>
                        <div class="approval-user">
                            <strong>审核人员：</strong>
                            <span class="user-name">${this.escapeHtml(item.userName || '未知')}</span>
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        historyListElement.innerHTML = historyHtml;

        // 显示内容
        const content = document.getElementById('approval-content');
        if (content) content.style.display = 'block';
    },

    // 显示商品主图
    displayMainImage(imageUrl) {
        const element = document.getElementById('detail-mainImage');
        if (!element) return;

        if (!imageUrl) {
            element.innerHTML = '<span class="text-muted">暂无</span>';
            return;
        }

        element.innerHTML = `
            <div class="image-preview">
                <img src="${this.escapeHtml(imageUrl)}"
                     alt="商品主图"
                     class="img-thumbnail"
                     style="max-width: 100px; max-height: 100px; cursor: pointer;"
                     onclick="ProductDetailModule.previewImage('${this.escapeHtml(imageUrl)}', '商品主图')"
                     onerror="this.parentElement.innerHTML='<div class=\\'img-placeholder\\' style=\\'width:100px;height:100px;\\'>图片加载失败</div>';">
            </div>
        `;
    },

    // 显示商品子图
    displaySubImages(imageList) {
        const element = document.getElementById('detail-subImages');
        if (!element) return;

        if (!imageList || !Array.isArray(imageList) || imageList.length === 0) {
            element.innerHTML = '<span class="text-muted">暂无</span>';
            return;
        }

        const imagesHtml = imageList.map((imageUrl, index) => `
            <img src="${this.escapeHtml(imageUrl)}"
                 alt="商品子图${index + 1}"
                 class="img-thumbnail me-1 mb-1"
                 style="max-width: 60px; max-height: 60px; cursor: pointer;"
                 onclick="ProductDetailModule.previewImage('${this.escapeHtml(imageUrl)}', '商品子图${index + 1}')"
                 onerror="this.outerHTML='<div class=\\'img-placeholder me-1 mb-1\\' style=\\'width:60px;height:60px;\\'>加载失败</div>';">
        `).join('');

        element.innerHTML = `<div class="image-list">${imagesHtml}</div>`;
    },

    // 显示报价单图片
    displayQuotationImages(quotationList) {
        const element = document.getElementById('detail-quotationImages');
        if (!element) return;

        if (!quotationList || !Array.isArray(quotationList) || quotationList.length === 0) {
            element.innerHTML = '<span class="text-muted">暂无</span>';
            return;
        }

        const imagesHtml = quotationList.map((item, index) => {
            const fileName = this.escapeHtml(item.fileName || `报价单${index + 1}`);
            const fileUrl = this.escapeHtml(item.fileUrl || '');
            return `
                <img src="${fileUrl}"
                     alt="${fileName}"
                     class="img-thumbnail me-1 mb-1"
                     style="max-width: 60px; max-height: 60px; cursor: pointer;"
                     onclick="ProductDetailModule.previewImage('${fileUrl}', '${fileName}')"
                     onerror="this.outerHTML='<div class=\\'img-placeholder me-1 mb-1\\' style=\\'width:60px;height:60px;\\'>加载失败</div>';">
            `;
        }).join('');

        element.innerHTML = `<div class="image-list">${imagesHtml}</div>`;
    },

    // 显示商品资质图片
    displayProductEligibilityImages(eligibilityList) {
        const element = document.getElementById('detail-productEligibilityImages');
        if (!element) return;

        if (!eligibilityList || !Array.isArray(eligibilityList) || eligibilityList.length === 0) {
            element.innerHTML = '<span class="text-muted">暂无</span>';
            return;
        }

        const imagesHtml = eligibilityList.map((item, index) => {
            const fileName = this.escapeHtml(item.fileName || `商品资质${index + 1}`);
            const fileUrl = this.escapeHtml(item.fileUrl || '');
            return `
                <img src="${fileUrl}"
                     alt="${fileName}"
                     class="img-thumbnail me-1 mb-1"
                     style="max-width: 60px; max-height: 60px; cursor: pointer;"
                     onclick="ProductDetailModule.previewImage('${fileUrl}', '${fileName}')"
                     onerror="this.outerHTML='<div class=\\'img-placeholder me-1 mb-1\\' style=\\'width:60px;height:60px;\\'>加载失败</div>';">
            `;
        }).join('');

        element.innerHTML = `<div class="image-list">${imagesHtml}</div>`;
    },

    // 更新字段值
    updateField(fieldId, value) {
        const element = document.getElementById(fieldId);
        if (element) {
            if (typeof value === 'string' && value.includes('<')) {
                element.innerHTML = value;
            } else {
                element.textContent = value || '-';
            }
        }
    },

    // 显示加载状态
    showLoading(type) {
        const loadingElement = document.getElementById(`${type}-loading`);
        const contentElement = document.getElementById(`${type}-content`);
        const errorElement = document.getElementById(`${type}-error`);
        const emptyElement = document.getElementById(`${type}-empty`);

        if (loadingElement) loadingElement.style.display = 'flex';
        if (contentElement) contentElement.style.display = 'none';
        if (errorElement) errorElement.style.display = 'none';
        if (emptyElement) emptyElement.style.display = 'none';
    },

    // 隐藏加载状态
    hideLoading(type) {
        const loadingElement = document.getElementById(`${type}-loading`);
        if (loadingElement) loadingElement.style.display = 'none';
    },

    // 显示错误状态
    showError(type, message) {
        const loadingElement = document.getElementById(`${type}-loading`);
        const contentElement = document.getElementById(`${type}-content`);
        const errorElement = document.getElementById(`${type}-error`);
        const errorMessageElement = document.getElementById(`${type}-error-message`);
        const emptyElement = document.getElementById(`${type}-empty`);

        if (loadingElement) loadingElement.style.display = 'none';
        if (contentElement) contentElement.style.display = 'none';
        if (emptyElement) emptyElement.style.display = 'none';
        if (errorElement) errorElement.style.display = 'block';
        if (errorMessageElement) {
            // 根据错误类型显示不同的友好提示
            let friendlyMessage = message;
            if (message.includes('401') || message.includes('unauthorized')) {
                friendlyMessage = '登录已过期，请重新登录';
            } else if (message.includes('403') || message.includes('forbidden')) {
                friendlyMessage = '权限不足，无法访问该资源';
            } else if (message.includes('404') || message.includes('not found')) {
                friendlyMessage = '未找到相关数据';
            } else if (message.includes('timeout') || message.includes('网络')) {
                friendlyMessage = '网络连接超时，请检查网络连接后重试';
            } else if (message.includes('500') || message.includes('internal')) {
                friendlyMessage = '服务器内部错误，请稍后重试';
            }
            errorMessageElement.textContent = friendlyMessage;
        }
    },



    // 图片预览功能
    previewImage(imageUrl, title) {
        // 创建模态框显示大图
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${title}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body text-center">
                        <img src="${imageUrl}"
                             alt="${title}"
                             class="img-fluid"
                             style="max-width: 100%; max-height: 70vh;"
                             onerror="this.src='/static/images/placeholder.png'; this.onerror=null;">
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();

        // 模态框关闭后移除DOM元素
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    },

    // 格式化价格
    formatPrice(price) {
        if (price === null || price === undefined || price === '') return '-';
        return `¥${parseFloat(price).toFixed(2)}`;
    },

    // 格式化价格凭证类型
    formatPriceVoucherType(type) {
        const typeMap = {
            'WIN_BID_RESULT_VOUCHER': '有竞价成功的结果凭证',
            'HAVE_COMPARABLE_BENCHMARK_SUPPLIER': '有可比标杆供应商'
        };
        return typeMap[type] || type || '-';
    },

    // 格式化质量标准
    formatQualitiesType(type) {
        const typeMap = {
            'NO_QUALITY_STANDARDS_NO_VOUCHER': '无质量标准无凭证',
            'HAS_QUALITY_STANDARDS_HAS_VOUCHER': '有质量标准有凭证',
            'HAS_QUALITY_STANDARDS_NO_VOUCHER': '有质量标准无凭证',
            'NO_QUALITY_STANDARDS_HAS_VOUCHER': '无质量标准有凭证'
        };
        return typeMap[type] || type || '-';
    },

    // 格式化审核阶段
    formatApprovalPhase(phase) {
        const phaseMap = {
            'FIRST_APPROVE': '初审',
            'SECOND_APPROVE': '复审',
            'FINAL_APPROVE': '终审',
            'REVIEW': '审核',
            'APPROVE': '审批'
        };
        return phaseMap[phase] || phase || '未知阶段';
    },

    // 格式化审核状态
    formatApprovalStatus(status) {
        const statusMap = {
            'APPROVED': '<span class="badge bg-success">通过</span>',
            'REJECT': '<span class="badge bg-danger">驳回</span>',
            'PENDING': '<span class="badge bg-warning">待审核</span>',
            'DRAFT': '<span class="badge bg-secondary">草稿</span>',
            'CANCELLED': '<span class="badge bg-dark">已取消</span>'
        };
        return statusMap[status] || `<span class="badge bg-light text-dark">${status || '未知'}</span>`;
    },

    // 格式化日期时间
    formatDateTime(dateTimeStr) {
        if (!dateTimeStr) return '-';
        try {
            // 处理不同的日期格式
            let date;
            if (dateTimeStr.includes('T')) {
                // ISO格式：2025-07-15T11:41:05
                date = new Date(dateTimeStr);
            } else if (dateTimeStr.includes(' ')) {
                // 中文格式：2025-07-15 11:41:05
                date = new Date(dateTimeStr.replace(' ', 'T'));
            } else {
                date = new Date(dateTimeStr);
            }

            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        } catch (error) {
            console.warn('日期格式化失败:', dateTimeStr, error);
            return dateTimeStr;
        }
    },

    // HTML转义函数，防止XSS攻击
    escapeHtml(text) {
        if (typeof text !== 'string') return text;
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, function(m) { return map[m]; });
    },

    // 显示重试按钮
    showRetryButton(sku, productId) {
        const containers = ['basic-info-container', 'price-info-container', 'manufacturer-info-container', 'auxiliary-info-container', 'approval-history-container'];

        containers.forEach(containerId => {
            const container = document.getElementById(containerId);
            if (container) {
                const existingRetry = container.querySelector('.retry-button');
                if (!existingRetry) {
                    const retryButton = document.createElement('div');
                    retryButton.className = 'retry-button text-center mt-3';
                    retryButton.innerHTML = `
                        <button class="btn btn-outline-primary btn-sm" onclick="ProductDetailModule.retryLoadData('${sku}', '${productId}')">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-sm me-1" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4"></path>
                                <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4"></path>
                            </svg>
                            重新加载
                        </button>
                    `;
                    container.appendChild(retryButton);
                }
            }
        });
    },

    // 重试加载数据
    retryLoadData(sku, productId) {
        // 移除重试按钮
        document.querySelectorAll('.retry-button').forEach(btn => btn.remove());

        // 重新加载数据
        this.loadDetailData(sku, productId);
    },

    // 显示空状态
    showEmptyState(type, message = '暂无数据') {
        this.hideLoading(type);

        const content = document.getElementById(`${type}-content`);
        if (content) {
            content.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-xl text-muted" width="48" height="48" viewBox="0 0 24 24" stroke-width="1" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M14 3v4a1 1 0 0 0 1 1h4"></path>
                            <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"></path>
                        </svg>
                    </div>
                    <p class="empty-title">${message}</p>
                </div>
            `;
            content.style.display = 'block';
        }
    }
};

// 页面加载完成后初始化模块
document.addEventListener('DOMContentLoaded', function() {
    ProductDetailModule.init();
});
