{% extends "base.html" %}

{% block title %}{{ app_name }} - 友商数据采集{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="/static/css/scraper/scraper.css?ver={{ app_version }}">
{% endblock %}

{% block content %}
<div class="page-header d-print-none">
    <div class="container-xl">
        <div class="row g-2 align-items-center">
            <div class="col">
                <!-- 面包屑导航 -->
                <div class="page-pretitle">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="/">控制台</a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">友商数据采集</li>
                        </ol>
                    </nav>
                </div>
                <h2 class="page-title">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M4 4m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"></path>
                        <path d="M14 4m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"></path>
                        <path d="M4 14m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"></path>
                        <path d="M14 17h6"></path>
                        <path d="M17 14v6"></path>
                    </svg>
                    友商数据采集
                </h2>
                <div class="text-muted mt-1">
                    采集和分析友商平台数据
                </div>
            </div>
            <div class="col-auto ms-auto d-print-none">
                <div class="btn-list">
                    <a href="/" class="btn btn-outline-secondary">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M5 12l14 0"></path>
                            <path d="M5 12l6 6"></path>
                            <path d="M5 12l6 -6"></path>
                        </svg>
                        返回控制台
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="page-body">
    <div class="container-xl">
        <!-- 主要内容区域 - 左右布局 -->
        <div class="row scraper-layout">
            <!-- 左侧面板 - 平台选择和查询输入 -->
            <div class="col-md-3">
                <!-- 友商数据采集查询条件卡片 -->
                <div class="card query-card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"></path>
                                <path d="M21 21l-6 -6"></path>
                            </svg>
                            友商数据采集查询条件
                        </h5>
                        <div class="card-actions">
                            <button class="btn btn-sm btn-outline-secondary" id="clear-query-btn" onclick="clearScraperQueryCondition()">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M4 7l16 0"></path>
                                    <path d="M10 11l0 6"></path>
                                    <path d="M14 11l0 6"></path>
                                    <path d="M5 7l1 -4l4 0l1 4"></path>
                                    <path d="M9 7l6 0"></path>
                                </svg>
                                清空条件
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <form id="scraper-form">
                            <!-- 平台选择器（固定下拉菜单样式） -->
                            <div class="mb-3">
                                <label class="form-label">选择平台</label>

                                <!-- 统一下拉菜单 -->
                                <div class="platform-selector" id="platform-selector">
                                    <div class="dropdown">
                                        <button class="btn btn-outline-primary dropdown-toggle w-100 platform-dropdown-btn" type="button"
                                                id="platform-dropdown-btn" data-bs-toggle="dropdown" aria-expanded="false">
                                            <span id="platform-dropdown-text">当前：晨光</span>
                                        </button>
                                        <ul class="dropdown-menu w-100" aria-labelledby="platform-dropdown-btn">
                                            <li>
                                                <a class="dropdown-item platform-dropdown-item active" href="#" data-platform="colipu">
                                                    <i class="ti ti-circle-filled text-primary me-2"></i>
                                                    晨光科力普 
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item platform-dropdown-item" href="#" data-platform="officemate">
                                                    <i class="ti ti-circle text-muted me-2"></i>
                                                    欧菲斯
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item platform-dropdown-item" href="#" data-platform="comix">
                                                    <i class="ti ti-circle text-muted me-2"></i>
                                                    齐心
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item platform-dropdown-item" href="#" data-platform="xfs">
                                                    <i class="ti ti-circle text-muted me-2"></i>
                                                    鑫方盛
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item platform-dropdown-item" href="#" data-platform="lxwl">
                                                    <i class="ti ti-circle text-muted me-2"></i>
                                                    领先未来
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item platform-dropdown-item" href="#" data-platform="lxwl-new">
                                                    <i class="ti ti-circle text-muted me-2"></i>
                                                    领先未来(新)
                                                    <span class="badge bg-success ms-2">API</span>
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item platform-dropdown-item" href="#" data-platform="xhgj">
                                                    <i class="ti ti-circle text-muted me-2"></i>
                                                    咸亨
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>

                                <div class="form-text">
                                    选择要采集数据的友商平台
                                </div>
                            </div>


                            <!-- 查询内容输入 -->
                            <div class="mb-3">
                                <label class="form-label">SKU 列表</label>
                                <textarea
                                    class="form-control"
                                    id="query-content"
                                    name="query_content"
                                    placeholder="请输入查询内容，支持以下格式：&#10;1. 单个SKU：ABC123&#10;2. 多个SKU（每行一个）：&#10;   ABC123&#10;   DEF456&#10;   GHI789&#10;"
                                    rows="8"
                                    required
                                ></textarea>
                                <div class="form-text">
                                    批量查询时每行输入一个SKU。
                                </div>
                            </div>

                            <!-- SKU数量统计和验证信息 -->
                            <div class="mb-3">
                                <div id="sku-count-info" class="form-text d-flex align-items-center gap-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M9 11l3 3l8 -8"></path>
                                        <path d="M20 12v6a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2h9"></path>
                                    </svg>
                                    <span id="sku-count-text">请输入SKU列表</span>
                                </div>
                                <div id="sku-error-info" class="form-text text-danger d-none">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M12 9v2m0 4v.01"></path>
                                        <path d="M5 19h14a2 2 0 0 0 1.84 -2.75l-7.1 -12.25a2 2 0 0 0 -3.5 0l-7.1 12.25a2 2 0 0 0 1.75 2.75"></path>
                                    </svg>
                                    <span id="sku-error-text"></span>
                                </div>
                            </div>

                            <!-- 数据采集按钮 -->
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary" id="scraper-submit-btn" disabled>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"></path>
                                        <path d="M21 21l-6 -6"></path>
                                    </svg>
                                    <span class="spinner-border spinner-border-sm" style="display: none;" role="status" aria-hidden="true"></span>
                                    <span class="btn-text">采集晨光数据</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- 右侧面板 - 查询结果展示 -->
            <div class="col-md-8">
                <!-- 数据结果卡片 -->
                <div class="card results-card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M3 3m0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z"></path>
                                <path d="M7 7h10"></path>
                                <path d="M7 12h10"></path>
                                <path d="M7 17h10"></path>
                            </svg>
                            友商数据查询结果
                        </h5>
                        <div class="card-actions">
                            <div class="btn-list">
                                <!-- 数据操作按钮 -->
                                <button type="button" class="btn btn-sm btn-outline-primary" id="copy-data-btn" onclick="scraperManager.copyAllData()" disabled>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M8 8m0 2a2 2 0 0 1 2 -2h8a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-8a2 2 0 0 1 -2 -2z"></path>
                                        <path d="M16 8v-2a2 2 0 0 0 -2 -2h-8a2 2 0 0 0 -2 2v8a2 2 0 0 0 2 2h2"></path>
                                    </svg>
                                    复制数据
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-success" id="export-excel-btn" onclick="scraperManager.exportToExcel()" disabled>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M14 3v4a1 1 0 0 0 1 1h4"></path>
                                        <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"></path>
                                        <path d="M9 9l1 0"></path>
                                        <path d="M9 13l6 0"></path>
                                        <path d="M9 17l6 0"></path>
                                    </svg>
                                    导出Excel
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-info" id="export-csv-btn" onclick="scraperManager.exportToCSV()" disabled>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                        <path d="M14 3v4a1 1 0 0 0 1 1h4"></path>
                                        <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"></path>
                                        <path d="M9 9l1 0"></path>
                                        <path d="M9 13l6 0"></path>
                                        <path d="M9 17l6 0"></path>
                                    </svg>
                                    导出CSV
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div id="scraper-results"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="/static/js/button-manager.js?ver={{ app_version }}"></script>
<script src="/static/js/pagination-manager.js?ver={{ app_version }}"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script src="/static/js/scraper/scraper.js?ver={{ app_version }}"></script>
{% endblock %}
