/**
 * 友商数据采集模块JavaScript
 * 基于现有模块的设计模式，实现实时流式数据更新功能
 */

class ScraperManager {
    constructor() {
        this.currentPlatform = 'colipu'; // 默认选择晨光平台
        this.isLoading = false;
        this.currentData = [];
        this.streamResults = [];
        this.totalSkus = 0;
        this.completedSkus = 0;
        this.platformConfigs = {}; // 缓存平台配置信息

        // 分页管理器
        this.paginationManager = null;

        // 数据状态管理
        this.allData = []; // 存储所有数据的数组（包含占位数据）
        this.currentPage = 1; // 当前页码
        this.itemsPerPage = 10; // 每页显示的数据条数

        // 性能优化
        this.renderDebounceTimer = null; // 防抖计时器
        this.lastRenderTime = 0; // 上次渲染时间
        this.minRenderInterval = 100; // 最小渲染间隔（毫秒）

        this.init();
    }
    
    async init() {
        this.bindEvents();
        this.initPlatformSelector();
        this.initPaginationManager();

        // 初始化空结果显示
        this.displayResults([]);
        this.disableDataOperationButtons();

        // 加载保存的查询条件
        this.loadQueryConditionsFromStorage();

        // 加载当前平台配置
        await this.loadPlatformConfig(this.currentPlatform);
    }

    /**
     * 初始化分页管理器
     */
    initPaginationManager() {
        this.paginationManager = new PaginationManager({
            moduleId: 'scraper',
            pageSize: this.itemsPerPage,
            renderCallback: () => {
                this.renderCurrentPage();
            }
        });

        // 将分页管理器实例暴露到全局作用域，供分页控件使用
        window.paginationManager_scraper = this.paginationManager;
    }
    
    bindEvents() {
        // 下拉菜单平台切换事件
        document.querySelectorAll('.platform-dropdown-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                this.switchPlatform(item.dataset.platform);
            });
        });
        
        // 表单提交事件
        const form = document.getElementById('scraper-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleSubmit();
            });
        }
        
        // 查询内容输入框事件
        const textarea = document.getElementById('query-content');
        if (textarea) {
            textarea.addEventListener('input', () => {
                this.validateForm();
                // 实时保存查询内容到本地存储
                this.saveQueryConditionsToStorage({
                    queryContent: textarea.value.trim()
                });
            });
        }

        // 移除了Playwright模式开关事件（现在由配置文件控制）
    }
    
    initPlatformSelector() {
        // 从缓存加载上次选择的平台，如果没有缓存则使用默认平台
        const savedPlatform = this.loadSelectedPlatformFromStorage();
        const platformToUse = savedPlatform || this.currentPlatform;

        // 更新当前平台并切换
        this.currentPlatform = platformToUse;
        this.switchPlatform(platformToUse);
    }
    
    async switchPlatform(platform) {
        this.currentPlatform = platform;

        // 保存用户选择的平台到缓存
        this.saveSelectedPlatformToStorage(platform);

        // 更新下拉菜单状态
        this.updateDropdownPlatformState(platform);

        // 加载平台配置并更新平台信息显示
        await this.loadPlatformConfig(platform);
        this.updatePlatformInfo(platform);

        // 加载该平台的历史查询条件
        this.loadQueryConditionsFromStorage();

        // 清空结果
        this.displayResults([]);
        this.disableDataOperationButtons();

        console.log(`切换到平台: ${platform}`);
    }
    
    updatePlatformInfo(platform) {
        const platformNames = {
            'colipu': '晨光',
            'officemate': '欧菲斯',
            'comix': '齐心',
            'xfs': '鑫方盛',
            'lxwl': '领先未来',
            'lxwl-new': '领先未来(新)',
            'xhgj': '咸亨'
        };

        const platformName = platformNames[platform] || platform;

        // 更新页面标题或其他平台相关信息
        this.updateSubmitButtonText(platformName);
    }

    /**
     * 加载平台配置信息
     */
    async loadPlatformConfig(platform) {
        try {
            // 如果已经缓存了配置，直接返回
            if (this.platformConfigs[platform]) {
                return this.platformConfigs[platform];
            }

            const configEndpoints = {
                'colipu': '/api/v1/scraper/colipu/config',
                'officemate': '/api/v1/scraper/officemate/config',
                'comix': '/api/v1/scraper/comix/config',
                'xfs': '/api/v1/scraper/xfs/config',
                'lxwl': '/api/v1/scraper/lxwl/config',
                'lxwl-new': '/api/v1/scraper/lxwl-new/config',
                'xhgj': '/api/v1/scraper/xhgj/config'
            };

            const endpoint = configEndpoints[platform];
            if (!endpoint) {
                console.warn(`未知的平台类型: ${platform}`);
                return null;
            }

            const response = await fetch(endpoint, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            if (result.success) {
                // 缓存配置信息
                this.platformConfigs[platform] = result.data;
                console.log(`✅ 加载${result.data.platform_name}平台配置成功:`, result.data);
                return result.data;
            } else {
                throw new Error(result.message || '获取配置失败');
            }

        } catch (error) {
            console.error(`❌ 加载平台配置失败 (${platform}):`, error);
            // 返回默认配置
            return {
                platform: platform,
                platform_name: this.getPlatformName(platform),
                playwright_mode: false,
                mode_text: "HTTP直接模式"
            };
        }
    }

    /**
     * 获取平台中文名称
     */
    getPlatformName(platform) {
        const platformNames = {
            'colipu': '晨光',
            'officemate': '欧菲斯',
            'comix': '齐心',
            'xfs': '鑫方盛',
            'lxwl': '领先未来',
            'lxwl-new': '领先未来(新)',
            'xhgj': '咸亨'
        };
        return platformNames[platform] || '未知';
    }

    updateSubmitButtonText(platformName = null) {
        const submitBtn = document.getElementById('scraper-submit-btn');

        if (submitBtn) {
            const btnText = submitBtn.querySelector('.btn-text');
            if (btnText) {
                // 获取当前平台配置
                const platformConfig = this.platformConfigs[this.currentPlatform];

                if (!platformName) {
                    platformName = platformConfig ? platformConfig.platform_name : this.getPlatformName(this.currentPlatform);
                }

                // 根据配置显示模式标识
                let modeText = '';
                if (platformConfig && platformConfig.playwright_mode) {
                    modeText = '(Playwright)';
                } else if (platformConfig && platformConfig.api_mode) {
                    modeText = '(API)';
                }

                btnText.textContent = `采集${platformName}数据${modeText}`;
            }
        }
    }
    
    validateForm() {
        const textarea = document.getElementById('query-content');
        const submitBtn = document.getElementById('scraper-submit-btn');
        const skuCountInfo = document.getElementById('sku-count-info');
        const skuCountText = document.getElementById('sku-count-text');
        const skuErrorInfo = document.getElementById('sku-error-info');
        const skuErrorText = document.getElementById('sku-error-text');

        if (!textarea || !submitBtn) return;

        // 如果正在加载，跳过验证逻辑，保持禁用状态
        if (this.isLoading) {
            submitBtn.disabled = true;
            return;
        }

        const queryContent = textarea.value.trim();

        // 解析SKU数量
        const skuCount = this.countValidSkus(queryContent);
        const maxSkus = 50; // SKU数量限制

        // 更新SKU计数显示
        this.updateSkuCountDisplay(skuCount, maxSkus, skuCountInfo, skuCountText, skuErrorInfo, skuErrorText);

        // 验证表单
        const hasContent = queryContent.length > 0;
        const isSkuCountValid = skuCount > 0 && skuCount <= maxSkus;
        const isValid = hasContent && isSkuCountValid;

        // 只有在非加载状态下才根据验证结果设置按钮状态
        submitBtn.disabled = !isValid;
    }

    countValidSkus(queryContent) {
        // 统计有效的SKU数量
        if (!queryContent) return 0;

        // 按换行符分割并过滤空行
        const lines = queryContent.split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0);

        return lines.length;
    }

    updateSkuCountDisplay(skuCount, maxSkus, skuCountInfo, skuCountText, skuErrorInfo, skuErrorText) {
        // 更新SKU计数和错误信息显示
        if (!skuCountInfo || !skuCountText || !skuErrorInfo || !skuErrorText) return;

        if (skuCount === 0) {
            // 没有输入SKU
            skuCountInfo.classList.remove('d-none');
            skuErrorInfo.classList.add('d-none');
            skuCountText.textContent = '请输入SKU列表';
            skuCountInfo.className = 'form-text d-flex align-items-center gap-2';
        } else if (skuCount <= maxSkus) {
            // SKU数量在限制范围内
            skuCountInfo.classList.remove('d-none');
            skuErrorInfo.classList.add('d-none');
            skuCountText.textContent = `已输入 ${skuCount}/${maxSkus} 个SKU`;
            skuCountInfo.className = 'form-text d-flex align-items-center gap-2 text-success';
        } else {
            // SKU数量超出限制
            skuCountInfo.classList.add('d-none');
            skuErrorInfo.classList.remove('d-none');
            skuErrorText.textContent = `单次最多只能采集${maxSkus}个SKU，当前输入了${skuCount}个`;
        }
    }
    
    async handleSubmit() {
        if (this.isLoading) return;

        const textarea = document.getElementById('query-content');
        const queryContent = textarea.value.trim();

        if (!queryContent) {
            this.showError('请输入查询内容');
            return;
        }

        // 验证SKU数量限制
        const skuCount = this.countValidSkus(queryContent);
        const maxSkus = 50;

        if (skuCount === 0) {
            this.showError('未找到有效的SKU编码');
            return;
        }

        if (skuCount > maxSkus) {
            this.showError(`单次最多只能采集${maxSkus}个SKU，当前输入了${skuCount}个`);
            return;
        }

        try {
            this.setLoading(true);

            // 解析SKU列表
            const skus = this.parseQueryContent(queryContent);
            if (skus.length === 0) {
                this.showError('未找到有效的SKU编码');
                return;
            }

            console.log(`🔍 前端解析SKU列表 (${skus.length}个):`, skus);
            console.log(`📝 原始查询内容: "${queryContent}"`);

            // 预渲染SKU表格
            this.prerenderSkuTable(skus);

            // 使用流式数据采集
            await this.fetchDataStream(queryContent);

        } catch (error) {
            console.error('数据采集错误:', error);
            this.displayResults([]);
            this.disableDataOperationButtons();
            if (typeof authManager !== 'undefined') {
                authManager.showNotification('数据采集失败: ' + error.message, 'error');
            } else {
                this.showError('数据采集失败: ' + error.message);
            }

            // 记录查询统计（异常）
            if (typeof dashboardStats !== 'undefined') {
                dashboardStats.recordQuery('scraper', false);
            }
        } finally {
            this.setLoading(false);
        }
    }

    parseQueryContent(queryContent) {
        // 解析查询内容，提取SKU列表
        if (!queryContent) return [];

        // 支持多种分隔符：空格、逗号、换行符
        const skus = queryContent.split(/[,\s\n]+/)
            .map(sku => sku.trim())
            .filter(sku => sku.length > 0);

        // 去重但保持顺序
        const uniqueSkus = [];
        const seen = new Set();
        for (const sku of skus) {
            if (!seen.has(sku)) {
                seen.add(sku);
                uniqueSkus.push(sku);
            }
        }

        return uniqueSkus;
    }

    prerenderSkuTable(skus) {
        // 预渲染SKU表格，显示采集中状态
        console.log(`开始预渲染表格，共 ${skus.length} 个SKU`);

        // 清空所有数据并重置页码
        this.allData = [];
        this.currentPage = 1;

        // 创建预渲染数据 - 完整的9字段结构
        const prerenderData = skus.map((sku, index) => ({
            sku_number: sku,
            product_name: '',  // 空值，将通过遮罩显示
            product_ventor: '',
            url: '',
            product_unit: '',
            product_model: '',
            Null1: '-',
            Null2: '-',
            product_price: '',
            status: 'loading',
            index: index,
            original_order: index,
            original: sku // 用于后续匹配后端返回的数据
        }));

        // 初始化数据数组
        this.allData = prerenderData;
        this.streamResults = new Array(skus.length).fill(null);
        this.totalSkus = skus.length;
        this.completedSkus = 0;

        // 设置分页管理器数据
        this.paginationManager.setData(this.allData, this.allData);

        // 渲染第一页数据
        this.renderCurrentPage();

        console.log(`预渲染表格完成，共 ${skus.length} 个SKU`);
    }

    /**
     * 渲染当前页数据（带性能优化）
     */
    renderCurrentPage() {
        // 使用防抖优化，避免频繁渲染
        this.debouncedRender();
    }

    /**
     * 防抖渲染方法
     */
    debouncedRender() {
        // 清除之前的计时器
        if (this.renderDebounceTimer) {
            clearTimeout(this.renderDebounceTimer);
        }

        // 设置新的计时器
        this.renderDebounceTimer = setTimeout(() => {
            this.performRender();
        }, 50); // 50ms防抖延迟
    }

    /**
     * 执行实际渲染
     */
    performRender() {
        const now = Date.now();

        // 检查渲染间隔，避免过于频繁的渲染
        if (now - this.lastRenderTime < this.minRenderInterval) {
            // 如果距离上次渲染时间太短，延迟渲染
            setTimeout(() => this.performRender(), this.minRenderInterval - (now - this.lastRenderTime));
            return;
        }

        const resultsContainer = document.getElementById('scraper-results');
        if (!resultsContainer) {
            console.error('未找到结果容器');
            return;
        }

        // 获取当前页数据
        const currentPageData = this.paginationManager.getCurrentPageData();

        // 使用DocumentFragment优化DOM操作
        const fragment = document.createDocumentFragment();
        const tempDiv = document.createElement('div');

        // 生成表格HTML
        const tableHTML = this.generateTableHTML(currentPageData);

        // 生成分页控件HTML
        const paginationHTML = this.paginationManager.generatePaginationHTML();

        // 组合完整HTML
        tempDiv.innerHTML = tableHTML + paginationHTML;

        // 将内容移动到fragment
        while (tempDiv.firstChild) {
            fragment.appendChild(tempDiv.firstChild);
        }

        // 一次性更新DOM
        resultsContainer.innerHTML = '';
        resultsContainer.appendChild(fragment);

        // 更新最后渲染时间
        this.lastRenderTime = now;

        console.log(`渲染当前页完成，显示 ${currentPageData.length} 条数据`);
    }

    /**
     * 生成表格HTML（性能优化版本）
     */
    generateTableHTML(data) {
        if (!data || data.length === 0) {
            return `
                <div class="empty">
                    <div class="empty-img"><img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDEyOCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik02NCA5NkM4MC41Njk0IDk2IDk0IDgyLjU2OTQgOTQgNjZDOTQgNDkuNDMwNiA4MC41Njk0IDM2IDY0IDM2QzQ3LjQzMDYgMzYgMzQgNDkuNDMwNiAzNCA2NkMzNCA4Mi41Njk0IDQ3LjQzMDYgOTYgNjQgOTZaIiBzdHJva2U9IiNEREREREQiIHN0cm9rZS13aWR0aD0iMiIvPgo8cGF0aCBkPSJNNjQgNzJWNjAiIHN0cm9rZT0iI0RERERERCIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPHA+PC9wYXRoPgo8L3N2Zz4K" alt=""></div>
                    <p class="empty-title">暂无数据</p>
                    <p class="empty-subtitle text-muted">请输入SKU列表并点击采集按钮开始数据采集</p>
                </div>
            `;
        }

        // 使用数组拼接优化性能
        const htmlParts = [
            '<div class="table-responsive">',
            '<table class="table table-vcenter" id="scraper-table">',
            '<thead>',
            '<tr>',
            '<th>SKU编码</th>',
            '<th>商品名称</th>',
            '<th>品牌</th>',
            '<th>计量单位</th>',
            '<th>规格参数</th>',
            '<th>官网价</th>',
            '<th width="80">操作</th>',
            '</tr>',
            '</thead>',
            '<tbody>'
        ];

        // 批量生成行HTML
        const rowsHTML = data.map(item => this.generateRowHTML(item));
        htmlParts.push(...rowsHTML);

        htmlParts.push(
            '</tbody>',
            '</table>',
            '</div>'
        );

        return htmlParts.join('');
    }

    /**
     * 生成单行HTML（优化版本）
     */
    generateRowHTML(item) {
        const isError = item.status === 'error';
        const isLoading = item.status === 'loading';
        const originalIndex = item.original_order !== undefined ? item.original_order : item.index;
        const rowClass = isError ? 'error-row' : (isLoading ? 'loading-row' : 'success-row');

        // 预计算所有单元格内容
        const cells = [
            `<code>${this.escapeHtml(item.sku_number)}</code>`,
            this.renderCellContent(item, 'product_name', isLoading),
            this.renderCellContent(item, 'product_ventor', isLoading),
            this.renderCellContent(item, 'product_unit', isLoading),
            this.renderCellContent(item, 'product_model', isLoading),
            this.renderCellContent(item, 'product_price', isLoading),
            this.renderActionCell(item, originalIndex, isError, isLoading)
        ];

        const cellsHTML = cells.map((content, index) => {
            const fieldNames = ['sku', 'product_name', 'product_ventor', 'product_unit', 'product_model', 'product_price', 'action'];
            const className = index === 0 ? 'sku-cell' : '';
            const dataField = index > 0 ? ` data-field="${fieldNames[index]}"` : '';
            return `<td class="${className}"${dataField}>${content}</td>`;
        }).join('');

        return `<tr data-row-index="${originalIndex}" data-sku="${item.sku_number}" id="row-${originalIndex}" class="${rowClass}">${cellsHTML}</tr>`;
    }

    /**
     * 渲染单元格内容
     */
    renderCellContent(item, fieldName, isLoading) {
        if (isLoading) {
            return `
                <div class="loading-placeholder">
                    <i class="ti ti-loader-2 loading-spinner"></i>
                    ${fieldName === 'product_name' ? '<span class="loading-text">加载中...</span>' : ''}
                </div>
            `;
        }

        const isError = item.status === 'error';

        switch (fieldName) {
            case 'product_name':
                if (isError) {
                    return '<span class="text-danger">获取失败</span>';
                } else {
                    const displayName = item.product_name || '';
                    return `
                        <div class="text-truncate" style="max-width: 200px;" title="${this.escapeHtml(displayName)}">
                            ${this.escapeHtml(displayName)}
                        </div>
                    `;
                }

            case 'product_ventor':
                if (isError) {
                    return '<span class="text-danger">获取失败</span>';
                } else {
                    const displayVentor = item.product_ventor || '晨光';
                    return `
                        <div class="table-cell-ellipsis" style="max-width: 120px;" title="${this.escapeHtml(displayVentor)}">
                            <span class="badge bg-primary-lt">${this.escapeHtml(displayVentor)}</span>
                        </div>
                    `;
                }

            case 'product_unit':
                if (isError) {
                    return '<span class="text-danger">获取失败</span>';
                } else {
                    const displayUnit = item.product_unit || '个';
                    return `<span class="badge bg-secondary-lt">${this.escapeHtml(displayUnit)}</span>`;
                }

            case 'product_model':
                if (isError) {
                    return '<span class="text-danger">获取失败</span>';
                } else {
                    const displayModel = item.product_model || '-';
                    return `
                        <div class="text-truncate" style="max-width: 150px;" title="${this.escapeHtml(displayModel)}">
                            ${this.escapeHtml(displayModel)}
                        </div>
                    `;
                }

            case 'product_price':
                if (isError) {
                    return '<span class="text-danger">获取失败</span>';
                } else {
                    const displayPrice = item.product_price || '暂无价格信息';
                    if (displayPrice.includes('¥')) {
                        return `<span class="text-success fw-bold">${this.escapeHtml(displayPrice)}</span>`;
                    } else {
                        return `<span class="text-muted">${this.escapeHtml(displayPrice)}</span>`;
                    }
                }

            default:
                return '';
        }
    }

    /**
     * 渲染操作单元格
     */
    renderActionCell(item, originalIndex, isError, isLoading) {
        if (isLoading) {
            return `
                <div class="loading-placeholder">
                    <span class="text-muted">-</span>
                </div>
            `;
        }

        if (isError) {
            return `
                <button type="button"
                        class="btn btn-sm btn-outline-primary btn-icon"
                        title="重试采集"
                        onclick="event.stopPropagation(); scraperManager.retrySingleSku('${this.escapeHtml(item.sku_number)}', ${originalIndex})"
                        id="retry-btn-${originalIndex}">
                    <i class="ti ti-refresh"></i>
                </button>
            `;
        }

        return '';
    }

    async fetchDataStream(queryContent) {
        const apiEndpoints = {
            'colipu': '/api/v1/scraper/colipu/stream',
            'officemate': '/api/v1/scraper/officemate/stream',
            'comix': '/api/v1/scraper/comix/stream',
            'xfs': '/api/v1/scraper/xfs/stream',
            'lxwl': '/api/v1/scraper/lxwl/stream',
            'lxwl-new': '/api/v1/scraper/lxwl-new/stream',
            'xhgj': '/api/v1/scraper/xhgj/stream'
        };

        const endpoint = apiEndpoints[this.currentPlatform];
        if (!endpoint) {
            throw new Error('未知的平台类型');
        }

        // 注意：不要在这里清空结果，因为预渲染表格已经显示
        // 流式结果数组已在prerenderSkuTable中初始化

        const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                query_content: queryContent
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = ''; // 用于缓存不完整的数据行
        let retryCount = 0;
        const maxRetries = 3;

        // 重置统计信息
        this.streamStats = {
            totalChunks: 0,
            totalLines: 0,
            successfulParses: 0,
            failedParses: 0,
            retries: 0,
            startTime: Date.now(),
            endTime: null
        };

        if (this.debugMode) {
            console.log('🔍 开始流数据采集，调试模式已启用');
        }

        try {
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                // 解码当前块并添加到缓冲区
                const chunk = decoder.decode(value, { stream: true });
                buffer += chunk;

                // 按行分割，保留最后一个可能不完整的行
                const lines = buffer.split('\n');
                buffer = lines.pop() || ''; // 保留最后一个可能不完整的行

                // 处理完整的行
                for (const line of lines) {
                    if (line.trim() && line.startsWith('data: ')) {
                        await this.processStreamLine(line, retryCount, maxRetries);
                    }
                }
            }

            // 处理缓冲区中剩余的数据
            if (buffer.trim() && buffer.startsWith('data: ')) {
                await this.processStreamLine(buffer, retryCount, maxRetries);
            }

        } catch (error) {
            console.error('❌ 流数据处理发生严重错误:', {
                error: error.message,
                stack: error.stack,
                platform: this.currentPlatform,
                endpoint: endpoint
            });

            // 恢复UI状态
            this.setLoading(false);
            this.enableDataOperationButtons();

            // 显示用户友好的错误信息
            const userMessage = this.getUserFriendlyErrorMessage(error);
            if (typeof authManager !== 'undefined') {
                authManager.showNotification(userMessage, 'error');
            } else {
                this.showError(userMessage);
            }

            // 记录查询统计（失败）
            if (typeof dashboardStats !== 'undefined') {
                dashboardStats.recordQuery('scraper', false);
            }

            throw error;
        } finally {
            reader.releaseLock();
        }
    }

    /**
     * 处理单行流数据，包含错误处理和重试机制
     * @param {string} line - 数据行
     * @param {number} retryCount - 当前重试次数
     * @param {number} maxRetries - 最大重试次数
     */
    async processStreamLine(line, retryCount, maxRetries) {
        try {
            // 提取 JSON 数据部分
            const jsonStr = line.slice(6); // 移除 'data: ' 前缀

            // 验证 JSON 字符串的基本完整性
            if (!this.isValidJsonString(jsonStr)) {
                console.warn('检测到不完整的 JSON 数据，跳过处理:', jsonStr.substring(0, 100) + '...');
                return;
            }

            // 尝试解析 JSON
            const data = JSON.parse(jsonStr);

            // 验证解析后的数据结构
            if (!this.isValidStreamData(data)) {
                console.warn('无效的流数据结构:', data);
                return;
            }

            await this.handleStreamData(data);

        } catch (error) {
            console.error(`解析流数据失败 (尝试 ${retryCount + 1}/${maxRetries + 1}):`, {
                error: error.message,
                line: line.substring(0, 200) + (line.length > 200 ? '...' : ''),
                position: error.message.match(/position (\d+)/)?.[1] || 'unknown'
            });

            // 如果是 JSON 解析错误且还有重试机会，可以尝试数据清理
            if (error instanceof SyntaxError && retryCount < maxRetries) {
                const cleanedLine = this.cleanJsonString(line);
                if (cleanedLine !== line) {
                    console.log('尝试使用清理后的数据重新解析...');
                    await this.processStreamLine(cleanedLine, retryCount + 1, maxRetries);
                }
            }
        }
    }

    /**
     * 验证 JSON 字符串的基本完整性
     * @param {string} jsonStr - JSON 字符串
     * @returns {boolean} 是否为有效的 JSON 字符串
     */
    isValidJsonString(jsonStr) {
        if (!jsonStr || typeof jsonStr !== 'string') {
            return false;
        }

        const trimmed = jsonStr.trim();

        // 检查是否以 { 开始并以 } 结束
        if (!trimmed.startsWith('{') || !trimmed.endsWith('}')) {
            return false;
        }

        // 简单的括号匹配检查
        let braceCount = 0;
        let inString = false;
        let escaped = false;

        for (let i = 0; i < trimmed.length; i++) {
            const char = trimmed[i];

            if (escaped) {
                escaped = false;
                continue;
            }

            if (char === '\\') {
                escaped = true;
                continue;
            }

            if (char === '"') {
                inString = !inString;
                continue;
            }

            if (!inString) {
                if (char === '{') {
                    braceCount++;
                } else if (char === '}') {
                    braceCount--;
                }
            }
        }

        return braceCount === 0 && !inString;
    }

    /**
     * 验证流数据结构的有效性
     * @param {object} data - 解析后的数据对象
     * @returns {boolean} 是否为有效的流数据
     */
    isValidStreamData(data) {
        if (!data || typeof data !== 'object') {
            return false;
        }

        // 检查必需的字段
        if (!data.type || typeof data.type !== 'string') {
            return false;
        }

        // 根据不同类型验证特定字段
        switch (data.type) {
            case 'init':
                return typeof data.total === 'number';
            case 'progress':
                return typeof data.message === 'string';
            case 'result':
                return typeof data.index === 'number' &&
                       data.data &&
                       typeof data.data === 'object' &&
                       typeof data.current === 'number' &&
                       typeof data.total === 'number';
            case 'complete':
                return true;
            case 'error':
                return typeof data.message === 'string';
            default:
                console.warn('未知的流数据类型:', data.type);
                return false;
        }
    }

    /**
     * 清理 JSON 字符串中的常见问题
     * @param {string} line - 原始数据行
     * @returns {string} 清理后的数据行
     */
    cleanJsonString(line) {
        if (!line.startsWith('data: ')) {
            return line;
        }

        let jsonStr = line.slice(6);

        // 移除可能的控制字符
        jsonStr = jsonStr.replace(/[\x00-\x1F\x7F]/g, '');

        // 修复常见的转义问题
        jsonStr = jsonStr.replace(/\\"/g, '"').replace(/\\'/g, "'");

        // 尝试修复未闭合的字符串（简单情况）
        const lastQuoteIndex = jsonStr.lastIndexOf('"');
        const lastBraceIndex = jsonStr.lastIndexOf('}');

        if (lastQuoteIndex > lastBraceIndex) {
            // 可能存在未闭合的字符串，尝试添加闭合引号
            jsonStr = jsonStr.substring(0, lastQuoteIndex + 1) + '}';
        }

        return 'data: ' + jsonStr;
    }

    async handleStreamData(data) {
        try {
            switch (data.type) {
                case 'init':
                    // 初始化信息已在预渲染时处理，这里不需要额外操作
                    console.log(`🚀 开始流式采集 ${data.total} 个SKU`);
                    break;

                case 'progress':
                    // 不在按钮上显示进度信息
                    console.log(`📈 ${data.message}`);
                    break;

                case 'result':
                    // 验证结果数据的完整性
                    if (!this.validateResultData(data)) {
                        console.warn('收到无效的结果数据:', data);
                        return;
                    }

                    // 将结果放入正确的位置（保持输入顺序）
                    this.streamResults[data.index] = data.data;
                    this.completedSkus = data.current;

                    console.log(`🔄 收到流式数据结果 - 索引: ${data.index}, SKU: ${data.data.sku_number}, 状态: ${data.data.status}`);

                    // 更新allData数组中对应的数据
                    try {
                        this.updateDataInAllData(data.index, data.data);
                        console.log(`📊 SKU ${data.data.sku_number} 采集完成 (${data.current}/${data.total})`);
                    } catch (updateError) {
                        console.error('更新数据时发生错误:', updateError);
                        // 继续处理，不中断整个流程
                    }
                    break;

                case 'complete':
                    // 恢复UI状态：停止加载状态
                    this.setLoading(false);

                    // 启用数据操作按钮
                    this.enableDataOperationButtons();

                    console.log('✅ 流式数据采集完成，所有表格行已实时更新');

                    // 显示完成通知
                    if (typeof authManager !== 'undefined') {
                        authManager.showNotification(data.message || '数据采集完成', 'success');
                    }

                    // 记录查询统计
                    if (typeof dashboardStats !== 'undefined') {
                        dashboardStats.recordQuery('scraper', true);
                    }
                    break;

                case 'error':
                    // 恢复UI状态：停止加载状态
                    this.setLoading(false);

                    const errorMessage = data.message || '数据采集过程中发生未知错误';
                    console.error('❌ 流数据错误:', errorMessage);

                    this.showError(errorMessage);
                    if (typeof authManager !== 'undefined') {
                        authManager.showNotification(errorMessage, 'error');
                    }

                    // 记录查询统计（失败）
                    if (typeof dashboardStats !== 'undefined') {
                        dashboardStats.recordQuery('scraper', false);
                    }
                    break;

                default:
                    console.warn('⚠️ 收到未知类型的流数据:', data.type, data);
                    break;
            }
        } catch (error) {
            console.error('处理流数据时发生错误:', error);
            // 不重新抛出错误，避免中断整个流程
        }
    }

    /**
     * 验证结果数据的完整性
     * @param {object} data - 流数据对象
     * @returns {boolean} 数据是否有效
     */
    validateResultData(data) {
        if (!data || typeof data !== 'object') {
            return false;
        }

        // 检查必需字段
        if (typeof data.index !== 'number' || data.index < 0) {
            console.warn('无效的索引值:', data.index);
            return false;
        }

        if (typeof data.current !== 'number' || typeof data.total !== 'number') {
            console.warn('无效的进度数据:', { current: data.current, total: data.total });
            return false;
        }

        if (!data.data || typeof data.data !== 'object') {
            console.warn('缺少结果数据对象');
            return false;
        }

        // 检查结果数据的基本字段
        if (!data.data.sku_number || typeof data.data.sku_number !== 'string') {
            console.warn('无效的SKU编号:', data.data.sku_number);
            return false;
        }

        if (!data.data.status || typeof data.data.status !== 'string') {
            console.warn('无效的状态信息:', data.data.status);
            return false;
        }

        return true;
    }

    /**
     * 将技术错误转换为用户友好的错误消息
     * @param {Error} error - 错误对象
     * @returns {string} 用户友好的错误消息
     */
    getUserFriendlyErrorMessage(error) {
        if (!error) {
            return '发生未知错误，请稍后重试';
        }

        const message = error.message || '';

        // JSON 解析错误
        if (error instanceof SyntaxError && message.includes('JSON')) {
            return '数据格式错误，可能是网络传输问题导致的数据损坏，请重试';
        }

        // 网络错误
        if (message.includes('fetch') || message.includes('network') || message.includes('Failed to fetch')) {
            return '网络连接错误，请检查网络连接后重试';
        }

        // 超时错误
        if (message.includes('timeout') || message.includes('超时')) {
            return '请求超时，服务器响应较慢，请稍后重试';
        }

        // HTTP 状态错误
        if (message.includes('HTTP')) {
            const statusMatch = message.match(/HTTP (\d+)/);
            if (statusMatch) {
                const status = parseInt(statusMatch[1]);
                switch (status) {
                    case 400:
                        return '请求参数错误，请检查输入的SKU格式';
                    case 401:
                        return '身份验证失败，请重新登录';
                    case 403:
                        return '权限不足，无法访问该功能';
                    case 404:
                        return '服务不可用，请联系管理员';
                    case 429:
                        return '请求过于频繁，请稍后重试';
                    case 500:
                        return '服务器内部错误，请稍后重试';
                    case 502:
                    case 503:
                    case 504:
                        return '服务暂时不可用，请稍后重试';
                    default:
                        return `服务器错误 (${status})，请稍后重试`;
                }
            }
        }

        // 数据相关错误
        if (message.includes('Unterminated string') || message.includes('未终止的字符串')) {
            return '数据传输不完整，请重试。如果问题持续存在，请联系技术支持';
        }

        if (message.includes('Unexpected token') || message.includes('意外的标记')) {
            return '数据格式异常，可能是服务器返回了非预期的内容，请重试';
        }

        // 默认错误消息
        return `数据采集失败: ${message.substring(0, 100)}${message.length > 100 ? '...' : ''}`;
    }

    /**
     * 更新allData数组中的数据并重新渲染当前页
     */
    updateDataInAllData(index, newData) {
        // 查找并更新allData中对应的数据
        const dataIndex = this.allData.findIndex(item => item.original === newData.sku_number);
        if (dataIndex !== -1) {
            // 更新数据，保留原有的索引信息
            this.allData[dataIndex] = {
                ...this.allData[dataIndex],
                ...newData,
                index: index,
                original_order: index
            };

            // 更新分页管理器的数据
            this.paginationManager.setData(this.allData, this.allData);

            // 检查更新的项是否在当前页，如果是则重新渲染当前页
            const currentPageData = this.paginationManager.getCurrentPageData();
            const isInCurrentPage = currentPageData.some(item => item.original === newData.sku_number);

            if (isInCurrentPage) {
                this.renderCurrentPage();
            }

            console.log(`✅ SKU ${newData.sku_number} 数据已更新并重新渲染`);
        } else {
            console.warn(`⚠️ 未找到SKU ${newData.sku_number} 对应的数据项`);
        }
    }

    // 移除旧的单行更新方法，现在使用整页重新渲染的方式

    // 移除旧的单元格更新方法，现在使用整页重新渲染的方式

    // 移除旧的流式显示更新方法，现在使用单行更新
    // updateStreamDisplay() 方法已被 updateSingleTableRow() 替代

    // 移除按钮进度显示功能
    // updateProgressMessage() 方法已移除，不再在按钮上显示进度

    async fetchData(queryContent) {
        const apiEndpoints = {
            'colipu': '/api/v1/scraper/colipu',
            'officemate': '/api/v1/scraper/officemate',
            'comix': '/api/v1/scraper/comix',
            'xfs': '/api/v1/scraper/xfs',
            'lxwl': '/api/v1/scraper/lxwl',
            'lxwl-new': '/api/v1/scraper/lxwl-new',
            'xhgj': '/api/v1/scraper/xhgj'
        };

        const endpoint = apiEndpoints[this.currentPlatform];
        if (!endpoint) {
            throw new Error('未知的平台类型');
        }

        const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                query_content: queryContent
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
    }
    
    setLoading(loading) {
        this.isLoading = loading;

        // 1. 控制采集按钮状态
        this.setSubmitButtonState(loading);

        // 2. 控制SKU输入框状态
        this.setQueryInputState(loading);

        // 3. 控制平台选择器状态
        this.setPlatformSelectorState(loading);

        // 4. 控制清空按钮状态
        this.setClearButtonState(loading);

        // 5. 重新验证表单状态
        this.validateForm();
    }

    /**
     * 设置采集按钮状态
     */
    setSubmitButtonState(loading) {
        const submitBtn = document.getElementById('scraper-submit-btn');
        const spinner = submitBtn?.querySelector('.spinner-border');
        const btnText = submitBtn?.querySelector('.btn-text');

        if (submitBtn) {
            submitBtn.disabled = loading;

            // 控制加载图标显示
            if (spinner) {
                spinner.style.display = loading ? 'inline-block' : 'none';
            }

            // 控制按钮文本
            if (btnText) {
                if (loading) {
                    btnText.textContent = '采集中...';
                } else {
                    this.updateSubmitButtonText();
                }
            }

            // 添加视觉状态类
            if (loading) {
                submitBtn.classList.add('btn-loading');
            } else {
                submitBtn.classList.remove('btn-loading');
            }
        }
    }

    /**
     * 设置SKU输入框状态
     */
    setQueryInputState(loading) {
        const queryInput = document.getElementById('query-content');

        if (queryInput) {
            queryInput.disabled = loading;
            queryInput.readOnly = loading;

            // 添加视觉状态类
            if (loading) {
                queryInput.classList.add('form-control-disabled');
                queryInput.style.backgroundColor = '#f8f9fa';
                queryInput.style.cursor = 'not-allowed';
            } else {
                queryInput.classList.remove('form-control-disabled');
                queryInput.style.backgroundColor = '';
                queryInput.style.cursor = '';
            }
        }
    }

    /**
     * 设置平台选择器状态
     */
    setPlatformSelectorState(loading) {
        // 禁用平台下拉菜单按钮
        const platformDropdownBtn = document.querySelector('.platform-dropdown-btn');
        if (platformDropdownBtn) {
            platformDropdownBtn.disabled = loading;

            if (loading) {
                platformDropdownBtn.classList.add('disabled');
                platformDropdownBtn.style.pointerEvents = 'none';
                platformDropdownBtn.style.opacity = '0.6';
            } else {
                platformDropdownBtn.classList.remove('disabled');
                platformDropdownBtn.style.pointerEvents = '';
                platformDropdownBtn.style.opacity = '';
            }
        }

        // 禁用所有平台选择项
        const platformItems = document.querySelectorAll('.platform-dropdown-item');
        platformItems.forEach(item => {
            if (loading) {
                item.style.pointerEvents = 'none';
                item.style.opacity = '0.6';
                item.classList.add('disabled');
            } else {
                item.style.pointerEvents = '';
                item.style.opacity = '';
                item.classList.remove('disabled');
            }
        });
    }

    /**
     * 设置清空按钮状态
     */
    setClearButtonState(loading) {
        const clearBtn = document.getElementById('clear-query-btn');

        if (clearBtn) {
            clearBtn.disabled = loading;

            if (loading) {
                clearBtn.classList.add('disabled');
                clearBtn.style.pointerEvents = 'none';
                clearBtn.style.opacity = '0.6';
            } else {
                clearBtn.classList.remove('disabled');
                clearBtn.style.pointerEvents = '';
                clearBtn.style.opacity = '';
            }
        }
    }

    
    // 启用数据操作按钮
    enableDataOperationButtons() {
        const copyBtn = document.getElementById('copy-data-btn');
        const excelBtn = document.getElementById('export-excel-btn');
        const csvBtn = document.getElementById('export-csv-btn');

        if (copyBtn) copyBtn.disabled = false;
        if (excelBtn) excelBtn.disabled = false;
        if (csvBtn) csvBtn.disabled = false;
    }

    // 禁用数据操作按钮
    disableDataOperationButtons() {
        const copyBtn = document.getElementById('copy-data-btn');
        const excelBtn = document.getElementById('export-excel-btn');
        const csvBtn = document.getElementById('export-csv-btn');

        if (copyBtn) copyBtn.disabled = true;
        if (excelBtn) excelBtn.disabled = true;
        if (csvBtn) csvBtn.disabled = true;
    }

    // 显示结果数据
    displayResults(data) {
        // 更新allData数组
        this.allData = data || [];

        // 设置分页管理器数据
        if (this.paginationManager) {
            this.paginationManager.setData(this.allData, this.allData);
            // 渲染当前页
            this.renderCurrentPage();
        } else {
            // 如果分页管理器未初始化，直接渲染
            const resultsContainer = document.getElementById('scraper-results');
            if (resultsContainer) {
                const tableHTML = this.generateTableHTML(data);
                resultsContainer.innerHTML = tableHTML;
            }
        }

        // 获取有效数据
        const validResults = data.filter(r => r !== null);

        // 如果有数据，启用操作按钮
        if (validResults.length > 0) {
            this.enableDataOperationButtons();
        } else {
            this.disableDataOperationButtons();
        }
    }

    // 移除旧的renderTable方法，现在使用generateTableHTML方法

    // 复制所有数据（统一标准格式）
    copyAllData() {
        try {
            // 使用分页管理器获取所有数据
            const allData = this.paginationManager ? this.paginationManager.getAllData() : this.allData;

            if (!allData || allData.length === 0) {
                this.showError('没有可复制的数据');
                return;
            }

            // 获取有效数据（排除加载状态的数据）
            const validData = allData.filter(item => item !== null && item.status !== 'loading');
            if (validData.length === 0) {
                this.showError('没有有效的数据可复制');
                return;
            }

            // 定义统一标准格式的表头
            const headers = [
                '是否参与报价*', '商品sku*', '商品名称*', '品牌*', '制造商', '商品官网地址*', '规格参数*', '报价*', '官网价'
            ];

            // 构建制表符分隔的数据（适合Excel粘贴）
            let tabContent = headers.join('\t') + '\n';

            validData.forEach(item => {
                const row = [
                    '是',                           // 固定值"是"
                    item.sku_number || '',          // 商品sku*
                    item.product_name || '',        // 商品名称*
                    item.product_ventor || '',      // 品牌*
                    '',                             // 空字段
                    item.url || '',                 // URL地址*
                    item.product_model || '',       // 规格参数*
                    '',                             // 空字段
                    item.product_price || ''        // 官网价
                ];
                tabContent += row.join('\t') + '\n';
            });

            // 复制到剪贴板（制表符格式，适合Excel粘贴自动分列）
            navigator.clipboard.writeText(tabContent).then(() => {
                if (typeof authManager !== 'undefined') {
                    authManager.showNotification(`已复制 ${validData.length} 条数据到剪贴板`, 'success');
                }
            }).catch(err => {
                console.error('复制失败:', err);
                this.showError('复制失败，请手动选择数据');
            });

        } catch (error) {
            console.error('复制数据失败:', error);
            this.showError('复制数据失败: ' + error.message);
        }
    }

    // 导出Excel文件
    exportToExcel() {
        try {
            // 使用分页管理器获取所有数据
            const allData = this.paginationManager ? this.paginationManager.getAllData() : this.allData;

            if (!allData || allData.length === 0) {
                this.showError('没有可导出的数据');
                return;
            }

            // 获取有效数据（排除加载状态的数据）
            const validData = allData.filter(item => item !== null && item.status !== 'loading');
            if (validData.length === 0) {
                this.showError('没有有效的数据可导出');
                return;
            }

            // 生成文件名
            const now = new Date();
            const timestamp = now.getFullYear() +
                String(now.getMonth() + 1).padStart(2, '0') +
                String(now.getDate()).padStart(2, '0') + '_' +
                String(now.getHours()).padStart(2, '0') +
                String(now.getMinutes()).padStart(2, '0') +
                String(now.getSeconds()).padStart(2, '0');
            const platformName = this.getPlatformName(this.currentPlatform);
            const filename = `友商数据采集_${platformName}平台_${timestamp}.xlsx`;

            // 构建Excel数据
            const excelData = this.prepareExportData(validData);

            // 创建工作簿
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.aoa_to_sheet(excelData);

            // 设置列宽（统一标准格式）
            ws['!cols'] = [
                { wch: 8 },  // 是
                { wch: 15 }, // 商品sku*
                { wch: 30 }, // 商品名称*
                { wch: 15 }, // 品牌*
                { wch: 8 },  // 空
                { wch: 40 }, // URL地址*
                { wch: 20 }, // 规格参数*
                { wch: 8 },  // 空
                { wch: 15 }  // 官网价
            ];

            XLSX.utils.book_append_sheet(wb, ws, '友商数据');
            XLSX.writeFile(wb, filename);

            if (typeof authManager !== 'undefined') {
                authManager.showNotification(`已导出 ${validData.length} 条数据到 ${filename}`, 'success');
            }

        } catch (error) {
            console.error('导出Excel失败:', error);
            this.showError('导出Excel失败: ' + error.message);
        }
    }

    // 导出CSV文件
    exportToCSV() {
        try {
            // 使用分页管理器获取所有数据
            const allData = this.paginationManager ? this.paginationManager.getAllData() : this.allData;

            if (!allData || allData.length === 0) {
                this.showError('没有可导出的数据');
                return;
            }

            // 获取有效数据（排除加载状态的数据）
            const validData = allData.filter(item => item !== null && item.status !== 'loading');
            if (validData.length === 0) {
                this.showError('没有有效的数据可导出');
                return;
            }

            // 生成文件名
            const now = new Date();
            const timestamp = now.getFullYear() +
                String(now.getMonth() + 1).padStart(2, '0') +
                String(now.getDate()).padStart(2, '0') + '_' +
                String(now.getHours()).padStart(2, '0') +
                String(now.getMinutes()).padStart(2, '0') +
                String(now.getSeconds()).padStart(2, '0');
            const platformName = this.getPlatformName(this.currentPlatform);
            const filename = `友商数据采集_${platformName}平台_${timestamp}.csv`;

            // 构建CSV数据
            const csvData = this.prepareExportData(validData);
            let csvContent = '';

            csvData.forEach(row => {
                // 处理包含逗号的字段，用双引号包围
                const processedRow = row.map(cell => {
                    const cellStr = String(cell || '');
                    if (cellStr.includes(',') || cellStr.includes('"') || cellStr.includes('\n')) {
                        return '"' + cellStr.replace(/"/g, '""') + '"';
                    }
                    return cellStr;
                });
                csvContent += processedRow.join(',') + '\n';
            });

            // 添加BOM以支持中文
            const BOM = '\uFEFF';
            const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });

            // 创建下载链接
            const link = document.createElement('a');
            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', filename);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }

            if (typeof authManager !== 'undefined') {
                authManager.showNotification(`已导出 ${validData.length} 条数据到 ${filename}`, 'success');
            }

        } catch (error) {
            console.error('导出CSV失败:', error);
            this.showError('导出CSV失败: ' + error.message);
        }
    }

    // 准备导出数据（统一标准格式）
    prepareExportData(data) {
        // 定义统一标准格式表头
        const headers = [
            '是否参与报价*', '商品sku*', '商品名称*', '品牌*', '制造商', '商品官网地址*', '规格参数*', '报价*', '官网价'
        ];

        // 构建数据行
        const rows = [headers];

        data.forEach(item => {
            const row = [
                '是',                           // 固定值"是"
                item.sku_number || '',          // 商品sku*
                item.product_name || '',        // 商品名称*
                item.product_ventor || '',      // 品牌*
                '',                             // 空字段
                item.url || '',                 // URL地址*
                item.product_model || '',       // 规格参数*
                '',                             // 空字段
                item.product_price || ''        // 官网价
            ];
            rows.push(row);
        });

        return rows;
    }

    async retrySingleSku(sku, rowIndex) {
        try {
            // 获取重试按钮并设置加载状态
            const retryBtn = document.getElementById(`retry-btn-${rowIndex}`);
            if (retryBtn) {
                retryBtn.disabled = true;
                retryBtn.innerHTML = '<i class="ti ti-loader-2 spinner"></i>';
            }

            // 调用重试API（采集模式由配置文件控制）
            const apiEndpoints = {
                'colipu': '/api/v1/scraper/colipu/retry',
                'officemate': '/api/v1/scraper/officemate/retry',
                'comix': '/api/v1/scraper/comix/retry',
                'xfs': '/api/v1/scraper/xfs/retry',
                'lxwl': '/api/v1/scraper/lxwl/retry',
                'lxwl-new': '/api/v1/scraper/lxwl-new/retry',
                'xhgj': '/api/v1/scraper/xhgj/retry'
            };

            const endpoint = apiEndpoints[this.currentPlatform];
            if (!endpoint) {
                throw new Error('未知的平台类型');
            }

            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    sku: sku
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.success && result.data) {
                // 更新对应行的数据
                if (this.streamResults && this.streamResults[rowIndex]) {
                    // 更新流式结果数组
                    this.streamResults[rowIndex] = result.data;
                }

                // 更新allData数组中的数据
                this.updateDataInAllData(rowIndex, result.data);

                // 显示成功通知
                if (typeof authManager !== 'undefined') {
                    const statusText = result.data.status === 'success' ? '重试成功' : '重试完成但仍失败';
                    authManager.showNotification(`SKU ${sku} ${statusText}`, result.data.status === 'success' ? 'success' : 'warning');
                }
            } else {
                throw new Error(result.message || '重试失败');
            }

        } catch (error) {
            console.error('重试SKU失败:', error);
            if (typeof authManager !== 'undefined') {
                authManager.showNotification(`SKU ${sku} 重试失败: ${error.message}`, 'error');
            }
        } finally {
            // 恢复重试按钮状态
            const retryBtn = document.getElementById(`retry-btn-${rowIndex}`);
            if (retryBtn) {
                retryBtn.disabled = false;
                retryBtn.innerHTML = '<i class="ti ti-refresh"></i>';
            }
        }
    }

    showError(message) {
        if (typeof authManager !== 'undefined') {
            authManager.showNotification(message, 'error');
        } else {
            console.error(message);
        }
    }

    // HTML转义函数，与需求凭证模块保持一致
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 格式化价格，与需求凭证模块保持一致
    formatPrice(price) {
        if (price === null || price === undefined) return '0.00';
        return parseFloat(price).toFixed(2);
    }

    // 保存查询条件到本地存储（平台独立缓存）
    saveQueryConditionsToStorage(conditions) {
        try {
            // 只保存查询内容字段到本地存储，使用平台独立的缓存键
            const scraperConditions = {
                queryContent: conditions.queryContent || ''
            };
            const cacheKey = `scraper_query_content_${this.currentPlatform}`;
            localStorage.setItem(cacheKey, JSON.stringify(scraperConditions));
            console.log(`已保存平台 ${this.currentPlatform} 的查询条件到缓存`);
        } catch (error) {
            console.warn('无法保存友商数据采集查询条件到本地存储:', error);
        }
    }

    // 保存用户选择的平台到本地存储
    saveSelectedPlatformToStorage(platform) {
        try {
            localStorage.setItem('scraper_selected_platform', platform);
            console.log(`已保存用户选择的平台: ${platform}`);
        } catch (error) {
            console.warn('无法保存用户选择的平台到本地存储:', error);
        }
    }

    // 从本地存储加载用户上次选择的平台
    loadSelectedPlatformFromStorage() {
        try {
            const savedPlatform = localStorage.getItem('scraper_selected_platform');
            if (savedPlatform) {
                // 验证平台ID是否有效
                const validPlatforms = ['colipu', 'officemate', 'comix', 'xfs', 'lxwl', 'lxwl-new', 'xhgj'];
                if (validPlatforms.includes(savedPlatform)) {
                    console.log(`从缓存加载用户上次选择的平台: ${savedPlatform}`);
                    return savedPlatform;
                } else {
                    console.warn(`缓存中的平台ID无效: ${savedPlatform}，将使用默认平台`);
                    // 清理无效的缓存
                    localStorage.removeItem('scraper_selected_platform');
                }
            }
        } catch (error) {
            console.warn('无法从本地存储加载用户选择的平台:', error);
        }
        return null; // 返回null表示没有有效的缓存数据
    }

    // 从本地存储加载查询条件（平台独立缓存）
    loadQueryConditionsFromStorage() {
        try {
            // 使用平台独立的缓存键加载数据
            const cacheKey = `scraper_query_content_${this.currentPlatform}`;
            const stored = localStorage.getItem(cacheKey);

            const form = document.getElementById('scraper-form');
            const queryContentField = form ? form.querySelector('#query-content') : null;

            if (stored && queryContentField) {
                const conditions = JSON.parse(stored);
                // 仅恢复查询内容字段值
                if (conditions.queryContent) {
                    queryContentField.value = conditions.queryContent;
                    // 恢复后重新验证表单
                    this.validateForm();
                    console.log(`已加载平台 ${this.currentPlatform} 的历史查询条件`);
                } else {
                    // 如果缓存中没有内容，清空输入框
                    queryContentField.value = '';
                    this.validateForm();
                }
            } else if (queryContentField) {
                // 如果没有缓存数据，清空输入框
                queryContentField.value = '';
                this.validateForm();
                console.log(`平台 ${this.currentPlatform} 暂无历史查询记录`);
            }
        } catch (error) {
            console.warn('无法从本地存储加载友商数据采集查询条件:', error);
            // 错误处理：清空输入框
            const form = document.getElementById('scraper-form');
            const queryContentField = form ? form.querySelector('#query-content') : null;
            if (queryContentField) {
                queryContentField.value = '';
                this.validateForm();
            }
        }
    }


    // 清理指定平台的缓存数据
    clearPlatformCache(platform) {
        try {
            const cacheKey = `scraper_query_content_${platform}`;
            localStorage.removeItem(cacheKey);
            console.log(`已清理平台 ${platform} 的缓存数据`);

            // 如果是当前平台，清空输入框
            if (platform === this.currentPlatform) {
                const form = document.getElementById('scraper-form');
                const queryContentField = form ? form.querySelector('#query-content') : null;
                if (queryContentField) {
                    queryContentField.value = '';
                    this.validateForm();
                }
            }
        } catch (error) {
            console.warn(`清理平台 ${platform} 缓存时出错:`, error);
        }
    }

    // 清理所有缓存数据（包括平台选择和SKU数据）
    clearAllCache() {
        try {
            // 清理平台选择缓存
            localStorage.removeItem('scraper_selected_platform');

            // 清理所有平台的SKU缓存
            const platforms = ['colipu', 'officemate', 'comix', 'xfs', 'lxwl', 'lxwl-new', 'xhgj'];
            platforms.forEach(platform => {
                const cacheKey = `scraper_query_content_${platform}`;
                localStorage.removeItem(cacheKey);
            });

            console.log('已清理所有友商数据采集器缓存数据');

            // 清空当前输入框
            const form = document.getElementById('scraper-form');
            const queryContentField = form ? form.querySelector('#query-content') : null;
            if (queryContentField) {
                queryContentField.value = '';
                this.validateForm();
            }
        } catch (error) {
            console.warn('清理所有缓存时出错:', error);
        }
    }

    updateDropdownPlatformState(platform) {
        // 更新下拉菜单的选中状态
        const platformNames = {
            'colipu': '晨光',
            'officemate': '欧菲斯',
            'comix': '齐心',
            'xfs': '鑫方盛',
            'lxwl': '领先未来',
            'lxwl-new': '领先未来(新)',
            'xhgj': '咸亨'
        };

        const platformName = platformNames[platform] || platform;

        // 更新下拉按钮文本
        const dropdownText = document.getElementById('platform-dropdown-text');
        if (dropdownText) {
            dropdownText.textContent = `当前：${platformName}`;
        }

        // 更新下拉菜单项状态
        document.querySelectorAll('.platform-dropdown-item').forEach(item => {
            const icon = item.querySelector('.ti-circle, .ti-circle-filled');
            if (item.dataset.platform === platform) {
                // 选中状态
                item.classList.add('active');
                if (icon) {
                    icon.className = 'ti ti-circle-filled text-primary me-2';
                }
            } else {
                // 未选中状态
                item.classList.remove('active');
                if (icon) {
                    icon.className = 'ti ti-circle text-muted me-2';
                }
            }
        });

        console.log(`下拉菜单状态已更新: ${platformName}`);
    }


}

// 清空查询条件函数（全局函数，供HTML调用）
async function clearScraperQueryCondition() {
    const form = document.getElementById('scraper-form');
    if (form) {
        // 清空查询内容
        const queryContent = form.querySelector('#query-content');
        if (queryContent) {
            queryContent.value = '';
        }

        // 重置平台选择为默认（晨光）
        if (window.scraperManager) {
            await window.scraperManager.switchPlatform('colipu');
        }

        // 清空结果
        if (window.scraperManager) {
            window.scraperManager.displayResults([]);
            window.scraperManager.disableDataOperationButtons();
        }

        // 清空本地存储
        try {
            localStorage.removeItem('scraper-query-conditions');
        } catch (error) {
            console.warn('无法清空友商数据采集本地存储:', error);
        }

        // 显示清空成功提示
        if (typeof authManager !== 'undefined') {
            authManager.showNotification('查询条件已清空', 'success');
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async function() {
    window.scraperManager = new ScraperManager();
    console.log('友商数据采集模块已初始化');
});
