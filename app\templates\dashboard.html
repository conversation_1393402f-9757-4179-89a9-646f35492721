{% extends "base.html" %}

{% block title %}{{ app_name }} - 控制台{% endblock %}

{% block content %}
<div class="page-header d-print-none">
    <div class="container-xl">
        <div class="row g-2 align-items-center">
            <div class="col">
                <h2 class="page-title">
                    数据采集控制台
                </h2>
                <div class="text-muted mt-1">
                    选择下方模块开始数据采集
                </div>
            </div>
        </div>
    </div>
</div>

<div class="page-body">
    <div class="container-xl">
        <!-- 模块选择区域 -->
        <div id="main-content">
            <div class="row">
                <div class="col-12 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">数据采集模块</h3>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <!-- 友商数据采集模块 -->
                                <div class="col-md-6 col-lg-12 col-xl-12">
                                    <a href="/scraper" class="card card-link card-link-pop h-100">
                                        <div class="card-body text-center">
                                            <div class="card-icon mb-3">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler text-purple" width="48" height="48" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                    <path d="M3 7v10a2 2 0 0 0 2 2h14a2 2 0 0 0 2 -2v-10"></path>
                                                    <path d="M3 7a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2"></path>
                                                    <path d="M13 17v-6a1 1 0 0 0 -1 -1h-2a1 1 0 0 0 -1 1v6"></path>
                                                </svg>
                                            </div>
                                            <h4 class="card-title">友商数据采集</h4>
                                            <p class="text-muted">采集和分析友商平台商品数据</p>
                                        </div>
                                    </a>
                                </div>

                                <!-- 需求凭证查询模块 -->
                                <div class="col-md-6 col-lg-3 col-xl-3">
                                    <a href="/modules/xqpz" class="card card-link card-link-pop h-100">
                                        <div class="card-body text-center">
                                            <div class="card-icon mb-3">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler text-primary" width="48" height="48" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                    <path d="M14 3v4a1 1 0 0 0 1 1h4"></path>
                                                    <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"></path>
                                                    <path d="M9 9l1 0"></path>
                                                    <path d="M9 13l6 0"></path>
                                                    <path d="M9 17l6 0"></path>
                                                </svg>
                                            </div>
                                            <h4 class="card-title">需求凭证查询</h4>
                                            <p class="text-muted">采集和管理需求凭证数据</p>
                                        </div>
                                    </a>
                                </div>

                                <!-- 需求单查询模块 -->
                                <div class="col-md-6 col-lg-3 col-xl-3">
                                    <a href="/modules/xqd" class="card card-link card-link-pop h-100">
                                        <div class="card-body text-center">
                                            <div class="card-icon mb-3">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler text-success" width="48" height="48" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                    <path d="M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2"></path>
                                                    <path d="M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z"></path>
                                                    <path d="M9 12l2 2l4 -4"></path>
                                                </svg>
                                            </div>
                                            <h4 class="card-title">需求单查询</h4>
                                            <p class="text-muted">采集和管理需求单数据</p>
                                        </div>
                                    </a>
                                </div>

                                <!-- 商品状态查询模块 -->
                                <div class="col-md-6 col-lg-3 col-xl-3">
                                    <a href="/modules/product" class="card card-link card-link-pop h-100">
                                        <div class="card-body text-center">
                                            <div class="card-icon mb-3">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler text-warning" width="48" height="48" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                    <path d="M7 4v16l13 0v-16"></path>
                                                    <path d="M7 4l4 0l0 4"></path>
                                                    <path d="M11 4l4 0"></path>
                                                    <path d="M15 4l0 4l-4 0"></path>
                                                    <path d="M7 8l13 0"></path>
                                                    <path d="M7 12l13 0"></path>
                                                    <path d="M7 16l13 0"></path>
                                                </svg>
                                            </div>
                                            <h4 class="card-title">商品状态查询</h4>
                                            <p class="text-muted">采集和管理商品状态数据</p>
                                        </div>
                                    </a>
                                </div>

                                <!-- 品牌库管理模块 -->
                                <div class="col-md-6 col-lg-3 col-xl-3">
                                    <a href="/modules/brand" class="card card-link card-link-pop h-100">
                                        <div class="card-body text-center">
                                            <div class="card-icon mb-3">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler text-info" width="48" height="48" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                    <path d="M12 3l8 4.5l0 9l-8 4.5l-8 -4.5l0 -9l8 -4.5"></path>
                                                    <path d="M12 12l8 -4.5"></path>
                                                    <path d="M12 12l0 9"></path>
                                                    <path d="M12 12l-8 -4.5"></path>
                                                </svg>
                                            </div>
                                            <h4 class="card-title">品牌库</h4>
                                            <p class="text-muted">管理和维护品牌信息数据</p>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>



            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 仪表板特定的JavaScript代码
document.addEventListener('DOMContentLoaded', function() {
    console.log('仪表板页面已加载，所有功能对未登录用户开放');

    // 强制显示主要内容，无论登录状态如何
    const mainContent = document.getElementById('main-content');
    if (mainContent) {
        mainContent.style.display = 'block';
        console.log('主要内容已强制显示');
    }

    // 隐藏登录提示（如果存在）
    const loginPrompt = document.getElementById('login-prompt');
    if (loginPrompt) {
        loginPrompt.style.display = 'none';
    }

    // 可选：显示用户登录状态信息
    if (typeof authManager !== 'undefined' && authManager.userInfo && authManager.token) {
        console.log('用户已登录:', authManager.userInfo.username);
    } else {
        console.log('用户未登录，但可以正常使用仪表板功能');
    }
});

// 重写authManager的方法，确保在dashboard页面中始终显示主要内容
if (typeof authManager !== 'undefined') {
    // 保存原始方法
    const originalShowLoginPrompt = authManager.showLoginPrompt;
    const originalCheckAuthStatus = authManager.checkAuthStatus;

    // 重写showLoginPrompt方法，在dashboard页面中不隐藏主要内容
    authManager.showLoginPrompt = function() {
        console.log('Dashboard: 拦截showLoginPrompt调用，保持主要内容可见');
        const mainContent = document.getElementById('main-content');
        if (mainContent) {
            mainContent.style.display = 'block';
        }
        // 不调用原始的showLoginPrompt方法，避免隐藏内容
    };

    // 重写checkAuthStatus方法，在dashboard页面中始终返回成功并显示内容
    authManager.checkAuthStatus = async function() {
        const result = await originalCheckAuthStatus.call(this);
        // 无论认证结果如何，都显示主要内容
        const mainContent = document.getElementById('main-content');
        if (mainContent) {
            mainContent.style.display = 'block';
        }
        return result;
    };
}
</script>
{% endblock %}
