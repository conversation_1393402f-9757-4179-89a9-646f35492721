/**
 * 需求单详情页面 JavaScript
 * 使用模块化设计模式，提供完整的详情查看、权限检查、弹窗功能
 */

// 需求单详情模块对象
const XqdDetailModule = {
    // 模块初始化
    init() {
        console.log('需求单详情页面已加载');

        // 从URL参数获取需求单信息
        const urlParams = new URLSearchParams(window.location.search);
        const askSheetCode = urlParams.get('askSheetCode') || '';
        const askSheetId = urlParams.get('askSheetId') || '';

        if (!askSheetCode || !askSheetId) {
            this.showError('detail', '缺少必要的参数信息');
            this.showError('product', '缺少必要的参数信息');
            return;
        }

        // 更新页面标题
        this.updatePageTitle(askSheetCode);

        // 初始化认证检查
        this.initAuthCheck(askSheetCode, askSheetId);
    },

    // 更新页面标题
    updatePageTitle(askSheetCode) {
        const subtitle = document.getElementById('detail-subtitle');
        if (subtitle) {
            subtitle.textContent = `比价单号: ${askSheetCode}`;
        }
    },

    // 初始化认证检查
    async initAuthCheck(askSheetCode, askSheetId) {
        try {
            // 检查认证管理器是否可用
            if (typeof authManager === 'undefined') {
                console.error('认证管理器未加载');
                this.showLoginPrompt();
                return;
            }

            // 检查认证状态
            const isAuthenticated = await authManager.checkAuthStatus();

            if (isAuthenticated) {
                // 已认证，检查ELECTRON权限
                this.checkElectronAccessAndLoadData(askSheetCode, askSheetId);
            } else {
                // 未认证，显示登录提示
                this.showLoginPrompt();
            }
        } catch (error) {
            console.error('认证检查失败:', error);
            this.showLoginPrompt();
        }
    },

    // 检查ELECTRON访问权限并加载数据
    checkElectronAccessAndLoadData(askSheetCode, askSheetId) {
        if (!authManager.userInfo) {
            this.showLoginPrompt();
            return;
        }

        if (!authManager.userInfo.electron_access) {
            // 没有ELECTRON权限，显示权限不足提示
            this.showElectronAccessDenied();
            return;
        }

        // 有权限，显示主要内容并加载数据
        this.showMainContent();
        this.loadDetailData(askSheetCode, askSheetId);
        this.loadProductListData(askSheetCode, askSheetId);
    },

    // 显示登录提示
    showLoginPrompt() {
        const loginPrompt = document.getElementById('login-prompt');
        const mainContent = document.getElementById('main-content');
        const electronAccessDenied = document.getElementById('electron-access-denied');

        if (loginPrompt) loginPrompt.style.display = 'block';
        if (mainContent) mainContent.style.display = 'none';
        if (electronAccessDenied) electronAccessDenied.style.display = 'none';
    },

    // 显示ELECTRON权限不足提示
    showElectronAccessDenied() {
        const loginPrompt = document.getElementById('login-prompt');
        const mainContent = document.getElementById('main-content');
        const electronAccessDenied = document.getElementById('electron-access-denied');

        if (loginPrompt) loginPrompt.style.display = 'none';
        if (mainContent) mainContent.style.display = 'none';
        if (electronAccessDenied) electronAccessDenied.style.display = 'block';
    },

    // 显示主要内容
    showMainContent() {
        const loginPrompt = document.getElementById('login-prompt');
        const mainContent = document.getElementById('main-content');
        const electronAccessDenied = document.getElementById('electron-access-denied');

        if (loginPrompt) loginPrompt.style.display = 'none';
        if (mainContent) mainContent.style.display = 'block';
        if (electronAccessDenied) electronAccessDenied.style.display = 'none';
    },

    // 加载详情数据
    async loadDetailData(askSheetCode, askSheetId) {
        try {
            this.showLoading('detail');

            const response = await fetch('/api/v1/data/xqd/detail', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authManager.token}`
                },
                body: JSON.stringify({
                    askSheetCode: askSheetCode,
                    askSheetId: askSheetId
                })
            });

            if (!response.ok) {
                let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
                try {
                    const errorData = await response.json();
                    errorMessage = errorData.message || errorData.detail || errorMessage;
                } catch (e) {
                    console.warn('无法解析错误响应为JSON');
                }
                throw new Error(errorMessage);
            }

            const result = await response.json();

            if (!result.success) {
                // 检查是否为权限相关错误
                if (result.permission_error) {
                    authManager.showNotification('权限已失效，正在重新登录...', 'warning');
                    authManager.handleTokenExpiry();
                    return;
                }
                throw new Error(result.message || '获取详情失败');
            }

            // 显示详情数据
            this.displayDetailData(result.data);

        } catch (error) {
            console.error('加载需求单详情错误:', error);
            this.showError('detail', error.message);
        }
    },

    // 加载产品列表数据
    async loadProductListData(askSheetCode, askSheetId) {
        try {
            this.showLoading('product');

            const response = await fetch('/api/v1/data/xqd/detail-list', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authManager.token}`
                },
                body: JSON.stringify({
                    askSheetCode: askSheetCode,
                    askSheetId: askSheetId
                })
            });

            if (!response.ok) {
                let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
                try {
                    const errorData = await response.json();
                    errorMessage = errorData.message || errorData.detail || errorMessage;
                } catch (e) {
                    console.warn('无法解析错误响应为JSON');
                }
                throw new Error(errorMessage);
            }

            const result = await response.json();
            if (!result.success) {
                // 检查是否为权限相关错误
                if (result.permission_error) {
                    authManager.showNotification('权限已失效，正在重新登录...', 'warning');
                    authManager.handleTokenExpiry();
                    return;
                }
                throw new Error(result.message || '获取产品列表失败');
            }

            // 显示产品列表数据
            this.displayProductListData(result.data);

        } catch (error) {
            console.error('加载产品列表错误:', error);
            this.showError('product', error.message);
        }
    },

    // 显示详情数据
    displayDetailData(data) {
        // 更新各个字段
        document.getElementById('detail-askSheetCode').textContent = data.askSheetCode || '-';
        document.getElementById('detail-purchaserName').textContent = data.purchaserName || '-';
        document.getElementById('detail-askSheetName').textContent = data.askSheetName || '-';

        // 状态字段使用徽章显示
        const statusElement = document.getElementById('detail-askSheetStatus');
        statusElement.innerHTML = this.formatStatusBadge(data.askSheetStatus);

        // 询价范围字段
        const typeElement = document.getElementById('detail-askSheetType');
        typeElement.innerHTML = this.formatTypeBadge(data.askSheetType);

        document.getElementById('detail-answerEndTime').textContent = data.answerEndTime || '-';
        document.getElementById('detail-answerEffTime').textContent = data.answerEffTime || '-';
        document.getElementById('detail-askSheetUser').textContent = data.askSheetUser || '-';
        document.getElementById('detail-askSheetUserMobile').textContent = data.askSheetUserMobile || '-';
        document.getElementById('detail-answerBeginTime').textContent = data.answerBeginTime || '-';

        // 隐藏加载状态，显示内容
        this.hideLoading('detail');
        document.getElementById('detail-content').style.display = 'block';
    },

    // 显示产品列表数据
    displayProductListData(products) {
        const tableBody = document.getElementById('product-table-body');
        const productCount = document.getElementById('product-count');

        // 更新产品数量
        productCount.textContent = `${products.length} 个产品`;

        if (products.length === 0) {
            // 显示空状态
            this.hideLoading('product');
            document.getElementById('product-empty').style.display = 'block';
            return;
        }

        // 生成表格行
        let tableHTML = '';
        products.forEach((product, index) => {
            tableHTML += `
                <tr>
                    <td>
                        ${this.formatLongText(product.prodName || '', 'prodName', index)}
                    </td>
                    <td>${this.escapeHtml(product.brand || '')}</td>
                    <td><span class="badge bg-info">${product.num || 0}</span></td>
                    <td>${this.escapeHtml(product.unit || '')}</td>
                    <td>
                        ${this.formatLongText(product.specDesc || '', 'specDesc', index)}
                    </td>
                    <td>
                        ${this.formatLongText(product.manufacturer || '', 'manufacturer', index)}
                    </td>
                </tr>
            `;
        });

        tableBody.innerHTML = tableHTML;

        // 隐藏加载状态，显示内容
        this.hideLoading('product');
        document.getElementById('product-content').style.display = 'block';
    },

    // 显示加载状态
    showLoading(type) {
        const loadingElement = document.getElementById(`${type}-loading`);
        const contentElement = document.getElementById(`${type}-content`);
        const errorElement = document.getElementById(`${type}-error`);
        const emptyElement = document.getElementById(`${type}-empty`);

        if (loadingElement) loadingElement.style.display = 'flex';
        if (contentElement) contentElement.style.display = 'none';
        if (errorElement) errorElement.style.display = 'none';
        if (emptyElement) emptyElement.style.display = 'none';
    },

    // 隐藏加载状态
    hideLoading(type) {
        const loadingElement = document.getElementById(`${type}-loading`);
        if (loadingElement) loadingElement.style.display = 'none';
    },

    // 显示错误状态
    showError(type, message) {
        const loadingElement = document.getElementById(`${type}-loading`);
        const contentElement = document.getElementById(`${type}-content`);
        const errorElement = document.getElementById(`${type}-error`);
        const errorMessageElement = document.getElementById(`${type}-error-message`);
        const emptyElement = document.getElementById(`${type}-empty`);

        if (loadingElement) loadingElement.style.display = 'none';
        if (contentElement) contentElement.style.display = 'none';
        if (emptyElement) emptyElement.style.display = 'none';
        if (errorElement) errorElement.style.display = 'block';
        if (errorMessageElement) errorMessageElement.textContent = message;
    },

    // 格式化状态徽章
    formatStatusBadge(status) {
        const statusMap = {
            'DRAFT': { text: '草稿', class: 'bg-secondary' },
            'INQUIRING': { text: '询价中', class: 'bg-primary' },
            'CONFIRM_WAIT': { text: '决标中', class: 'bg-warning' },
            'FINISHED': { text: '已完成', class: 'bg-success' },
            'APPROVAL_ING': { text: '审批中', class: 'bg-info' }
        };

        const statusInfo = statusMap[status] || { text: status || '未知', class: 'bg-secondary' };
        return `<span class="badge ${statusInfo.class} status-badge">${statusInfo.text}</span>`;
    },

    // 格式化类型徽章
    formatTypeBadge(type) {
        const typeMap = {
            'ELEC_ASK': { text: '超市比价', class: 'bg-blue' },
            'NET_ASK': { text: '全网比价', class: 'bg-green' }
        };

        const typeInfo = typeMap[type] || { text: type || '未知', class: 'bg-secondary' };
        return `<span class="badge ${typeInfo.class} status-badge">${typeInfo.text}</span>`;
    },

    // HTML转义函数
    escapeHtml(text) {
        if (text === null || text === undefined) {
            return '';
        }
        const div = document.createElement('div');
        div.textContent = String(text);
        return div.innerHTML;
    },

    // 格式化长文本（带CSS省略号和查看详情功能）
    formatLongText(text, fieldType, rowIndex = null) {
        if (!text || text.trim() === '') {
            return '<span class="text-muted">-</span>';
        }

        const escapedText = this.escapeHtml(text);
        const uniqueId = `${fieldType}_${rowIndex}_${Date.now()}`;

        return `
            <div class="d-flex align-items-center">
                <span class="table-cell-ellipsis me-2" style="max-width: 200px;" title="${escapedText}">${escapedText}</span>
                <button class="btn btn-sm btn-ghost-secondary"
                        onclick="event.stopPropagation(); showTextDetail('${uniqueId}', this, '${fieldType}')"
                        title="查看完整内容"
                        data-full-text="${escapedText}">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-sm" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"></path>
                        <path d="M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6"></path>
                    </svg>
                </button>
            </div>
        `;
    }
};

// 显示文本详情弹窗
function showTextDetail(uniqueId, buttonElement, fieldType) {
    const fullText = buttonElement.getAttribute('data-full-text');
    if (!fullText) return;

    // 移除已存在的弹窗
    hideTextDetail();

    // 获取字段类型的中文名称
    const fieldNames = {
        'prodName': '产品名称',
        'specDesc': '规格描述',
        'manufacturer': '制造商'
    };
    const fieldName = fieldNames[fieldType] || '详情';

    // 创建弹窗元素
    const popover = document.createElement('div');
    popover.id = 'text-detail-popover';
    popover.className = 'delist-reason-popover'; // 复用商品状态模块的样式
    popover.innerHTML = `
        <div class="popover-content">
            <div class="popover-header">
                <span class="popover-title">${fieldName}详情</span>
                <button type="button" class="btn-close" onclick="hideTextDetail()" aria-label="关闭"></button>
            </div>
            <div class="popover-body">
                <p class="mb-0">${XqdDetailModule.escapeHtml(fullText)}</p>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.appendChild(popover);

    // 计算弹窗位置
    const buttonRect = buttonElement.getBoundingClientRect();
    const popoverRect = popover.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let left = buttonRect.left + buttonRect.width / 2 - popoverRect.width / 2;
    let top = buttonRect.top - popoverRect.height - 10;

    // 确保弹窗不超出视口边界
    if (left < 10) left = 10;
    if (left + popoverRect.width > viewportWidth - 10) {
        left = viewportWidth - popoverRect.width - 10;
    }

    if (top < 10) {
        top = buttonRect.bottom + 10;
    }

    popover.style.left = `${left}px`;
    popover.style.top = `${top}px`;

    // 显示弹窗
    setTimeout(() => {
        popover.classList.add('show');
    }, 10);

    // 点击外部关闭弹窗
    setTimeout(() => {
        document.addEventListener('click', handleTextDetailOutsideClick);
    }, 100);
}

// 隐藏文本详情弹窗
function hideTextDetail() {
    const popover = document.getElementById('text-detail-popover');
    if (popover) {
        popover.classList.remove('show');
        setTimeout(() => {
            if (popover.parentNode) {
                popover.parentNode.removeChild(popover);
            }
        }, 200);
    }

    // 移除外部点击监听器
    document.removeEventListener('click', handleTextDetailOutsideClick);
}

// 处理外部点击事件
function handleTextDetailOutsideClick(event) {
    const popover = document.getElementById('text-detail-popover');
    if (popover && !popover.contains(event.target)) {
        hideTextDetail();
    }
}

// 模块初始化
document.addEventListener('DOMContentLoaded', function() {
    XqdDetailModule.init();
});
