/* 需求单详情页面样式 */

/* 详情卡片样式 */
.detail-card {
    margin-bottom: 1.5rem;
}

.detail-field {
    margin-bottom: 0.75rem;
}

.detail-field .label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.25rem;
}

.detail-field .value {
    color: #212529;
}

.status-badge {
    font-size: 0.875rem;
}

/* 加载覆盖层样式 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

/* 产品表格样式 */
.product-table {
    font-size: 0.875rem;
}

.product-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

.product-table td {
    vertical-align: middle;
}

.text-truncate-custom {
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 表格样式已移动到 custom.css 的公共表格样式 */

/* ========== 表格文本省略号统一样式 ========== */

/* 表格单元格文本省略号样式 - 单行显示+省略号 */
.table-cell-ellipsis {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

/* 文本截断样式（保留兼容性） */
.text-truncate-custom {
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 详情弹窗样式 - 使用公共弹窗组件样式 */
/* 弹窗和按钮样式已移动到 custom.css */
/* JavaScript 中需要将 .delist-reason-popover 改为 .popover-component */

/* 详情字段样式 */
.detail-field {
    margin-bottom: 1rem;
}

.detail-field .label {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
    font-weight: 500;
}

.detail-field .value {
    font-size: 0.9rem;
    color: #495057;
    word-wrap: break-word;
}

/* 状态徽章样式 */
.status-badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

/* 产品表格样式优化 */
.product-table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    font-size: 0.875rem;
    color: #495057;
}

.product-table td {
    vertical-align: middle;
    font-size: 0.875rem;
}

/* 加载和错误状态样式 */
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    flex-direction: column;
}

.loading-container .spinner-border {
    margin-bottom: 1rem;
}
