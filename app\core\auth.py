"""
认证依赖项和中间件
"""

from fastapi import Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Dict, Any, Optional
from app.core.security import security_manager
from app.core.logging import logger

# HTTP Bearer 认证方案
security = HTTPBearer()


def get_client_ip(request: Request) -> str:
    """
    获取客户端真实IP地址

    按优先级检查以下请求头：
    1. X-Forwarded-For (多层代理情况下取第一个IP)
    2. X-Real-IP
    3. X-Forwarded-Host
    4. request.client.host (fallback)

    Args:
        request: FastAPI Request对象

    Returns:
        客户端真实IP地址字符串
    """
    # 1. 检查 X-Forwarded-For 头部（最高优先级）
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        # 处理多层代理情况，取第一个IP作为真实客户端IP
        real_ip = forwarded_for.split(",")[0].strip()
        if real_ip and real_ip != "unknown":
            logger.debug(f"从X-Forwarded-For获取客户端IP: {real_ip}")
            return real_ip

    # 2. 检查 X-Real-IP 头部
    real_ip = request.headers.get("X-Real-IP")
    if real_ip and real_ip != "unknown":
        logger.debug(f"从X-Real-IP获取客户端IP: {real_ip}")
        return real_ip

    # 3. 检查 X-Forwarded-Host 头部
    forwarded_host = request.headers.get("X-Forwarded-Host")
    if forwarded_host and forwarded_host != "unknown":
        logger.debug(f"从X-Forwarded-Host获取客户端IP: {forwarded_host}")
        return forwarded_host

    # 4. Fallback: 使用request.client.host
    client_host = getattr(request.client, 'host', None) if request.client else None
    if client_host:
        logger.debug(f"从request.client.host获取客户端IP: {client_host}")
        return client_host

    # 5. 最终fallback
    logger.warning("无法获取客户端IP地址，使用默认值")
    return "unknown"


async def get_current_user(
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> Dict[str, Any]:
    """
    获取当前用户信息
    
    从JWT令牌中解析用户信息，包括ELECTRON访问权限
    """
    try:
        # 验证令牌
        token = credentials.credentials
        payload = security_manager.verify_token(token)
        
        # 获取客户端IP地址
        client_ip = get_client_ip(request)

        # 提取用户信息
        user_info = {
            'username': payload.get('username'),
            'local_username': payload.get('local_username'),
            'service_username': payload.get('service_username'),
            'electron_username': payload.get('electron_username'),
            'platform': payload.get('platform'),
            'platform_display': payload.get('platform_display'),
            'display_name': payload.get('display_name'),
            'electron_access': payload.get('electron_access', False),
            'electron_token': payload.get('electron_token', ''),
            'client_ip': client_ip,  # 添加客户端IP信息
            'exp': payload.get('exp'),
            'iat': payload.get('iat')
        }
        
        # 检查必要的用户信息
        if not user_info['username']:
            logger.warning("JWT令牌中缺少用户名信息")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌中缺少用户信息",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        logger.debug(f"用户认证成功: {user_info['username']}")
        return user_info
        
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"用户认证过程中发生错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="认证失败",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_user_with_electron(
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    获取具有ELECTRON访问权限的当前用户
    
    确保用户已登录且具有ELECTRON平台访问权限
    """
    # 检查ELECTRON访问权限
    if not current_user.get('electron_access'):
        logger.warning(f"用户 {current_user.get('username')} 没有ELECTRON访问权限")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要电子超市平台访问权限"
        )
    
    # 检查ELECTRON访问令牌
    if not current_user.get('electron_token'):
        logger.warning(f"用户 {current_user.get('username')} 缺少ELECTRON访问令牌")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="缺少电子超市平台访问令牌"
        )
    
    return current_user
