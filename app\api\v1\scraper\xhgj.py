"""
咸亨平台数据采集API路由
参考齐心平台实现，提供完整的数据采集API接口
"""
from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from typing import Dict, Any
import json
from app.services.scraper.xhgj_service import xhgj_service
from app.services.analytics_service import analytics_service
from app.core.logging import logger
from app.core.config import get_settings
from app.core.auth import get_client_ip


router = APIRouter()


class XhgjScraperRequest(BaseModel):
    """咸亨平台数据采集请求模型"""
    query_content: str = Field(..., description="查询内容（SKU列表，支持空格、逗号、换行分隔）")


class XhgjSingleSkuRequest(BaseModel):
    """咸亨平台单个SKU数据采集请求模型"""
    sku: str = Field(..., description="单个SKU编码")


@router.post("/xhgj")
async def scrape_xhgj_data(request: XhgjScraperRequest, http_request: Request) -> Dict[str, Any]:
    """
    咸亨平台数据采集（批量模式）

    支持两种模式：
    - 直接模式：使用requests直接请求
    - Playwright模式：先获取cookie再使用requests请求
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        logger.info(f"收到咸亨平台数据采集请求 - 查询内容: {request.query_content}, 客户端IP: {client_ip}")

        # 设置客户端IP头部
        xhgj_service.set_client_ip_headers(client_ip)

        # 解析查询内容为SKU列表用于统计
        sku_list = xhgj_service._parse_query_content(request.query_content)

        # 记录查询日志到分析统计
        await analytics_service.record_query_log(
            module="scraper-xhgj",
            user="友商数据采集",
            platform="SCRAPER",
            success=True,
            query_params={
                'sku_list': sku_list,
                'sku_count': len(sku_list),
                'query_type': 'batch'
            }
        )

        # 执行数据采集（模式由配置文件控制）
        result = await xhgj_service.scrape_data(
            query_content=request.query_content
        )

        logger.info(f"咸亨平台数据采集完成 - 成功: {result.get('success_count', 0)}/{result.get('total_count', 0)} 条记录")

        return result

    except Exception as e:
        logger.error(f"咸亨平台数据采集API异常: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"咸亨平台数据采集失败: {str(e)}"
        )


@router.post("/xhgj/stream")
async def scrape_xhgj_data_stream(request: XhgjScraperRequest, http_request: Request):
    """
    咸亨平台数据采集（流式响应模式）

    实时返回每个SKU的采集结果，支持前端异步更新
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        logger.info(f"收到咸亨平台流式数据采集请求 - 客户端IP: {client_ip}")

        # 设置客户端IP头部
        xhgj_service.set_client_ip_headers(client_ip)

        # 解析查询内容为SKU列表用于统计
        sku_list = xhgj_service._parse_query_content(request.query_content)

        # 记录查询日志到分析统计
        await analytics_service.record_query_log(
            module="scraper-xhgj",
            user="友商数据采集",
            platform="SCRAPER",
            success=True,
            query_params={
                'sku_list': sku_list,
                'sku_count': len(sku_list),
                'query_type': 'stream'
            }
        )

        async def generate_stream():
            """生成流式响应数据"""
            try:
                # 解析SKU列表
                skus = xhgj_service._parse_query_content(request.query_content)

                if not skus:
                    yield f"data: {json.dumps({'type': 'error', 'message': '未找到有效的SKU编码'})}\n\n"
                    return

                # 发送初始化信息
                yield f"data: {json.dumps({'type': 'init', 'total': len(skus), 'skus': skus})}\n\n"

                # 执行流式数据采集（模式由配置文件控制）
                async for result in xhgj_service.scrape_data_stream(
                    query_content=request.query_content
                ):
                    yield f"data: {json.dumps(result)}\n\n"

            except Exception as e:
                logger.error(f"流式数据采集异常: {str(e)}")
                yield f"data: {json.dumps({'type': 'error', 'message': f'数据采集失败: {str(e)}'})}\n\n"

        return StreamingResponse(
            generate_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream"
            }
        )

    except Exception as e:
        logger.error(f"咸亨平台流式数据采集API异常: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"咸亨平台流式数据采集失败: {str(e)}"
        )


@router.post("/xhgj/retry")
async def retry_single_sku(request: XhgjSingleSkuRequest, http_request: Request) -> Dict[str, Any]:
    """
    重试单个SKU的数据采集
    """
    try:
        # 获取客户端IP
        client_ip = get_client_ip(http_request)
        logger.info(f"收到单个SKU重试请求 - SKU: {request.sku}, 客户端IP: {client_ip}")

        # 设置客户端IP头部
        xhgj_service.set_client_ip_headers(client_ip)

        # 执行单个SKU采集（模式由配置文件控制）
        result = await xhgj_service.scrape_single_sku(
            sku=request.sku
        )

        logger.info(f"单个SKU重试完成 - SKU: {request.sku}, 状态: {result.get('status')}")

        return {
            "success": True,
            "data": result,
            "message": f"SKU {request.sku} 重试完成"
        }

    except Exception as e:
        logger.error(f"单个SKU重试API异常: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"SKU重试失败: {str(e)}"
        )





@router.get("/xhgj/config")
async def get_xhgj_config() -> Dict[str, Any]:
    """
    获取咸亨平台采集配置信息

    返回当前的Playwright模式配置状态，用于前端显示
    """
    try:
        settings = get_settings()

        return {
            "success": True,
            "data": {
                "platform": "xhgj",
                "platform_name": "咸亨",
                "playwright_mode": settings.XHGJ_USE_PLAYWRIGHT,
                "mode_text": "Playwright增强模式" if settings.XHGJ_USE_PLAYWRIGHT else "HTTP直接模式"
            },
            "message": "配置获取成功"
        }

    except Exception as e:
        logger.error(f"获取咸亨平台配置失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取配置失败: {str(e)}"
        )
