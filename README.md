# 新航发航空数据采集器

[![FastAPI](https://img.shields.io/badge/FastAPI-0.104.1+-green.svg)](https://fastapi.tiangolo.com/)
[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/)
[![Tabler](https://img.shields.io/badge/Tabler.io-1.3+-orange.svg)](https://tabler.io/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

基于FastAPI的现代化异步Web应用，专为航空数据采集设计，支持多平台登录、实时数据查询和智能分析统计。

## 🚀 项目特性

- ✅ **现代化架构** - 基于FastAPI >=0.104.1的高性能异步Web框架
- ✅ **多平台认证** - 支持航空平台和航发平台SSO登录，JWT 2小时会话管理
- ✅ **专业UI设计** - 集成Tabler.io v1.3+前端框架，响应式设计
- ✅ **4大数据模块** - 商品状态、需求凭证、需求单、需求单信息采集
- ✅ **智能分析** - 24小时查询趋势图表、本周模块调用统计
- ✅ **高级功能** - 数据筛选器、分页管理、本地存储、导出功能
- ✅ **跨平台兼容** - 支持Windows、Linux、macOS多平台运行
- ✅ **无数据库设计** - 内存存储+JSON文件，轻量级部署

## 🏗️ 技术架构

### 后端技术栈
- **FastAPI** - 现代、快速的Python Web框架
- **aiohttp** - 异步HTTP客户端，支持高并发请求
- **Jinja2** - 强大的模板引擎
- **python-jose** - JWT令牌处理和安全认证
- **loguru** - 高性能结构化日志系统
- **BeautifulSoup4** - HTML解析，用于外部平台登录
- **pydantic** - 数据验证和设置管理

### 前端技术栈
- **Tabler.io** - 现代化管理界面UI框架
- **Bootstrap 5** - 响应式CSS框架
- **Chart.js** - 交互式图表库，用于数据可视化
- **原生JavaScript** - 模块化设计，无重型框架依赖
- **LocalStorage** - 客户端数据持久化

### 核心组件
- **认证系统** - 多平台SSO集成，JWT会话管理
- **数据采集引擎** - 异步数据获取和处理
- **分析统计服务** - 实时数据分析和趋势统计
- **UI组件库** - 可复用的前端组件

## 📁 项目结构

```
新航发航空数据采集器/
├── app/                     # 应用核心目录
│   ├── api/v1/             # API路由模块
│   │   ├── modules/        # 🆕 数据采集模块API
│   │   │   ├── product.py  # 商品状态查询API
│   │   │   ├── xqd.py      # 需求单查询API
│   │   │   ├── xqpz.py     # 需求凭证查询API
│   │   │   └── __init__.py # 模块API入口
│   │   ├── analytics.py    # 分析统计API
│   │   ├── auth.py         # 认证API
│   │   └── __init__.py     # API路由入口
│   ├── core/               # 核心配置模块
│   │   ├── config.py       # 应用配置
│   │   ├── logging.py      # 日志配置
│   │   ├── security.py     # 安全管理
│   │   └── auth.py         # 认证中间件
│   ├── services/           # 业务逻辑服务
│   │   ├── modules/        # 🆕 数据采集模块服务
│   │   │   ├── product_service.py # 商品状态服务
│   │   │   ├── xqd_service.py     # 需求单服务
│   │   │   ├── xqpz_service.py    # 需求凭证服务
│   │   │   └── __init__.py        # 模块服务入口
│   │   ├── auth_service.py # 异步认证服务
│   │   ├── analytics_service.py # 分析统计服务
│   │   └── __init__.py     # 服务入口
│   ├── templates/          # HTML模板
│   │   ├── base.html       # 基础模板
│   │   ├── index.html      # 主页模板
│   │   ├── dashboard.html  # 控制台模板
│   │   ├── footer.html     # 页脚组件
│   │   └── modules/        # 模块模板
│   │       ├── base_module.html    # 模块基础模板
│   │       ├── product-status.html # 商品状态模块
│   │       ├── xqd.html    # 需求单模块
│   │       ├── xqd-detail.html # 需求单详情
│   │       └── xqpz.html   # 需求凭证模块
│   ├── static/             # 静态资源
│   │   ├── css/            # 样式文件
│   │   │   ├── modules/    # 🆕 模块专用样式
│   │   │   │   ├── product.css     # 商品状态样式
│   │   │   │   ├── xqd.css         # 需求单样式
│   │   │   │   ├── xqd-detail.css  # 需求单详情样式
│   │   │   │   └── xqpz.css        # 需求凭证样式
│   │   │   ├── custom.css  # 自定义样式
│   │   │   ├── tabler.min.css      # Tabler框架样式
│   │   │   └── tabler-icons.min.css # Tabler图标样式
│   │   ├── js/             # JavaScript文件
│   │   │   ├── modules/    # 🆕 模块专用脚本
│   │   │   │   ├── product.js      # 商品状态模块
│   │   │   │   ├── xqd.js          # 需求单模块
│   │   │   │   ├── xqd-detail.js   # 需求单详情
│   │   │   │   └── xqpz.js         # 需求凭证模块
│   │   │   ├── auth.js     # 认证管理
│   │   │   ├── main.js     # 主要功能
│   │   │   ├── dashboard-stats.js # 仪表板统计
│   │   │   ├── data-collection.js # 数据采集
│   │   │   ├── data-filter.js # 数据筛选器
│   │   │   ├── pagination-manager.js # 分页管理
│   │   │   ├── account-manager.js # 账号管理
│   │   │   ├── button-manager.js # 按钮管理
│   │   │   ├── chart.min.js # Chart.js图表库
│   │   │   └── tabler.min.js # Tabler框架脚本
│   │   └── images/         # 图片资源
│   └── main.py             # 应用入口
├── logs/                   # 日志文件目录
├── analytics_data.json     # 分析数据存储
├── requirements.txt        # 依赖清单
├── run.py                  # 启动脚本
├── .env.example           # 环境变量示例
└── README.md              # 项目文档
```

## 🔄 模块化重构说明

### 重构概述

为了提升代码组织性、可维护性和扩展性，项目已完成模块文件结构重构，将所有数据采集模块相关文件移动到专门的 `modules` 子目录中。

### 重构目的和优势

#### 🎯 重构目的
- **代码组织性提升** - 模块相关文件集中管理，便于定位和维护
- **可维护性增强** - 清晰的目录结构，降低代码维护成本
- **扩展性改善** - 新增数据采集模块时有明确的文件放置规范
- **团队协作优化** - 模块化结构便于多人协作开发
- **职责分离** - 不同类型的功能模块独立管理

#### ✨ 重构优势
1. **模块化设计** - 每个数据采集模块的API、服务、样式、脚本文件统一管理
2. **命名规范** - 统一的文件命名约定，提升代码可读性
3. **依赖清晰** - 模块间依赖关系更加明确
4. **便于测试** - 模块化结构便于单元测试和集成测试
5. **快速定位** - 开发者可以快速找到特定模块的相关文件

### 重构涉及的文件变更

#### API路由文件重构
```
app/api/v1/product_status.py → app/api/v1/modules/product.py
app/api/v1/xqd.py            → app/api/v1/modules/xqd.py
app/api/v1/xqpz.py           → app/api/v1/modules/xqpz.py
```

#### 服务层文件重构
```
app/services/product_status_service.py → app/services/modules/product_service.py
app/services/xqd_service.py            → app/services/modules/xqd_service.py
app/services/xqpz_service.py           → app/services/modules/xqpz_service.py
```

#### 前端资源文件重构
```
# CSS文件
app/static/css/product-status.css → app/static/css/modules/product.css
app/static/css/xqd.css            → app/static/css/modules/xqd.css
app/static/css/xqd-detail.css     → app/static/css/modules/xqd-detail.css
app/static/css/xqpz.css           → app/static/css/modules/xqpz.css

# JavaScript文件
app/static/js/product-status.js → app/static/js/modules/product.js
app/static/js/xqd.js            → app/static/js/modules/xqd.js
app/static/js/xqd-detail.js     → app/static/js/modules/xqd-detail.js
app/static/js/xqpz.js           → app/static/js/modules/xqpz.js
```

### 开发规范和文件命名约定

#### 新增数据采集模块规范

1. **API路由文件**
   - 位置：`app/api/v1/modules/`
   - 命名：`{module_name}.py`
   - 示例：`app/api/v1/modules/new_module.py`

2. **服务层文件**
   - 位置：`app/services/modules/`
   - 命名：`{module_name}_service.py`
   - 示例：`app/services/modules/new_module_service.py`

3. **前端CSS文件**
   - 位置：`app/static/css/modules/`
   - 命名：`{module_name}.css`
   - 示例：`app/static/css/modules/new_module.css`

4. **前端JavaScript文件**
   - 位置：`app/static/js/modules/`
   - 命名：`{module_name}.js`
   - 示例：`app/static/js/modules/new_module.js`

5. **HTML模板文件**
   - 位置：`app/templates/modules/`
   - 命名：`{module_name}.html`
   - 示例：`app/templates/modules/new_module.html`

#### 导入路径更新规范

```python
# API路由导入
from app.api.v1.modules import product, xqd, xqpz

# 服务层导入
from app.services.modules.product_service import product_status_service
from app.services.modules.xqd_service import xqd_service
from app.services.modules.xqpz_service import xqpz_service
```

#### HTML模板路径更新规范

```html
<!-- CSS文件引用 -->
<link rel="stylesheet" href="{{ url_for('static', path='/css/modules/module_name.css') }}">

<!-- JavaScript文件引用 -->
<script src="{{ url_for('static', path='/js/modules/module_name.js') }}"></script>
```

## 🔧 核心功能模块

### 1. 多平台认证系统
- **航空平台登录** - 支持航空平台SSO认证，自动获取ELECTRON访问权限
- **航发平台登录** - 支持航发平台SSO认证，多平台账号管理
- **JWT会话管理** - 8 小时令牌有效期，自动过期检测和提醒
- **账号管理** - 本地账号存储、记住密码、快速切换功能
- **权限控制** - ELECTRON平台权限验证，数据采集功能访问控制

### 2. 数据采集模块

#### 📦 商品状态查询模块
- **API端点**: `/api/v1/data/product-status`
- **查询字段**: 商品名称、SKU编码、上架状态、下架类型、审核状态
- **功能特性**: 
  - 支持批量SKU查询（空格分隔）
  - 审核状态自动查询（REJECT状态显示驳回原因）
  - 下架原因智能截断显示
  - 单个SKU快速查询功能

#### 🎫 需求凭证查询模块
- **API端点**: `/api/v1/data/xqpz`
- **查询字段**: 比价单号、商品SKU编码
- **功能特性**:
  - 7列数据展示（SKU、商品名称、中标价格、计量单位、状态、客户信息）
  - 状态徽章显示（QUOTED蓝色、BIDDED绿色、NOTBID红色、FLOWBID橙色）
  - BIDDED状态商品状态查询功能
  - 本地存储查询条件

#### 📋 需求单查询模块
- **API端点**: `/api/v1/data/xqd`
- **查询字段**: 比价单号、比价单名称、报价时间范围、询价单状态、采购清单信息
- **功能特性**:
  - 9列数据展示（比价单号、名称、时间、商品数量、状态、客户信息等）
  - 状态徽章显示（DRAFT灰色、INQUIRING蓝色、CONFIRM_WAIT橙色、FINISHED绿色、APPROVAL_ING黄色）
  - INQUIRING状态报价单下载功能
  - 需求单详情页面（新标签页打开）
  - 产品列表查询和超长文本弹窗显示

### 3. 智能分析统计

#### 📊 控制台仪表板
- **24小时查询趋势图** - 基于Chart.js的交互式曲线图
- **本周模块调用统计** - 4个数据采集模块的调用次数统计
- **实时数据更新** - 自动刷新统计数据
- **专业图标设计** - 使用Tabler Icons库

#### 📈 分析统计API
- **查询日志记录**: `POST /api/v1/analytics/query-log`
- **24小时趋势**: `GET /api/v1/analytics/hourly-trends`
- **本周模块统计**: `GET /api/v1/analytics/weekly-module-stats`
- **实时仪表板数据**: `GET /api/v1/analytics/realtime-dashboard`

### 4. 高级UI功能

#### 🔍 数据筛选器
- **动态筛选器生成** - 根据数据列自动生成筛选条件
- **AND/OR逻辑支持** - 复杂筛选条件组合
- **本地存储状态** - 筛选条件自动保存和恢复
- **可折叠面板** - 节省界面空间

#### 📄 分页管理
- **灵活分页选项** - 支持20/50/80/100条每页
- **页码跳转** - 快速导航到指定页面
- **本地存储设置** - 分页设置自动保存
- **与筛选器集成** - 筛选后数据的分页处理

#### 📤 数据导出功能
- **表格行选择** - 支持单选/多选/范围选择
- **智能复制** - 根据选择状态复制对应数据
- **导出所有筛选数据** - 不限于当前页面

#### 💾 本地存储管理
- **查询条件保存** - 自动保存和恢复查询参数
- **账号信息存储** - 记住密码和快速登录
- **界面状态保存** - 分页、筛选器状态持久化
- **模块化存储** - 不同模块独立的存储空间

## 🚀 快速开始

### 环境要求
- Python 3.8+
- Windows/Linux/macOS
- 1GB+ 内存
- 网络连接（用于外部平台认证）

### 1. 环境准备

```bash

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/macOS
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境变量

```bash
# 复制环境变量示例文件
cp .env.example .env

# 编辑.env文件，配置必要的参数
# 特别是JWT密钥和外部平台配置
```

### 3. 运行应用

```bash
# 开发模式运行
python run.py

# 或者直接运行主模块
python app/main.py
```

### 4. 访问应用

- **主页**: http://localhost:8000
- **控制台**: http://localhost:8000/dashboard
- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health

## 📋 API文档

### 认证API

#### POST /api/v1/auth/login
用户登录接口

**请求参数**:
```json
{
  "username": "用户名",
  "password": "密码",
  "platform": "aviation|engine"
}
```

**响应示例**:
```json
{
  "success": true,
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "expires_in": 7200,
  "platform": "aviation",
  "user_info": {
    "local_username": "本地用户名",
    "service_username": "商城用户名",
    "electron_username": "超市用户名",
    "electron_access": true
  },
  "message": "登录成功"
}
```

#### GET /api/v1/auth/verify
令牌验证接口

**请求头**:
```
Authorization: Bearer <access_token>
```

**响应示例**:
```json
{
  "valid": true,
  "expired": false,
  "user_info": {
    "username": "用户名",
    "platform": "aviation",
    "electron_access": true
  }
}
```

### 数据采集API

#### POST /api/v1/data/product-status/query
商品状态查询

**请求参数**:
```json
{
  "name": "商品名称（可选）",
  "sku": "SKU编码（可选，支持多个用空格分隔）",
  "shelf_status": "UP_SHELF|DOWN_SHELF（可选）",
  "delist_type": "下架类型（可选）",
  "approval_status": "APPROVED|DRAFT（可选）"
}
```

#### POST /api/v1/data/xqpz/query
需求凭证查询

**请求参数**:
```json
{
  "askSheetCode": "比价单号（可选）",
  "sku": "商品SKU编码（可选）"
}
```

#### POST /api/v1/data/xqd/query
需求单查询

**请求参数**:
```json
{
  "askSheetCode": "比价单号（可选）",
  "askSheetName": "比价单名称（可选）",
  "answerBeginTime": "开始时间（可选）",
  "answerEndTime": "结束时间（可选）",
  "askSheetStatus": "询价单状态（可选）",
  "purchasingListInfo": "采购清单信息（可选）"
}
```

### 分析统计API

#### GET /api/v1/analytics/hourly-trends
获取24小时查询趋势

**响应示例**:
```json
{
  "success": true,
  "data": {
    "hourly_data": [0, 1, 2, 5, 3, 1, 0, ...],
    "hour_labels": ["00:00", "01:00", "02:00", ...],
    "total_queries": 15,
    "peak_hour": "14:00",
    "success_rate": 98.5
  }
}
```

#### GET /api/v1/analytics/weekly-module-stats
获取本周模块调用统计

**响应示例**:
```json
{
  "success": true,
  "data": {
    "modules": {
      "product-status": {
        "name": "商品状态",
        "this_week": 25,
        "last_week": 18,
        "change": 7,
        "change_percent": 38.9
      },
      "xqpz": {
        "name": "需求凭证",
        "this_week": 15,
        "last_week": 12,
        "change": 3,
        "change_percent": 25.0
      }
    }
  }
}
```

## ⚙️ 配置说明

### 环境变量配置

创建 `.env` 文件并配置以下参数：

```bash
# 应用配置
APP_NAME="新航发航空数据采集器"
APP_VERSION="1.3.0"
DEBUG=true
SECRET_KEY="your-secret-key-here-change-in-production"

# 服务器配置
HOST=0.0.0.0
PORT=8000

# JWT配置
JWT_SECRET_KEY="your-jwt-secret-key-here"
JWT_ALGORITHM="HS256"
ACCESS_TOKEN_EXPIRE_MINUTES=120  # 2小时

# 日志配置
LOG_LEVEL="INFO"
LOG_FILE="logs/app.log"
LOG_ROTATION="1 day"
LOG_RETENTION="30 days"

# 外部平台配置
CRYPTO_SERVICE_URL="https://tool.ksccc.cc:4433/crypto.php"

# 航空平台配置
AVIATION_SSO_URL="https://sso.eavic.com/login"
AVIATION_SERVICE_URL="https://www.eavic.com/caslogin"
AVIATION_ELECTRON_URL="https://www.eavic.com/rest/electron/electronShop"

# 航发平台配置
ENGINE_SSO_URL="https://sso.aecc-mall.com/login"
ENGINE_SERVICE_URL="https://www.aecc-mall.com/caslogin"
ENGINE_ELECTRON_URL="https://www.aecc-mall.com/rest/electron/electronShopLogin"
```

### 依赖项说明

主要依赖项及其用途：

```txt
# FastAPI 核心依赖
fastapi>=0.104.1          # Web框架
uvicorn[standard]>=0.24.0 # ASGI服务器
python-multipart>=0.0.6   # 表单数据处理

# 异步HTTP请求
aiohttp>=3.9.0            # 异步HTTP客户端
aiofiles>=23.2.1          # 异步文件操作

# 模板和静态文件
jinja2>=3.1.2             # 模板引擎

# 认证和安全
python-jose[cryptography]>=3.3.0  # JWT处理
passlib[bcrypt]>=1.7.4    # 密码加密

# 配置管理
pydantic>=2.5.0           # 数据验证
pydantic-settings>=2.1.0  # 设置管理
python-dotenv>=1.0.0      # 环境变量

# 日志系统
loguru>=0.7.2             # 高性能日志

# 外部平台集成
requests>=2.31.0          # HTTP请求
beautifulsoup4>=4.12.0    # HTML解析
lxml>=4.9.0               # XML/HTML解析器

# 开发工具
pytest>=7.4.3            # 测试框架
pytest-asyncio>=0.21.1   # 异步测试
```

## 🔧 开发指南

### 添加新的数据采集模块

按照重构后的模块化结构，添加新模块需要遵循以下步骤：

1. **创建API路由**
```python
# app/api/v1/modules/new_module.py
from fastapi import APIRouter, Depends
from app.core.auth import get_current_user
from app.services.modules.new_module_service import new_module_service

router = APIRouter()

@router.post("/new-module")
async def query_new_module(
    request: NewModuleRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    # 实现查询逻辑
    result = await new_module_service.query_data(**request.dict())
    return result
```

2. **创建服务类**
```python
# app/services/modules/new_module_service.py
from app.core.logging import logger

class NewModuleService:
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0...',
            'Content-Type': 'application/json'
        }

    async def query_data(self, **kwargs):
        # 实现数据查询逻辑
        logger.info("开始新模块数据查询")
        # ... 查询逻辑
        return {"success": True, "data": [], "message": "查询成功"}

# 创建全局实例
new_module_service = NewModuleService()
```

3. **更新API路由注册**
```python
# app/api/v1/__init__.py
from app.api.v1.modules import product, xqd, xqpz, new_module

# 添加新模块路由
api_router.include_router(new_module.router, prefix="/data", tags=["新模块采集"])
```

4. **添加前端模板**
```html
<!-- app/templates/modules/new-module.html -->
{% extends "base.html" %}

{% block title %}{{ app_name }} - 新模块采集{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', path='/css/modules/new-module.css') }}">
{% endblock %}

{% block content %}
<!-- 模块特定内容 -->
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', path='/js/button-manager.js') }}"></script>
<script src="{{ url_for('static', path='/js/pagination-manager.js') }}"></script>
<script src="{{ url_for('static', path='/js/data-filter.js') }}"></script>
<script src="{{ url_for('static', path='/js/modules/new-module.js') }}"></script>
{% endblock %}
```

5. **创建模块样式**
```css
/* app/static/css/modules/new-module.css */
.new-module-card {
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .new-module-card {
        margin: 0.5rem;
    }
}
```

6. **创建JavaScript模块**
```javascript
// app/static/js/modules/new-module.js
class NewModuleManager {
    constructor() {
        this.apiEndpoint = '/api/v1/data/new-module';
        this.init();
    }

    init() {
        // 初始化事件监听器
        this.bindEvents();
    }

    bindEvents() {
        // 绑定查询按钮事件
        document.getElementById('queryBtn').addEventListener('click', () => {
            this.handleQuery();
        });
    }

    async handleQuery() {
        // 查询逻辑实现
        try {
            const response = await fetch(this.apiEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                },
                body: JSON.stringify(this.getQueryParams())
            });

            const result = await response.json();
            this.displayResults(result);
        } catch (error) {
            console.error('查询失败:', error);
        }
    }

    getQueryParams() {
        // 获取查询参数
        return {};
    }

    displayResults(result) {
        // 显示查询结果
        console.log('查询结果:', result);
    }
}

// 初始化模块管理器
document.addEventListener('DOMContentLoaded', () => {
    new NewModuleManager();
});
```

### 自定义样式

编辑 `app/static/css/custom.css` 文件来自定义样式：

```css
/* 自定义模块样式 */
.new-module-card {
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .new-module-card {
        margin: 0.5rem;
    }
}
```

### 日志配置

应用使用 `loguru` 进行日志管理，配置文件位于 `app/core/logging.py`：

```python
# 自定义日志配置
logger.add(
    "logs/custom.log",
    rotation="1 day",
    retention="30 days",
    level="INFO",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
)
```

## 🚀 部署说明

### 生产环境部署

#### 1. 使用Gunicorn部署

```bash
# 安装Gunicorn
pip install gunicorn

# 启动应用
gunicorn app.main:app \
  -w 4 \
  -k uvicorn.workers.UvicornWorker \
  --bind 0.0.0.0:8000 \
  --access-logfile logs/access.log \
  --error-logfile logs/error.log
```

#### 2. 使用Docker部署

创建 `Dockerfile`：

```dockerfile
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建日志目录
RUN mkdir -p logs

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

创建 `docker-compose.yml`：

```yaml
version: '3.8'

services:
  newhf-app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DEBUG=false
      - SECRET_KEY=${SECRET_KEY}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
    volumes:
      - ./logs:/app/logs
      - ./analytics_data.json:/app/analytics_data.json
    restart: unless-stopped
```

#### 3. 使用Nginx反向代理

**重要提示：** 应用已内置反向代理头部处理中间件，确保在HTTPS环境下静态资源URL正确生成。

##### 基础HTTP配置

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
    }

    location /static/ {
        alias /path/to/app/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

##### HTTPS生产环境配置

对于HTTPS部署，请使用项目根目录的 `nginx.conf.example` 完整配置文件：

```bash
# 复制配置文件
cp nginx.conf.example /etc/nginx/sites-available/newhf-aviation
ln -s /etc/nginx/sites-available/newhf-aviation /etc/nginx/sites-enabled/

# 修改配置文件中的域名和路径
sudo nano /etc/nginx/sites-available/newhf-aviation

# 测试配置并重启Nginx
sudo nginx -t
sudo systemctl restart nginx
```

##### 应用配置

在生产环境中，更新 `.env` 文件启用反向代理支持：

```bash
# 反向代理配置
BEHIND_PROXY=true
TRUSTED_HOSTS=["your-domain.com", "www.your-domain.com"]
FORCE_HTTPS=true
```

##### 测试和验证

项目提供了专用的测试脚本来验证HTTPS配置：

```bash
# 测试反向代理头部处理
python test_proxy_headers.py --url https://your-domain.com

# 检查HTTPS部署配置
python check_https_deployment.py https://your-domain.com

# 调试模式下查看请求头部信息
curl https://your-domain.com/debug/headers
```

##### 常见问题解决

1. **静态资源仍使用HTTP协议**
   - 确保Nginx配置包含 `X-Forwarded-Proto` 头部
   - 检查应用配置 `BEHIND_PROXY=true`
   - 重启应用和Nginx服务

2. **混合内容错误**
   - 检查浏览器开发者工具的控制台
   - 确保所有静态资源使用HTTPS或相对路径
   - 验证CDN资源（如Tabler Icons）使用HTTPS

3. **SSL证书问题**
   - 使用 `check_https_deployment.py` 脚本检查证书
   - 确保证书包含所有域名（主域名和www子域名）
   - 检查证书有效期
```

### 生产环境配置

```bash
# 设置生产环境变量
export DEBUG=false
export SECRET_KEY="your-production-secret-key"
export JWT_SECRET_KEY="your-production-jwt-key"
export LOG_LEVEL="WARNING"

# 创建systemd服务
sudo tee /etc/systemd/system/newhf.service > /dev/null <<EOF
[Unit]
Description=NewHF Aviation Data Collector
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/path/to/app
Environment=PATH=/path/to/venv/bin
ExecStart=/path/to/venv/bin/gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
Restart=always

[Install]
WantedBy=multi-user.target
EOF

# 启用并启动服务
sudo systemctl enable newhf
sudo systemctl start newhf
```

## 🔍 故障排除

### 常见问题

#### 1. 登录失败
**问题**: 用户无法登录外部平台

**解决方案**:
- 检查网络连接是否正常
- 确认外部平台配置URL是否正确
- 查看日志文件 `logs/app.log` 获取详细错误信息
- 验证用户名和密码是否正确
- 检查外部平台是否可访问

```bash
# 检查网络连接
curl -I https://sso.eavic.com/login

# 查看详细日志
tail -f logs/app.log
```

#### 2. 令牌过期
**问题**: JWT令牌过期导致API调用失败

**解决方案**:
- 重新登录获取新令牌
- 检查系统时间是否正确

#### 3. 数据查询失败
**问题**: 数据采集模块查询失败

**解决方案**:
- 确认已正确登录并获得ELECTRON访问权限
- 检查API端点是否正确
- 查看浏览器控制台错误信息
- 验证查询参数格式是否正确

#### 4. 图表不显示
**问题**: 仪表板图表无法正常显示

**解决方案**:
- 检查Chart.js是否正确加载
- 确认网络连接正常（CDN资源）
- 查看浏览器控制台JavaScript错误
- 检查分析统计API是否正常工作

```javascript
// 在浏览器控制台检查Chart.js
console.log(typeof Chart);  // 应该返回 'function'
```

#### 5. 文件权限问题
**问题**: 日志文件或数据文件无法写入

**解决方案**:
```bash
# 检查文件权限
ls -la logs/
ls -la analytics_data.json

# 修复权限
chmod 755 logs/
chmod 644 logs/*.log
chmod 644 analytics_data.json
```

### 性能优化

#### 1. 内存使用优化
- 定期清理过期的分析数据
- 监控内存使用情况
- 调整日志保留策略

```python
# 监控内存使用
import psutil
print(f"内存使用: {psutil.virtual_memory().percent}%")
```

#### 2. 响应时间优化
- 使用异步请求处理
- 启用HTTP缓存
- 优化数据库查询（如果使用）

#### 3. 并发处理优化
```bash
# 调整Gunicorn工作进程数
gunicorn app.main:app -w $(nproc) -k uvicorn.workers.UvicornWorker
```

### 监控和维护

#### 1. 健康检查
```bash
# 检查应用状态
curl http://localhost:8000/health

# 检查API响应
curl -H "Authorization: Bearer <token>" http://localhost:8000/api/v1/analytics/test
```

#### 2. 日志监控
```bash
# 监控错误日志
tail -f logs/app_error.log | grep ERROR

# 分析访问模式
grep "POST /api/v1/data" logs/app.log | wc -l
```

#### 3. 备份策略
```bash
# 备份分析数据
cp analytics_data.json analytics_data.json.backup.$(date +%Y%m%d)

# 备份配置文件
tar -czf config_backup_$(date +%Y%m%d).tar.gz .env app/core/config.py
```

## 🧪 测试

### 运行测试

```bash
# 安装测试依赖
pip install pytest pytest-asyncio

# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_main.py

# 运行测试并生成覆盖率报告
pytest --cov=app tests/
```

### 测试用例示例

```python
# tests/test_auth.py
import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_login_success():
    response = client.post("/api/v1/auth/login", json={
        "username": "test_user",
        "password": "test_password",
        "platform": "aviation"
    })
    assert response.status_code == 200
    assert response.json()["success"] == True

def test_health_check():
    response = client.get("/health")
    assert response.status_code == 200
    assert "healthy" in response.json()["status"]
```

### 手动测试

使用测试账号进行端到端测试：

- **平台**: 航空平台
- **用户名**: hkcomix5
- **密码**: QXComix@12345677

测试流程：
1. 登录系统
2. 测试各个数据采集模块
3. 验证数据筛选和分页功能
4. 检查仪表板统计功能
5. 测试数据导出功能

## 📄 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 🤝 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. **Fork 项目**
2. **创建功能分支** (`git checkout -b feature/AmazingFeature`)
3. **提交更改** (`git commit -m 'Add some AmazingFeature'`)
4. **推送到分支** (`git push origin feature/AmazingFeature`)
5. **创建 Pull Request**

### 代码规范

- 使用 Python 3.8+ 语法
- 遵循 PEP 8 代码风格
- 添加适当的类型注解
- 编写单元测试
- 更新相关文档

### 提交信息规范

```
类型(范围): 简短描述

详细描述（可选）

关联问题: #123
```

类型包括：
- `feat`: 新功能
- `fix`: 错误修复
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- **项目地址**: [GitHub Repository](https://github.com/your-username/Project_NewHF)
- **问题反馈**: [GitHub Issues](https://github.com/your-username/Project_NewHF/issues)
- **邮箱**: <EMAIL>

## � 更新日志

### v1.3.0 (2025-01-11) - 模块化重构版本

#### 🔄 重大重构
- **模块文件结构重构** - 将所有数据采集模块相关文件移动到专门的 `modules` 子目录
- **API路由重组** - 数据采集模块API移至 `app/api/v1/modules/` 目录
- **服务层重组** - 数据采集服务移至 `app/services/modules/` 目录
- **前端资源重组** - CSS和JS文件移至 `app/static/css/modules/` 和 `app/static/js/modules/`

#### ✨ 新增功能
- **模块化开发规范** - 建立了统一的模块开发和命名规范
- **改进的项目结构** - 更清晰的目录组织，便于维护和扩展
- **开发指南更新** - 详细的模块化开发指南和最佳实践

#### 🔧 技术改进
- **导入路径优化** - 更新所有模块的导入路径以反映新结构
- **HTML模板路径更新** - 更新所有模板中的CSS和JS文件引用路径
- **缓存清理** - 清理旧的Python缓存文件，确保新结构正常工作

#### 📁 文件变更
- `app/api/v1/product_status.py` → `app/api/v1/modules/product.py`
- `app/api/v1/xqd.py` → `app/api/v1/modules/xqd.py`
- `app/api/v1/xqpz.py` → `app/api/v1/modules/xqpz.py`
- `app/services/product_status_service.py` → `app/services/modules/product_service.py`
- `app/services/xqd_service.py` → `app/services/modules/xqd_service.py`
- `app/services/xqpz_service.py` → `app/services/modules/xqpz_service.py`
- 所有模块CSS和JS文件移至对应的 `modules` 子目录

#### 🎯 重构优势
- **代码组织性提升** - 模块相关文件集中管理
- **可维护性增强** - 清晰的目录结构，降低维护成本
- **扩展性改善** - 新增模块有明确的文件放置规范
- **团队协作优化** - 模块化结构便于多人协作开发

### v1.2.0 (2024-12-XX) - 功能完善版本
- 完善数据采集模块功能
- 添加高级筛选和分页功能
- 优化用户界面和交互体验

### v1.1.0 (2024-11-XX) - 基础功能版本
- 实现基础数据采集功能
- 添加多平台认证系统
- 集成分析统计功能

### v1.0.0 (2024-10-XX) - 初始版本
- 项目初始化
- 基础架构搭建
- 核心功能实现

## �🙏 致谢

感谢以下开源项目和技术：

- [FastAPI](https://fastapi.tiangolo.com/) - 现代化的Python Web框架
- [Tabler.io](https://tabler.io/) - 优秀的管理界面UI框架
- [Chart.js](https://www.chartjs.org/) - 强大的图表库
- [Bootstrap](https://getbootstrap.com/) - 响应式CSS框架
- [Loguru](https://github.com/Delgan/loguru) - 优雅的Python日志库

---

<div align="center">
  <p>🚀 <strong>新航发航空数据采集器</strong> - 让数据采集更简单、更高效！</p>
  <p>Made with ❤️ by Development Team</p>
  <p><em>v1.3.0 - 模块化重构版本</em></p>
</div>
```
