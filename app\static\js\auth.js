/**
 * 认证管理模块
 * 处理用户登录、登出和会话管理，支持2小时JWT会话和过期提醒
 */

class AuthManager {
    constructor() {
        this.token = localStorage.getItem('access_token');
        this.userInfo = JSON.parse(localStorage.getItem('user_info') || '{}');
        this.tokenExpiry = localStorage.getItem('token_expiry');
        
        // 初始化时检查登录状态
        this.checkAuthStatus();
        
        // 设置定时检查令牌过期（每30秒检查一次）
        this.setupTokenExpiryCheck();
    }

    /**
     * 检查认证状态
     */
    async checkAuthStatus() {
        if (!this.token) {
            this.showLoginPrompt();
            return false;
        }

        try {
            const response = await fetch('/api/v1/auth/verify', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                    'Content-Type': 'application/json'
                }
            });

            const result = await response.json();
            
            if (result.valid && !result.expired) {
                this.showMainContent();
                this.updateUserDisplay();
                return true;
            } else {
                this.handleTokenExpiry();
                return false;
            }
        } catch (error) {
            console.error('认证状态检查失败:', error);
            this.showLoginPrompt();
            return false;
        }
    }

    /**
     * 自动登录（用于一键快速切换）
     */
    async autoLogin(accountData) {
        try {
            // 显示切换提示
            this.showNotification(`正在切换到${accountData.platform === 'aviation' ? '航空' : '航发'}平台账号: ${accountData.username}`, 'info');

            const response = await fetch('/api/v1/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: accountData.username,
                    password: accountData.password,
                    platform: accountData.platform
                })
            });

            const result = await response.json();

            if (result.success) {
                // 保存认证信息
                this.token = result.access_token;
                this.userInfo = {
                    username: accountData.username,
                    platform: accountData.platform,
                    ...result.user_info
                };

                const expiryTime = new Date(Date.now() + result.expires_in * 1000);
                this.tokenExpiry = expiryTime.toISOString();

                // 存储到本地存储
                localStorage.setItem('access_token', this.token);
                localStorage.setItem('user_info', JSON.stringify(this.userInfo));
                localStorage.setItem('token_expiry', this.tokenExpiry);

                // 更新UI
                this.showMainContent();
                this.updateUserDisplay();

                // 更新账号最后使用时间
                if (typeof accountManager !== 'undefined') {
                    accountManager.updateLastUsed(accountData.platform, accountData.username);
                    accountManager.updateUI(); // 更新快速切换列表
                }

                this.showNotification(`已成功切换到${accountData.platform === 'aviation' ? '航空' : '航发'}平台`, 'success');

                return true;
            } else {
                this.showNotification(result.message || '自动登录失败', 'error');
                return false;
            }
        } catch (error) {
            console.error('自动登录失败:', error);
            this.showNotification('网络连接失败，请稍后重试', 'error');
            return false;
        }
    }

    /**
     * 用户登录
     */
    async login(formData) {
        const submitBtn = document.getElementById('login-submit-btn');
        const originalText = submitBtn.innerHTML;

        try {
            // 显示加载状态
            submitBtn.disabled = true;
            submitBtn.innerHTML = `
                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                登录中...
            `;

            const response = await fetch('/api/v1/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: formData.username,
                    password: formData.password,
                    platform: formData.platform
                })
            });

            const result = await response.json();

            if (result.success) {
                // 如果选择了记住密码，保存账号信息
                if (formData.remember_password && typeof accountManager !== 'undefined') {
                    accountManager.saveAccount(
                        formData.platform,
                        formData.username,
                        formData.password,
                        result.user_info?.display_name || formData.username
                    );
                    this.showNotification('账号信息已保存到本地', 'info');
                }

                // 更新账号最后使用时间
                if (typeof accountManager !== 'undefined') {
                    accountManager.updateLastUsed(formData.platform, formData.username);
                }

                // 保存认证信息
                this.token = result.access_token;
                this.userInfo = {
                    username: formData.username,
                    platform: formData.platform,
                    ...result.user_info
                };

                const expiryTime = new Date(Date.now() + result.expires_in * 1000);
                this.tokenExpiry = expiryTime.toISOString();

                // 存储到本地存储
                localStorage.setItem('access_token', this.token);
                localStorage.setItem('user_info', JSON.stringify(this.userInfo));
                localStorage.setItem('token_expiry', this.tokenExpiry);

                // 更新UI
                this.showMainContent();
                this.updateUserDisplay();

                // 关闭登录模态框
                const loginModal = bootstrap.Modal.getInstance(document.getElementById('login-modal'));
                if (loginModal) {
                    loginModal.hide();
                }

                this.showNotification(result.message || '登录成功', 'success');

                // 重置表单
                document.getElementById('login-form').reset();

                return true;
            } else {
                this.showNotification(result.message || '登录失败', 'error');
                return false;
            }
        } catch (error) {
            console.error('登录失败:', error);
            this.showNotification('网络连接失败，请稍后重试', 'error');
            return false;
        } finally {
            // 恢复按钮状态
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        }
    }

    /**
     * 用户登出
     */
    async logout() {
        try {
            if (this.token) {
                await fetch('/api/v1/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${this.token}`,
                        'Content-Type': 'application/json'
                    }
                });
            }
        } catch (error) {
            console.error('登出请求失败:', error);
        } finally {
            // 清除本地存储
            localStorage.removeItem('access_token');
            localStorage.removeItem('user_info');
            localStorage.removeItem('token_expiry');
            
            // 重置状态
            this.token = null;
            this.userInfo = {};
            this.tokenExpiry = null;
            
            // 更新UI
            this.showLoginPrompt();
            this.showNotification('已成功登出', 'info');
        }
    }

    /**
     * 处理令牌过期
     */
    handleTokenExpiry() {
        this.showNotification('登录已过期，请重新登录', 'warning');
        this.logout();
    }

    /**
     * 设置令牌过期检查
     */
    setupTokenExpiryCheck() {
        setInterval(() => {
            if (this.token && this.tokenExpiry) {
                const now = new Date();
                const expiry = new Date(this.tokenExpiry);
                const timeRemaining = expiry.getTime() - now.getTime();

                // 令牌过期时直接处理登出
                if (timeRemaining <= 0) {
                    this.handleTokenExpiry();
                }
            }
        }, 30000); // 每30秒检查一次
    }



    /**
     * 显示登录提示
     */
    showLoginPrompt() {
        try {
            // 安全地获取DOM元素并检查是否存在
            const loginPrompt = document.getElementById('login-prompt');
            const mainContent = document.getElementById('main-content');
            const userMenu = document.getElementById('user-menu');

            if (loginPrompt) {
                loginPrompt.style.display = 'block';
            }

            if (mainContent) {
                mainContent.style.display = 'none';
            }

            if (userMenu) {
                userMenu.classList.add('d-none');
            }

            // 更新切换账号按钮文本
            const switchBtn = document.getElementById('switch-account-btn');
            if (switchBtn) {
                switchBtn.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-login" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M14 8v-2a2 2 0 0 0 -2 -2h-7a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h7a2 2 0 0 0 2 -2v-2"></path>
                        <path d="M20 12h-13l3 -3m0 6l-3 -3"></path>
                    </svg>
                    登录
                `;
            }
        } catch (error) {
            console.warn('显示登录提示时发生错误:', error);
        }
    }

    /**
     * 显示主要内容
     */
    showMainContent() {
        try {
            // 安全地获取DOM元素并检查是否存在
            const loginPrompt = document.getElementById('login-prompt');
            const mainContent = document.getElementById('main-content');
            const userMenu = document.getElementById('user-menu');

            if (loginPrompt) {
                loginPrompt.style.display = 'none';
            }

            if (mainContent) {
                mainContent.style.display = 'block';
            }

            if (userMenu) {
                userMenu.classList.remove('d-none');
            }

            // 更新切换账号按钮文本
            const switchBtn = document.getElementById('switch-account-btn');
            if (switchBtn) {
                switchBtn.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-user-plus" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M8 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0"></path>
                        <path d="M16 19h6"></path>
                        <path d="M19 16v6"></path>
                        <path d="M6 21v-2a4 4 0 0 1 4 -4h4"></path>
                    </svg>
                    切换账号
                `;
            }
        } catch (error) {
            console.warn('显示主要内容时发生错误:', error);
        }
    }

    /**
     * 更新用户显示信息
     */
    updateUserDisplay() {
        // 更新主显示区域
        const userNameElement = document.getElementById('user-name');
        const serviceUsernameElement = document.getElementById('service-username');
        const electronUsernameElement = document.getElementById('electron-username');
        const userAvatarElement = document.getElementById('user-avatar');
        const userAvatarContainer = document.getElementById('user-avatar-container');

        // 更新下拉菜单详细信息
        const dropdownLocalUsername = document.getElementById('dropdown-local-username');
        const dropdownServiceUsername = document.getElementById('dropdown-service-username');
        const dropdownElectronUsername = document.getElementById('dropdown-electron-username');
        const electronAccessStatus = document.getElementById('electron-access-status');

        if (this.userInfo && this.userInfo.local_username) {
            // 主显示区域 - 显示登录的账号
            if (userNameElement) {
                userNameElement.textContent = this.userInfo.local_username;
            }

            if (serviceUsernameElement) {
                serviceUsernameElement.textContent = this.userInfo.service_username || '--';
            }

            if (electronUsernameElement) {
                electronUsernameElement.textContent = this.userInfo.electron_username || '--';
            }

            // 平台标识和头像颜色 - 将平台文字显示在avatar中
            if (userAvatarElement && userAvatarContainer) {
                if (this.userInfo.platform === 'aviation') {
                    userAvatarElement.textContent = '航空';
                    userAvatarContainer.className = 'avatar avatar-sm bg-primary text-white';
                } else if (this.userInfo.platform === 'engine') {
                    userAvatarElement.textContent = '航发';
                    userAvatarContainer.className = 'avatar avatar-sm bg-success text-white';
                } else {
                    // 默认显示用户名首字母
                    userAvatarElement.textContent = this.userInfo.local_username.charAt(0).toUpperCase();
                    userAvatarContainer.className = 'avatar avatar-sm bg-secondary text-white';
                }
            }

            // 下拉菜单详细信息
            if (dropdownLocalUsername) {
                dropdownLocalUsername.textContent = this.userInfo.local_username;
            }

            if (dropdownServiceUsername) {
                dropdownServiceUsername.textContent = this.userInfo.service_username || '未获取';
            }

            if (dropdownElectronUsername) {
                dropdownElectronUsername.textContent = this.userInfo.electron_username || '未获取';
            }

            // ELECTRON访问状态
            if (electronAccessStatus) {
                if (this.userInfo.electron_access) {
                    electronAccessStatus.innerHTML = '<span class="badge bg-success">可用</span>';
                } else {
                    electronAccessStatus.innerHTML = '<span class="badge bg-danger">不可用</span>';
                }
            }
        }
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        const alertClass = {
            'success': 'alert-success',
            'error': 'alert-danger',
            'warning': 'alert-warning',
            'info': 'alert-info'
        }[type] || 'alert-info';

        const notification = document.createElement('div');
        notification.className = `alert ${alertClass} alert-dismissible fade show`;
        notification.style.cssText = 'min-width: 300px; max-width: 400px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const container = document.getElementById('notification-container');

        // 将新通知插入到容器的开头（由于使用了column-reverse，会显示在底部）
        container.insertBefore(notification, container.firstChild);

        // 限制通知数量，最多显示5个
        const notifications = container.querySelectorAll('.alert');
        if (notifications.length > 5) {
            const oldestNotification = notifications[notifications.length - 1];
            this.removeNotificationWithAnimation(oldestNotification);
        }

        // 自动移除通知
        setTimeout(() => {
            if (notification.parentNode) {
                this.removeNotificationWithAnimation(notification);
            }
        }, 5000);
    }

    /**
     * 带动画移除通知
     */
    removeNotificationWithAnimation(notification) {
        if (!notification || !notification.parentNode) return;

        notification.classList.add('removing');

        // 等待动画完成后移除元素
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }

    /**
     * 获取认证头
     */
    getAuthHeaders() {
        return {
            'Authorization': `Bearer ${this.token}`,
            'Content-Type': 'application/json'
        };
    }
}

// 创建全局认证管理器实例
const authManager = new AuthManager();

// 登录表单处理
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            // 获取选中的平台
            const selectedPlatform = document.querySelector('input[name="platform"]:checked');
            if (!selectedPlatform) {
                authManager.showNotification('请选择登录平台', 'warning');
                return;
            }

            const loginData = {
                username: formData.get('username'),
                password: formData.get('password'),
                platform: selectedPlatform.value,
                remember_password: formData.get('remember_password') === 'on'
            };

            await authManager.login(loginData);
        });
    }

    // 监听登录模态框显示事件，更新已保存账号列表
    const loginModal = document.getElementById('login-modal');
    if (loginModal) {
        loginModal.addEventListener('shown.bs.modal', function() {
            if (typeof accountManager !== 'undefined') {
                accountManager.updateSavedAccountsDropdown();
            }
        });
    }
});

// 全局函数
function logout() {
    authManager.logout();
}