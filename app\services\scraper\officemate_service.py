"""
欧菲斯平台数据采集服务
参考齐心平台实现，为欧菲斯平台(https://www.ofs.cn/)提供完整的数据采集功能
使用统一Playwright管理器模块，支持302重定向处理
"""
import asyncio
import random
from typing import Dict, List, Any
import requests
import aiohttp
from lxml import html
from app.core.logging import logger
from app.core.config import get_settings
from .playwright_manager import PlaywrightManager


class OfficemateScraperService:
    """欧菲斯平台数据采集服务类"""

    def __init__(self):
        # 获取配置实例
        self.settings = get_settings()

        # 基础配置 - 欧菲斯平台URL模板
        self.base_url = "https://www.ofs.cn/gallery.html?scontent=n,{sku}"

        # XPath选择器配置 - 根据实际HTML结构分析更新（已通过调试验证）
        self.xpath_product_name = '//*[@id="product_container"]/div[1]/h2/text()'
        # 品牌：使用Meta标签策略（简化版本）
        self.xpath_product_ventor = '//meta[@property="og:product:brand"]/@content'
        # 计量单位：使用调试验证的正确XPath
        self.xpath_product_unit = '//span[contains(text(), "单位")]/parent::td/following-sibling::td/span/text()'
        # 型号规格：使用调试验证的正确XPath
        self.xpath_product_model = '//span[contains(text(), "型号")]/parent::td/following-sibling::td/span/text()'
        # 价格字段XPath选择器 - 使用Meta标签策略（简化版本）
        self.xpath_product_price = '//meta[@property="og:product:price"]/@content'

        # 请求配置
        self.min_delay = 2.0
        self.max_delay = 3.0
        self.request_timeout = 30
        self.max_retries = 1  # 移除自动重试，仅保留手动重试功能
        self.concurrent_threads = 4  # 欧菲斯平台：4个并发线程

        # User-Agent配置
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ]

        # 初始化session
        self.session = requests.Session()
        self.semaphore = None

        # 初始化统一Playwright管理器
        self.playwright_manager = PlaywrightManager(
            platform_name="欧菲斯",
            base_domain=".ofs.cn"
        )

        # 设置基础请求头
        self._setup_base_headers()

    def _setup_base_headers(self):
        """设置基础请求头"""
        self.session.headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        })

    def get_random_delay(self):
        """获取随机延迟时间"""
        return random.uniform(self.min_delay, self.max_delay)

    def get_random_user_agent(self):
        """获取随机User-Agent"""
        return random.choice(self.user_agents)

    def set_client_ip_headers(self, client_ip: str):
        """
        设置客户端IP相关请求头

        Args:
            client_ip: 客户端IP地址
        """
        if client_ip and client_ip != "unknown":
            self.session.headers.update({
                'X-Forwarded-For': client_ip,
                'X-Real-IP': client_ip
            })
            logger.debug(f"欧菲斯平台设置IP请求头: X-Forwarded-For={client_ip}, X-Real-IP={client_ip}")

    def _clean_field_value(self, value: str, field_type: str) -> str:
        """
        清理字段值，移除不必要的前缀文本
        Args:
            value: 原始字段值
            field_type: 字段类型 ('brand', 'model', 或 'price')
        Returns:
            清理后的字段值
        """
        if not value or value.strip() == '':
            return value

        cleaned_value = value.strip()

        # 清理品牌字段的"品牌："前缀
        if field_type == 'brand':
            prefixes_to_remove = ['品牌：', '品牌:', '品牌 :', '品牌 ：']
            for prefix in prefixes_to_remove:
                if cleaned_value.startswith(prefix):
                    cleaned_value = cleaned_value[len(prefix):].strip()
                    break

        # 清理规格参数字段的"商品型号："前缀
        elif field_type == 'model':
            prefixes_to_remove = ['商品型号：', '商品型号:', '商品型号 :', '商品型号 ：', '型号：', '型号:', '型号 :', '型号 ：']
            for prefix in prefixes_to_remove:
                if cleaned_value.startswith(prefix):
                    cleaned_value = cleaned_value[len(prefix):].strip()
                    break

        # 清理价格字段的"￥"符号
        elif field_type == 'price':
            # 移除￥符号
            if cleaned_value.startswith('￥'):
                cleaned_value = cleaned_value[1:].strip()

            # 移除其他可能的价格前缀
            price_prefixes = ['价格：', '价格:', '售价：', '售价:', '¥', '元']
            for prefix in price_prefixes:
                if cleaned_value.startswith(prefix):
                    cleaned_value = cleaned_value[len(prefix):].strip()
                    break

            # 移除价格后缀
            price_suffixes = ['元', '元/个', '/个', '/件', '/套']
            for suffix in price_suffixes:
                if cleaned_value.endswith(suffix):
                    cleaned_value = cleaned_value[:-len(suffix)].strip()
                    break

        return cleaned_value if cleaned_value else value

    def _parse_query_content(self, query_content: str) -> List[str]:
        """解析查询内容，提取SKU列表（保持用户输入顺序）"""
        if not query_content:
            return []

        # 支持多种分隔符：空格、逗号、换行符
        import re
        skus = re.split(r'[,\s\n]+', query_content.strip())
        # 过滤空字符串并去重，但保持输入顺序
        seen = set()
        unique_skus = []
        for sku in skus:
            sku = sku.strip()
            if sku and sku not in seen:
                seen.add(sku)
                unique_skus.append(sku)

        logger.debug(f"解析查询内容，提取到 {len(unique_skus)} 个SKU（保持输入顺序）: {unique_skus}")
        return unique_skus

    async def scrape_data(self, query_content: str, playwright_mode: bool = None) -> Dict[str, Any]:
        """
        采集欧菲斯平台数据
        Args:
            query_content: 查询内容（SKU列表）
            playwright_mode: 是否使用Playwright模式（已废弃，由配置文件控制）
        """
        try:
            # 从配置文件读取Playwright模式设置
            use_playwright = self.settings.OFFICEMATE_USE_PLAYWRIGHT
            logger.info(f"开始采集欧菲斯平台数据 - Playwright模式: {use_playwright}")

            skus = self._parse_query_content(query_content)

            if not skus:
                return {
                    "success": False,
                    "message": "未找到有效的SKU编码",
                    "data": [],
                    "total": 0,
                    "platform": "欧菲斯",
                    "query_content": query_content
                }

            # 根据配置选择采集方式
            if use_playwright:
                data = await self._scrape_with_playwright_mode(skus)
            else:
                data = await self._scrape_with_direct_mode(skus)

            success_count = len([item for item in data if item.get('status') == 'success'])
            mode_text = "Playwright模式" if use_playwright else "直接模式"

            result = {
                "success": True,
                "message": f"采集完成，成功 {success_count}/{len(data)} 条记录（{mode_text}）",
                "data": data,
                "total": len(data),
                "platform": "欧菲斯",
                "query_content": query_content,
                "mode": mode_text,
                "success_count": success_count,
                "total_count": len(data)
            }

            logger.info(f"✅ 欧菲斯平台数据采集完成 - 成功: {success_count}/{len(data)} 条记录")
            return result

        except Exception as e:
            logger.error(f"❌ 欧菲斯平台数据采集失败: {str(e)}")
            return {
                "success": False,
                "message": f"欧菲斯平台数据采集失败: {str(e)}",
                "data": [],
                "total": 0,
                "platform": "欧菲斯",
                "query_content": query_content
            }

    async def _scrape_with_playwright_mode(self, skus: List[str]) -> List[Dict]:
        """
        Playwright模式：使用统一Playwright管理器
        - Playwright仅用于获取Cookie（一次性初始化）
        - 后续所有数据采集使用Requests + Cookie
        - 支持Cookie失效自动刷新
        """
        logger.info("使用Playwright模式进行数据采集（统一管理器模式）...")

        # 智能Cookie管理：检查并在需要时刷新Cookie
        target_url = "https://www.ofs.cn/gallery.html?scontent=n,"
        success = await self.playwright_manager.refresh_cookies_if_needed(
            target_url=target_url,
            session_obj=self.session,
            test_url=target_url
        )
        if not success:
            logger.error("Cookie初始化/刷新失败，返回错误结果")
            return [self._create_error_result(sku, '无法获取或刷新cookies') for sku in skus]

        # 使用Requests进行批量采集（带Cookie）
        logger.info("Cookie已就绪，使用Requests进行数据采集...")
        results = await self._scrape_multiple_with_requests(skus, use_cookies=True)

        # 检查结果中是否有大量失败，可能是Cookie失效
        failed_count = sum(1 for r in results if r.get('status') == 'error')
        if failed_count > len(skus) * 0.5:  # 如果失败率超过50%
            logger.warning(f"检测到高失败率({failed_count}/{len(skus)})，可能是Cookie失效，尝试刷新Cookie...")

            # 强制刷新Cookie
            self.playwright_manager.reset_cookies()
            refresh_success = await self.playwright_manager.initialize_cookies(
                target_url=target_url,
                session_obj=self.session
            )

            if refresh_success:
                logger.info("Cookie刷新成功，重新采集失败的SKU...")
                # 重新采集失败的SKU
                failed_skus = [r['sku_number'] for r in results if r.get('status') == 'error']
                if failed_skus:
                    retry_results = await self._scrape_multiple_with_requests(failed_skus, use_cookies=True)
                    # 更新结果
                    for i, result in enumerate(results):
                        if result.get('status') == 'error':
                            sku = result['sku_number']
                            retry_result = next((r for r in retry_results if r['sku_number'] == sku), None)
                            if retry_result and retry_result.get('status') == 'success':
                                results[i] = retry_result
                                logger.info(f"SKU {sku} 重新采集成功")

        return results

    async def _scrape_with_direct_mode(self, skus: List[str]) -> List[Dict]:
        """直接模式：跳过cookie获取，直接用Requests进行数据采集"""
        return await self._scrape_multiple_with_requests(skus, use_cookies=False)

    async def _scrape_multiple_with_requests(self, skus: List[str], use_cookies: bool = True) -> List[Dict]:
        """
        批量爬取多个商品信息 - 并发优化版本（保持输入顺序）
        """
        total = len(skus)

        # 创建并发控制信号量
        self.semaphore = asyncio.Semaphore(self.concurrent_threads)

        mode_text = "Playwright模式" if use_cookies else "直接模式"
        logger.info(f"开始并发处理 {total} 个SKU，模式: {mode_text}，并发数: {self.concurrent_threads}")

        # 创建结果数组，保持输入顺序
        results = [None] * total

        # 定义单个SKU处理任务
        async def process_single_sku(index: int, sku: str):
            async with self.semaphore:  # 控制并发数量
                try:
                    # 处理单个SKU
                    result = await self._scrape_single_product(sku, use_cookies)
                    result['index'] = index  # 添加索引信息
                    result['original_order'] = index  # 保存原始顺序

                    # 将结果放入正确的位置
                    results[index] = result

                    logger.info(f"SKU {sku} 处理完成 (位置 {index + 1}/{total}): {result['status']}")

                except Exception as e:
                    error_result = self._create_error_result(sku, f'处理异常: {str(e)}')
                    error_result['index'] = index
                    error_result['original_order'] = index
                    results[index] = error_result

                    logger.error(f"SKU {sku} 处理异常 (位置 {index + 1}/{total}): {str(e)}")

        # 创建所有任务
        tasks = [process_single_sku(i, sku) for i, sku in enumerate(skus)]

        # 并发执行所有任务
        await asyncio.gather(*tasks)

        # 确保所有结果都已填充
        for i, result in enumerate(results):
            if result is None:
                results[i] = self._create_error_result(skus[i], '未知错误')
                results[i]['index'] = i
                results[i]['original_order'] = i

        success_count = sum(1 for r in results if r['status'] == 'success')
        logger.info(f"并发处理完成，成功: {success_count}/{total}")

        return results

    async def _scrape_single_product(self, sku: str, use_cookies: bool = True) -> Dict:
        """
        爬取单个商品信息 - 处理302重定向并使用XPath选择器直接解析
        """
        url = self.base_url.format(sku=sku)
        mode_text = "Playwright模式" if use_cookies else "直接模式"

        logger.info(f"SKU {sku} - 开始数据采集 ({mode_text})")

        try:
            # 智能延迟控制
            delay = self.get_random_delay()
            logger.debug(f"SKU {sku} - 请求, 延迟 {delay:.2f}秒 ({mode_text})")
            await asyncio.sleep(delay)

            # 动态设置请求头
            request_headers = self.session.headers.copy()
            request_headers['User-Agent'] = self.get_random_user_agent()

            # 使用异步aiohttp进行请求
            timeout = aiohttp.ClientTimeout(total=self.request_timeout)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                # 根据模式选择cookies策略
                cookies = None
                if use_cookies and hasattr(self.session, 'cookies'):
                    # 将requests cookies转换为aiohttp格式
                    cookies = {cookie.name: cookie.value for cookie in self.session.cookies}

                async with session.get(
                    url,
                    headers=request_headers,
                    cookies=cookies,
                    allow_redirects=True  # 允许302重定向
                ) as response:
                    response_status = response.status
                    response_content = await response.read()

            if response_status == 200:
                # 获取最终重定向后的URL
                final_url = str(response.url)
                logger.debug(f"SKU {sku} - 重定向到最终URL: {final_url}")

                # 验证响应内容质量
                if len(response_content) < 1000:
                    logger.warning(f"SKU {sku} - 响应内容过短，可能页面未完全加载")
                    return self._create_error_result(sku, '响应内容过短')

                # 解析HTML内容
                try:
                    tree = html.fromstring(response_content)
                except Exception as parse_error:
                    logger.warning(f"SKU {sku} - HTML解析失败: {str(parse_error)}")
                    return self._create_error_result(sku, f'HTML解析失败: {str(parse_error)}')

                # 验证页面是否包含商品信息
                page_title = tree.xpath('//title/text()')
                if page_title and ('404' in page_title[0] or '错误' in page_title[0] or 'error' in page_title[0].lower()):
                    return self._create_error_result(sku, '商品页面不存在')

                # 使用XPath选择器提取数据
                product_data = self._extract_product_data(tree, sku, final_url)

                # 验证关键字段是否成功提取
                if product_data['product_name'] != '获取失败' and product_data['product_price'] != '暂无价格信息':
                    logger.debug(f"SKU {sku} - 数据采集成功: {product_data['product_name']}, 价格: {product_data['product_price']}")
                    return product_data
                elif product_data['product_name'] != '获取失败':
                    # 产品名称成功但价格失败，仍然返回结果但记录警告
                    logger.warning(f"SKU {sku} - 产品信息采集成功但价格提取失败: {product_data['product_name']}")
                    return product_data
                else:
                    logger.warning(f"SKU {sku} - 未找到商品信息")
                    return self._create_error_result(sku, '未找到商品信息')

            elif response_status == 404:
                return self._create_error_result(sku, '商品不存在')
            else:
                logger.warning(f"SKU {sku} - HTTP错误: {response_status}")
                return self._create_error_result(sku, f'HTTP错误: {response_status}')

        except Exception as e:
            logger.warning(f"SKU {sku} - 采集异常: {str(e)}")
            return self._create_error_result(sku, f'采集异常: {str(e)}')

    def _extract_product_data(self, tree, sku: str, url: str) -> Dict:
        """
        使用XPath选择器提取商品数据 - 更新为实际HTML结构
        """
        try:
            # 提取产品名称
            product_name_elements = tree.xpath(self.xpath_product_name)
            product_name = product_name_elements[0].strip() if product_name_elements else '获取失败'

            # 提取品牌 - 使用Meta标签策略（简化版本）
            product_ventor_elements = tree.xpath(self.xpath_product_ventor)
            product_ventor = product_ventor_elements[0].strip() if product_ventor_elements else '欧菲斯'

            # 提取计量单位 - 从详细表格获取
            product_unit_elements = tree.xpath(self.xpath_product_unit)
            product_unit = product_unit_elements[0].strip() if product_unit_elements else '个'

            # 提取型号规格 - 从详细表格获取
            product_model_elements = tree.xpath(self.xpath_product_model)
            raw_product_model = product_model_elements[0].strip() if product_model_elements else '-'
            product_model = self._clean_field_value(raw_product_model, 'model')

            # 提取售价 - 使用Meta标签策略（简化版本）
            product_price_elements = tree.xpath(self.xpath_product_price)
            raw_product_price = product_price_elements[0].strip() if product_price_elements else '暂无价格信息'
            product_price = self._clean_field_value(raw_product_price, 'price')

            # 返回完整的9字段结构
            return {
                'sku_number': sku,
                'product_name': product_name,
                'product_ventor': product_ventor,
                'url': url,
                'product_unit': product_unit,
                'product_model': product_model,
                'Null1': '-',
                'Null2': '-',
                'product_price': product_price,
                'platform': '欧菲斯',
                'status': 'success'
            }

        except Exception as e:
            logger.error(f"SKU {sku} - 数据提取异常: {str(e)}")
            return self._create_error_result(sku, f'数据提取异常: {str(e)}')



    def _create_error_result(self, sku: str, error_message: str) -> Dict:
        """创建错误结果 - 9字段结构"""
        return {
            'sku_number': sku,
            'product_name': '获取失败',
            'product_ventor': '获取失败',
            'url': self.base_url.format(sku=sku),
            'product_unit': '获取失败',
            'product_model': '获取失败',
            'Null1': '-',
            'Null2': '-',
            'product_price': '获取失败',
            'platform': '欧菲斯',
            'status': 'error',
            'error': error_message
        }

    async def scrape_data_stream(self, query_content: str, playwright_mode: bool = None):
        """
        流式数据采集 - 实时返回每个SKU的采集结果
        """
        try:
            # 从配置文件读取Playwright模式设置
            use_playwright = self.settings.OFFICEMATE_USE_PLAYWRIGHT
            logger.info(f"开始流式采集欧菲斯平台数据 - Playwright模式: {use_playwright}")

            skus = self._parse_query_content(query_content)

            if not skus:
                yield {
                    "type": "error",
                    "message": "未找到有效的SKU编码"
                }
                return

            total = len(skus)

            # 根据配置选择采集方式
            if use_playwright:
                # 确保cookies已初始化
                if not self.playwright_manager.cookies_initialized:
                    yield {
                        "type": "progress",
                        "message": "正在初始化Playwright cookies...",
                        "current": 0,
                        "total": total
                    }

                    success = await self.playwright_manager.initialize_cookies(
                        target_url="https://www.ofs.cn/gallery.html?scontent=n,",
                        session_obj=self.session
                    )
                    if not success:
                        yield {
                            "type": "error",
                            "message": "无法获取初始cookies"
                        }
                        return

            # 创建并发控制信号量
            self.semaphore = asyncio.Semaphore(self.concurrent_threads)

            # 结果收集器，保持输入顺序
            results = [None] * total
            completed_count = 0

            # 定义单个SKU处理任务（简化版本）
            async def process_single_sku_simple(index: int, sku: str):
                nonlocal completed_count

                async with self.semaphore:
                    try:
                        # 处理单个SKU
                        result = await self._scrape_single_product(sku, use_playwright)
                        result['index'] = index
                        result['original_order'] = index

                        # 保存结果
                        results[index] = result
                        completed_count += 1

                        logger.info(f"流式处理完成 SKU {sku} (位置 {index + 1}/{total}): {result['status']}")

                        return {
                            "type": "result",
                            "data": result,
                            "current": completed_count,
                            "total": total,
                            "index": index
                        }

                    except Exception as e:
                        error_result = self._create_error_result(sku, f'处理异常: {str(e)}')
                        error_result['index'] = index
                        error_result['original_order'] = index
                        results[index] = error_result
                        completed_count += 1

                        logger.error(f"流式处理异常 SKU {sku} (位置 {index + 1}/{total}): {str(e)}")

                        return {
                            "type": "result",
                            "data": error_result,
                            "current": completed_count,
                            "total": total,
                            "index": index
                        }

            # 创建所有任务
            tasks = [asyncio.create_task(process_single_sku_simple(i, sku)) for i, sku in enumerate(skus)]

            # 实时流式处理：使用队列确保按完成顺序实时发送，但保持索引正确性
            pending_tasks = set(tasks)

            while pending_tasks:
                # 等待任何一个任务完成
                done, pending_tasks = await asyncio.wait(pending_tasks, return_when=asyncio.FIRST_COMPLETED)

                for task in done:
                    try:
                        task_result = await task
                        # 立即发送结果，保持索引与SKU的正确对应关系
                        yield task_result
                        logger.debug(f"✅ 流式发送结果 - 索引: {task_result['index']}, SKU: {task_result['data']['sku_number']}")
                    except Exception as e:
                        logger.error(f"流式任务执行异常: {str(e)}")
                        # 继续处理其他任务

            # 发送完成消息
            success_count = sum(1 for r in results if r and r['status'] == 'success')
            yield {
                "type": "complete",
                "message": f"流式采集完成，成功 {success_count}/{total} 条记录",
                "success_count": success_count,
                "total_count": total,
                "results": results
            }

        except Exception as e:
            logger.error(f"流式数据采集失败: {str(e)}")
            yield {
                "type": "error",
                "message": f"流式数据采集失败: {str(e)}"
            }

    async def scrape_single_sku(self, sku: str, playwright_mode: bool = None) -> Dict:
        """
        采集单个SKU数据（用于重试功能）
        """
        try:
            # 从配置文件读取Playwright模式设置
            use_playwright = self.settings.OFFICEMATE_USE_PLAYWRIGHT
            logger.info(f"开始采集单个SKU: {sku}, Playwright模式: {use_playwright} (配置驱动)")

            # 根据配置选择采集方式
            if use_playwright:
                # 确保cookies已初始化
                if not self.playwright_manager.cookies_initialized:
                    logger.info("单个SKU采集需要初始化cookies...")
                    success = await self.playwright_manager.initialize_cookies(
                        target_url="https://www.ofs.cn/gallery.html?scontent=n,",
                        session_obj=self.session
                    )
                    if not success:
                        return self._create_error_result(sku, '无法获取初始cookies')

                # 使用Playwright模式采集
                result = await self._scrape_single_product(sku, use_cookies=True)
            else:
                # 使用直接模式采集
                result = await self._scrape_single_product(sku, use_cookies=False)

            logger.info(f"单个SKU采集完成: {sku}, 状态: {result['status']}")
            return result

        except Exception as e:
            logger.error(f"单个SKU采集失败: {sku}, 错误: {str(e)}")
            return self._create_error_result(sku, f'采集异常: {str(e)}')

    def get_session_info(self) -> Dict:
        """获取当前session信息"""
        return {
            'cookies_initialized': self.playwright_manager.cookies_initialized,
            'cookies_count': len(self.session.cookies),
            'session_info': self.playwright_manager.get_session_info()
        }


# 创建全局实例
officemate_service = OfficemateScraperService()