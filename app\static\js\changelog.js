/**
 * 更新日志管理器
 * 负责加载和显示更新日志内容
 */

class ChangelogManager {
    constructor() {
        this.modal = null;
        this.loadingElement = null;
        this.contentElement = null;
        this.errorElement = null;
        this.isLoaded = false;
        this.changelogData = null;
        
        this.init();
    }

    /**
     * 初始化更新日志管理器
     */
    init() {
        // 获取DOM元素
        this.modal = document.getElementById('changelog-modal');
        this.loadingElement = document.getElementById('changelog-loading');
        this.contentElement = document.getElementById('changelog-content');
        this.errorElement = document.getElementById('changelog-error');

        // 绑定模态框事件
        if (this.modal) {
            this.modal.addEventListener('show.bs.modal', () => {
                this.onModalShow();
            });
        }
    }

    /**
     * 模态框显示时的处理
     */
    async onModalShow() {
        if (!this.isLoaded) {
            await this.loadChangelog();
        }
    }

    /**
     * 加载更新日志数据
     */
    async loadChangelog() {
        try {
            this.showLoading();

            const response = await fetch('/api/v1/system/changelog');
            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.message || '加载更新日志失败');
            }

            if (result.success && result.data) {
                this.changelogData = result.data;
                this.renderChangelog();
                this.isLoaded = true;
            } else {
                throw new Error('更新日志数据格式错误');
            }

        } catch (error) {
            console.error('加载更新日志失败:', error);
            this.showError();
        }
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        this.loadingElement.classList.remove('d-none');
        this.contentElement.classList.add('d-none');
        this.errorElement.classList.add('d-none');
    }

    /**
     * 显示错误状态
     */
    showError() {
        this.loadingElement.classList.add('d-none');
        this.contentElement.classList.add('d-none');
        this.errorElement.classList.remove('d-none');
    }

    /**
     * 渲染更新日志内容
     */
    renderChangelog() {
        if (!this.changelogData || !Array.isArray(this.changelogData)) {
            this.showError();
            return;
        }

        let html = '';

        this.changelogData.forEach((version, index) => {
            html += this.renderVersion(version, index === 0);
        });

        this.contentElement.innerHTML = html;
        this.loadingElement.classList.add('d-none');
        this.contentElement.classList.remove('d-none');
        this.errorElement.classList.add('d-none');
    }

    /**
     * 渲染单个版本
     */
    renderVersion(version, isLatest = false) {
        const badgeClass = isLatest ? 'bg-primary' : 'bg-secondary';
        const latestLabel = isLatest ? '<span class="badge bg-success ms-2">最新</span>' : '';

        let changesHtml = '';
        if (version.changes && Array.isArray(version.changes)) {
            changesHtml = version.changes.map(change => this.renderChange(change)).join('');
        }

        return `
            <div class="card mb-3">
                <div class="card-header">
                    <h3 class="card-title">
                        <span class="badge ${badgeClass}">${version.version}</span>
                        ${latestLabel}
                    </h3>
                </div>
                <div class="card-body">
                    ${changesHtml ? `<ul class="list-unstyled mb-0">${changesHtml}</ul>` : '<p class="text-muted">暂无更新记录</p>'}
                </div>
            </div>
        `;
    }

    /**
     * 渲染单个更新条目
     */
    renderChange(change) {
        const typeConfig = this.getChangeTypeConfig(change.type);

        return `
            <li>
                <span class="changelog-type-badge ${typeConfig.class}">${typeConfig.label}</span>
                <span class="flex-grow-1">${this.escapeHtml(change.content)}</span>
            </li>
        `;
    }

    /**
     * 获取更新类型配置
     */
    getChangeTypeConfig(type) {
        const configs = {
            'Added': {
                label: '新增',
                class: 'bg-success'
            },
            'Fixed': {
                label: '修复',
                class: 'bg-warning'
            },
            'Changed': {
                label: '变更',
                class: 'bg-info'
            },
            'Improved': {
                label: '优化',
                class: 'bg-cyan'
            },
            'Removed': {
                label: '删除',
                class: 'bg-danger'
            }
        };

        return configs[type] || {
            label: '其他',
            class: 'bg-secondary'
        };
    }

    /**
     * HTML转义
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 重新加载更新日志
     */
    async reload() {
        this.isLoaded = false;
        this.changelogData = null;
        await this.loadChangelog();
    }
}

// 创建全局实例
let changelogManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    changelogManager = new ChangelogManager();
    console.log('更新日志管理器已初始化');
});
