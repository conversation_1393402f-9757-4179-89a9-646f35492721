"""
领先未来(新)平台数据采集服务
基于API接口直接获取JSON数据，不使用XPath解析
目标网站：https://www.lxwlmall.com/detail?skuNo={SKU}
数据API：https://www.lxwlmall.com/api/mainsite-api-service/mainsite/{SKU}/1/sku-info
"""

import asyncio
import random
import requests
import aiohttp
from typing import Dict, List, Any, AsyncGenerator
from lxml import html
import json
from app.core.logging import logger
from app.core.config import get_settings
from app.services.scraper.playwright_manager import PlaywrightManager


class LxwlNewService:
    """领先未来(新)平台数据采集服务类"""
    
    def __init__(self):
        # 获取配置实例
        self.settings = get_settings()

        # 基础配置 - 领先未来(新)平台URL模板
        self.base_url = "https://www.lxwlmall.com/detail?skuNo={sku}"
        self.api_url = "https://www.lxwlmall.com/api/mainsite-api-service/mainsite/{sku}/1/sku-info"
        
        # 请求配置
        self.min_delay = 0.5  # 减少延迟以提高并发效果
        self.max_delay = 1.0
        self.request_timeout = 30
        self.max_retries = 1  # 移除自动重试，仅保留手动重试功能
        self.concurrent_threads = 5  # 领先未来(新)平台优化：3个并发线程
        
        # 初始化requests session
        self.session = requests.Session()
        self.session.headers.update({
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Referer': 'https://www.lxwlmall.com/',
            'Origin': 'https://www.lxwlmall.com'
        })
        
        # 初始化Playwright管理器（用于获取cookies）
        self.playwright_manager = PlaywrightManager(
            platform_name="领先未来(新)",
            base_domain="lxwlmall.com"
        )

    def get_random_delay(self) -> float:
        """获取随机延迟时间"""
        return random.uniform(self.min_delay, self.max_delay)

    def get_random_user_agent(self) -> str:
        """获取随机User-Agent"""
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
        return random.choice(user_agents)

    def set_client_ip_headers(self, client_ip: str):
        """
        设置客户端IP相关请求头

        Args:
            client_ip: 客户端IP地址
        """
        if client_ip and client_ip != "unknown":
            # 为requests session设置IP头部
            self.session.headers.update({
                'X-Forwarded-For': client_ip,
                'X-Real-IP': client_ip
            })
            # 存储IP用于aiohttp会话
            self.client_ip = client_ip
            logger.debug(f"领先未来(新)平台设置IP请求头: X-Forwarded-For={client_ip}, X-Real-IP={client_ip}")
        else:
            self.client_ip = None

    def _clean_field_value(self, value: str, field_type: str = 'general') -> str:
        """
        清理字段值，移除常见的前缀和后缀
        Args:
            value: 原始字段值
            field_type: 字段类型 ('brand', 'model', 'general')
        Returns:
            清理后的字段值
        """
        if not value or value.strip() == '':
            return ''

        cleaned_value = value.strip()
        
        # 清理品牌字段的"品牌："前缀
        if field_type == 'brand':
            prefixes_to_remove = ['品牌：', '品牌:', '品牌 :', '品牌 ：', '厂商：', '厂商:', '厂商 :', '厂商 ：']
            for prefix in prefixes_to_remove:
                if cleaned_value.startswith(prefix):
                    cleaned_value = cleaned_value[len(prefix):].strip()
                    break
        
        # 清理规格参数字段的"商品型号："前缀
        elif field_type == 'model':
            prefixes_to_remove = ['商品型号：', '商品型号:', '商品型号 :', '商品型号 ：', '型号：', '型号:', '型号 :', '型号 ：']
            for prefix in prefixes_to_remove:
                if cleaned_value.startswith(prefix):
                    cleaned_value = cleaned_value[len(prefix):].strip()
                    break
        
        return cleaned_value if cleaned_value else value

    def _parse_query_content(self, query_content: str) -> List[str]:
        """解析查询内容，提取SKU列表（保持用户输入顺序）"""
        if not query_content:
            return []

        # 支持多种分隔符：空格、逗号、换行符
        import re
        skus = re.split(r'[,\s\n]+', query_content.strip())
        # 过滤空字符串并去重，但保持输入顺序
        seen = set()
        unique_skus = []
        for sku in skus:
            sku = sku.strip()
            if sku and sku not in seen:
                seen.add(sku)
                unique_skus.append(sku)

        logger.info(f"解析查询内容完成，共提取 {len(unique_skus)} 个SKU")
        return unique_skus

    async def scrape_data(self, query_content: str, playwright_mode: bool = None) -> Dict[str, Any]:
        """
        采集领先未来(新)平台数据
        Args:
            query_content: 查询内容（SKU列表）
            playwright_mode: 是否使用Playwright模式（已废弃，由配置文件控制）
        """
        try:
            # 从配置文件读取Playwright模式设置
            use_playwright = getattr(self.settings, 'LXWL_NEW_USE_PLAYWRIGHT', False)
            logger.info(f"开始采集领先未来(新)平台数据 - Playwright模式: {use_playwright}")

            skus = self._parse_query_content(query_content)

            if not skus:
                return {
                    "success": False,
                    "message": "未找到有效的SKU编码",
                    "data": [],
                    "total": 0,
                    "platform": "领先未来(新)",
                    "query_content": query_content
                }

            # 根据配置选择采集方式
            if use_playwright:
                data = await self._scrape_with_playwright_mode(skus)
            else:
                data = await self._scrape_with_direct_mode(skus)

            # 统计结果
            success_count = sum(1 for item in data if item.get('status') == 'success')
            total_count = len(data)

            return {
                "success": True,
                "message": f"数据采集完成，成功: {success_count}/{total_count}",
                "data": data,
                "total": total_count,
                "success_count": success_count,
                "failed_count": total_count - success_count,
                "platform": "领先未来(新)",
                "query_content": query_content
            }

        except Exception as e:
            logger.error(f"领先未来(新)平台数据采集异常: {str(e)}")
            return {
                "success": False,
                "message": f"数据采集异常: {str(e)}",
                "data": [],
                "total": 0,
                "platform": "领先未来(新)",
                "query_content": query_content
            }

    async def _scrape_with_playwright_mode(self, skus: List[str]) -> List[Dict]:
        """
        Playwright模式：使用统一Playwright管理器
        - Playwright仅用于获取Cookie（一次性初始化）
        - 后续所有数据采集使用Requests + Cookie
        - 支持Cookie失效自动刷新
        """
        logger.info("使用Playwright模式进行数据采集（统一管理器模式）...")

        # 智能Cookie管理：检查并在需要时刷新Cookie
        target_url = "https://www.lxwlmall.com"
        success = await self.playwright_manager.refresh_cookies_if_needed(
            target_url=target_url,
            session_obj=self.session,
            test_url=target_url
        )
        if not success:
            logger.error("Cookie初始化/刷新失败，返回错误结果")
            return [self._create_error_result(sku, '无法获取或刷新cookies') for sku in skus]

        # 使用Requests进行批量采集（带Cookie）
        logger.info("Cookie已就绪，使用Requests进行数据采集...")
        results = await self._scrape_multiple_with_requests(skus, use_cookies=True)

        # 检查结果中是否有大量失败，可能是Cookie失效
        failed_count = sum(1 for r in results if r.get('status') == 'error')
        if failed_count > len(skus) * 0.5:  # 如果失败率超过50%
            logger.warning(f"检测到高失败率({failed_count}/{len(skus)})，可能是Cookie失效，尝试刷新Cookie...")

            # 强制刷新Cookie
            self.playwright_manager.reset_cookies()
            refresh_success = await self.playwright_manager.initialize_cookies(
                target_url=target_url,
                session_obj=self.session
            )

            if refresh_success:
                logger.info("Cookie刷新成功，重新采集失败的SKU...")
                # 重新采集失败的SKU
                failed_skus = [r['sku_number'] for r in results if r.get('status') == 'error']
                if failed_skus:
                    retry_results = await self._scrape_multiple_with_requests(failed_skus, use_cookies=True)
                    # 更新结果
                    for i, result in enumerate(results):
                        if result.get('status') == 'error':
                            sku = result['sku_number']
                            retry_result = next((r for r in retry_results if r['sku_number'] == sku), None)
                            if retry_result and retry_result.get('status') == 'success':
                                results[i] = retry_result
                                logger.info(f"SKU {sku} 重新采集成功")

        return results

    async def _scrape_with_direct_mode(self, skus: List[str]) -> List[Dict]:
        """
        直接模式：仅使用Requests进行数据采集
        """
        logger.info("使用直接模式进行数据采集...")
        return await self._scrape_multiple_with_requests(skus, use_cookies=False)

    async def _scrape_multiple_with_requests(self, skus: List[str], use_cookies: bool = True) -> List[Dict]:
        """
        使用Requests并发采集多个SKU数据
        """
        total = len(skus)
        logger.info(f"开始并发采集 {total} 个SKU数据...")

        # 创建信号量控制并发数
        semaphore = asyncio.Semaphore(self.concurrent_threads)

        # 初始化结果列表
        results = [None] * total

        async def process_single_sku(index: int, sku: str):
            """处理单个SKU的采集任务"""
            logger.info(f"🚀 并发任务 {index+1}/{total} (SKU {sku}) 等待信号量...")
            async with semaphore:
                logger.info(f"▶️ 并发任务 {index+1}/{total} (SKU {sku}) 开始执行")
                try:
                    result = await self._scrape_single_product(sku, use_cookies)
                    result['index'] = index
                    result['original_order'] = index
                    results[index] = result
                    logger.info(f"✅ 并发任务 {index+1}/{total} (SKU {sku}) 处理完成 - 状态: {result.get('status')}")
                except Exception as e:
                    logger.error(f"❌ 并发任务 {index+1}/{total} (SKU {sku}) 处理异常: {str(e)}")
                    error_result = self._create_error_result(sku, f'处理异常: {str(e)}')
                    error_result['index'] = index
                    error_result['original_order'] = index
                    results[index] = error_result

        # 创建所有任务
        tasks = [process_single_sku(i, sku) for i, sku in enumerate(skus)]

        # 并发执行所有任务
        await asyncio.gather(*tasks)

        # 确保所有结果都已填充
        for i, result in enumerate(results):
            if result is None:
                results[i] = self._create_error_result(skus[i], '未知错误')
                results[i]['index'] = i
                results[i]['original_order'] = i

        success_count = sum(1 for r in results if r['status'] == 'success')
        logger.info(f"并发处理完成，成功: {success_count}/{total}")

        return results

    async def _scrape_single_product(self, sku: str, use_cookies: bool = True) -> Dict:
        """
        爬取单个商品信息 - 使用API接口直接获取JSON数据
        """
        api_url = self.api_url.format(sku=sku)
        detail_url = self.base_url.format(sku=sku)
        mode_text = "Playwright模式" if use_cookies else "直接模式"

        logger.info(f"SKU {sku} - 开始数据采集 ({mode_text})")

        try:
            # 智能延迟控制
            delay = self.get_random_delay()
            logger.debug(f"SKU {sku} - API请求, 延迟 {delay:.2f}秒 ({mode_text})")
            await asyncio.sleep(delay)

            # 动态设置请求头
            request_headers = self.session.headers.copy()
            request_headers['User-Agent'] = self.get_random_user_agent()

            # 添加客户端IP头部（如果已设置）
            if hasattr(self, 'client_ip') and self.client_ip:
                request_headers['X-Forwarded-For'] = self.client_ip
                request_headers['X-Real-IP'] = self.client_ip

            # 使用异步aiohttp进行请求
            timeout = aiohttp.ClientTimeout(total=self.request_timeout)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                # 根据模式选择cookies策略
                cookies = None
                if use_cookies and hasattr(self.session, 'cookies'):
                    # 将requests cookies转换为aiohttp格式
                    cookies = {cookie.name: cookie.value for cookie in self.session.cookies}

                async with session.get(
                    api_url,
                    headers=request_headers,
                    cookies=cookies,
                    allow_redirects=True
                ) as response:
                    response_status = response.status
                    response_text = await response.text()

            if response_status == 200:
                # 解析JSON响应
                try:
                    json_data = json.loads(response_text)
                    product_data = self._extract_product_data_from_json(json_data, sku, detail_url)

                    if product_data['product_name'] != '获取失败':
                        logger.debug(f"SKU {sku} - 数据采集成功: {product_data['product_name']}")
                        return product_data
                    else:
                        logger.warning(f"SKU {sku} - 未找到商品信息")
                        return self._create_error_result(sku, '未找到商品信息')

                except json.JSONDecodeError as e:
                    logger.error(f"SKU {sku} - JSON解析失败: {str(e)}")
                    return self._create_error_result(sku, f'JSON解析失败: {str(e)}')

            elif response_status == 404:
                return self._create_error_result(sku, '商品不存在')
            else:
                logger.warning(f"SKU {sku} - HTTP错误: {response_status}")
                return self._create_error_result(sku, f'HTTP错误: {response_status}')

        except Exception as e:
            logger.warning(f"SKU {sku} - 采集异常: {str(e)}")
            return self._create_error_result(sku, f'采集异常: {str(e)}')

    def _extract_product_data_from_json(self, json_data: Dict, sku: str, url: str) -> Dict:
        """
        从API响应的JSON数据中提取商品数据
        数据映射关系：
        - primaryAttributeValue -> product_model
        - websitePrice -> product_price
        - brandName -> product_ventor
        - unit -> product_unit
        - name -> product_name
        """
        try:
            # 检查响应结构
            if not json_data or 'data' not in json_data:
                logger.warning(f"SKU {sku} - JSON响应格式异常，缺少data字段")
                return self._create_error_result(sku, 'JSON响应格式异常')

            data = json_data['data']
            if not data:
                logger.warning(f"SKU {sku} - data字段为空")
                return self._create_error_result(sku, '商品数据为空')

            # 提取各字段数据
            product_name = data.get('name', '获取失败')
            product_model = data.get('primaryAttributeValue', '-')
            product_ventor = data.get('brandName', '领先未来(新)')
            product_unit = data.get('unit', '个')
            website_price = data.get('websitePrice', 0)

            logger.debug(f"SKU {sku} - 原始数据: 名称='{product_name}', 型号='{product_model}', 品牌='{product_ventor}', 单位='{product_unit}', 价格={website_price}")

            # 处理价格格式
            if isinstance(website_price, (int, float)) and website_price > 0:
                product_price = f"{website_price:.2f}"
            else:
                product_price = '暂无价格信息'

            # 清理字段值
            product_name = self._clean_field_value(product_name, 'general')
            product_model = self._clean_field_value(product_model, 'model')
            product_ventor = self._clean_field_value(product_ventor, 'brand')
            product_unit = self._clean_field_value(product_unit, 'general')

            logger.debug(f"SKU {sku} - 清理后数据: 名称='{product_name}', 型号='{product_model}', 品牌='{product_ventor}', 单位='{product_unit}', 价格='{product_price}'")

            # 返回完整的9字段结构
            result = {
                'sku_number': sku,
                'product_name': product_name,
                'product_ventor': product_ventor,
                'url': url,
                'product_unit': product_unit,
                'product_model': product_model,
                'Null1': '-',
                'Null2': '-',
                'product_price': product_price,
                'platform': '领先未来(新)',
                'status': 'success'
            }

            logger.debug(f"SKU {sku} - 数据提取成功，品牌: '{product_ventor}', 型号: '{product_model}'")
            return result

        except Exception as e:
            logger.error(f"SKU {sku} - JSON数据提取异常: {str(e)}")
            return self._create_error_result(sku, f'JSON数据提取异常: {str(e)}')

    def _create_error_result(self, sku: str, error_message: str) -> Dict:
        """创建错误结果 - 9字段结构"""
        return {
            'sku_number': sku,
            'product_name': '获取失败',
            'product_ventor': '获取失败',
            'url': self.base_url.format(sku=sku),
            'product_unit': '获取失败',
            'product_model': '获取失败',
            'Null1': '-',
            'Null2': '-',
            'product_price': '获取失败',
            'platform': '领先未来(新)',
            'status': 'error',
            'error': error_message
        }

    async def scrape_data_stream(self, query_content: str, playwright_mode: bool = None):
        """
        流式数据采集 - 实时返回每个SKU的采集结果
        """
        try:
            # 从配置文件读取Playwright模式设置
            use_playwright = getattr(self.settings, 'LXWL_NEW_USE_PLAYWRIGHT', False)
            logger.info(f"开始流式采集领先未来(新)平台数据 - Playwright模式: {use_playwright}")

            skus = self._parse_query_content(query_content)

            if not skus:
                yield {
                    "type": "error",
                    "message": "未找到有效的SKU编码"
                }
                return

            # 根据配置选择采集方式
            if use_playwright:
                # 确保cookies已初始化
                if not self.playwright_manager.cookies_initialized:
                    logger.info("流式采集需要初始化cookies...")
                    success = await self.playwright_manager.initialize_cookies(
                        target_url="https://www.lxwlmall.com",
                        session_obj=self.session
                    )
                    if not success:
                        yield {
                            "type": "error",
                            "message": "无法获取初始cookies"
                        }
                        return

            # 并发处理SKU并实时返回结果
            total = len(skus)
            success_count = 0
            failed_count = 0

            # 发送初始进度信息
            yield {
                "type": "progress",
                "current": 0,
                "total": total,
                "message": f"开始并发采集 {total} 个SKU..."
            }

            # 创建信号量控制并发数
            semaphore = asyncio.Semaphore(self.concurrent_threads)

            # 使用asyncio.Queue来收集并发任务的结果
            result_queue = asyncio.Queue()
            completed_count = 0

            async def process_single_sku_stream(index: int, sku: str):
                """流式处理单个SKU的采集任务"""
                nonlocal success_count, failed_count, completed_count

                async with semaphore:
                    try:
                        # 采集单个SKU数据
                        result = await self._scrape_single_product(sku, use_cookies=use_playwright)
                        result['index'] = index
                        result['original_order'] = index

                        if result['status'] == 'success':
                            success_count += 1
                        else:
                            failed_count += 1

                        # 将结果放入队列
                        await result_queue.put({
                            "type": "result",
                            "data": result,
                            "index": index
                        })

                    except Exception as e:
                        failed_count += 1
                        error_result = self._create_error_result(sku, f'流式采集异常: {str(e)}')
                        error_result['index'] = index
                        error_result['original_order'] = index

                        await result_queue.put({
                            "type": "result",
                            "data": error_result,
                            "index": index
                        })

            # 创建所有任务
            tasks = [process_single_sku_stream(i, sku) for i, sku in enumerate(skus)]

            # 启动所有并发任务
            async def run_all_tasks():
                await asyncio.gather(*tasks)
                # 所有任务完成后，发送结束标记
                await result_queue.put({"type": "done"})

            # 启动任务执行
            task_runner = asyncio.create_task(run_all_tasks())

            # 实时从队列中获取结果并返回
            while True:
                try:
                    # 等待结果，设置超时避免无限等待
                    result = await asyncio.wait_for(result_queue.get(), timeout=1.0)

                    if result["type"] == "done":
                        break

                    # 更新计数器
                    completed_count += 1
                    result["current"] = completed_count
                    result["total"] = total
                    result["success_count"] = success_count
                    result["failed_count"] = failed_count

                    yield result

                except asyncio.TimeoutError:
                    # 检查任务是否完成
                    if task_runner.done():
                        break
                    continue

            # 确保任务完成
            await task_runner

            # 发送完成信息
            yield {
                "type": "complete",
                "total": total,
                "success_count": success_count,
                "failed_count": failed_count,
                "message": f"流式采集完成，成功: {success_count}/{total}"
            }

        except Exception as e:
            logger.error(f"流式数据采集异常: {str(e)}")
            yield {
                "type": "error",
                "message": f"流式数据采集异常: {str(e)}"
            }

    async def scrape_single_sku(self, sku: str, playwright_mode: bool = None) -> Dict:
        """
        采集单个SKU数据（用于重试功能）
        """
        try:
            # 从配置文件读取Playwright模式设置
            use_playwright = getattr(self.settings, 'LXWL_NEW_USE_PLAYWRIGHT', False)
            logger.info(f"开始采集单个SKU: {sku}, Playwright模式: {use_playwright} (配置驱动)")

            # 根据配置选择采集方式
            if use_playwright:
                # 确保cookies已初始化
                if not self.playwright_manager.cookies_initialized:
                    logger.info("单个SKU采集需要初始化cookies...")
                    success = await self.playwright_manager.initialize_cookies(
                        target_url="https://www.lxwlmall.com",
                        session_obj=self.session
                    )
                    if not success:
                        return self._create_error_result(sku, '无法获取初始cookies')

                # 使用Playwright模式采集
                result = await self._scrape_single_product(sku, use_cookies=True)
            else:
                # 使用直接模式采集
                result = await self._scrape_single_product(sku, use_cookies=False)

            logger.info(f"单个SKU采集完成 - SKU: {sku}, 状态: {result.get('status')}")
            return result

        except Exception as e:
            logger.error(f"单个SKU采集异常: {str(e)}")
            return self._create_error_result(sku, f'单个SKU采集异常: {str(e)}')


# 创建全局服务实例
lxwl_new_service = LxwlNewService()
