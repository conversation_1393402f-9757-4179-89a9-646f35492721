/**
 * 数据采集模块JavaScript
 * 处理4个数据采集模块的查询和显示功能
 */

// 数据采集管理器
class DataCollectionManager {
    constructor() {
        this.modules = {
            'xqpz': {
                name: '需求凭证',
                endpoint: '/api/v1/data/xqpz/query',
                columns: [
                    { key: 'credential_number', title: '凭证编号', sortable: true },
                    { key: 'title', title: '标题', sortable: true },
                    { key: 'status', title: '状态', render: (value) => Utils.formatStatus(value) },
                    { key: 'priority', title: '优先级', render: (value) => Utils.formatStatus(value) },
                    { key: 'department', title: '部门', sortable: true },
                    { key: 'created_at', title: '创建时间', render: (value) => Utils.formatDate(value) }
                ]
            },
            'xqd': {
                name: '需求单',
                endpoint: '/api/v1/data/xqd/query',
                columns: [
                    { key: 'order_number', title: '需求单号', sortable: true },
                    { key: 'title', title: '标题', sortable: true },
                    { key: 'status', title: '状态', render: (value) => Utils.formatStatus(value) },
                    { key: 'priority', title: '优先级', render: (value) => Utils.formatStatus(value) },
                    { key: 'amount', title: '金额', render: (value) => `¥${value?.toLocaleString() || 0}` },
                    { key: 'applicant', title: '申请人', sortable: true },
                    { key: 'created_at', title: '创建时间', render: (value) => Utils.formatDate(value) }
                ]
            },

            'product': {
                name: '商品状态',
                endpoint: '/api/v1/data/product/query',
                columns: [
                    { key: 'product_id', title: '商品ID', sortable: true },
                    { key: 'name', title: '商品名称', sortable: true },
                    { key: 'status', title: '状态', render: (value) => Utils.formatStatus(value) },
                    { key: 'price', title: '价格', render: (value) => `¥${value?.toLocaleString() || 0}` },
                    { key: 'stock', title: '库存', sortable: true },
                    { key: 'category', title: '类别', sortable: true },
                    { key: 'supplier', title: '供应商', sortable: true },
                    { key: 'updated_at', title: '更新时间', render: (value) => Utils.formatDate(value) }
                ]
            }
        };
        
        this.tableManagers = {};
        this.initializeForms();
    }

    /**
     * 初始化表单
     */
    initializeForms() {
        Object.keys(this.modules).forEach(moduleKey => {
            const form = document.getElementById(`${moduleKey}-form`);
            if (form) {
                form.addEventListener('submit', (e) => this.handleFormSubmit(e, moduleKey));
                
                // 创建表格管理器
                this.tableManagers[moduleKey] = new ModuleDataTableManager(
                    `${moduleKey}-results`,
                    moduleKey,
                    this
                );
            }
        });
    }

    /**
     * 处理表单提交
     */
    async handleFormSubmit(event, moduleKey) {
        event.preventDefault();

        // 检查ELECTRON权限
        if (!this.checkElectronAccess()) {
            return;
        }

        const form = event.target;
        const formData = new FormData(form);
        const queryText = formData.get('query_text');

        if (!queryText.trim()) {
            authManager.showNotification('请输入查询条件', 'warning');
            return;
        }

        // 保存查询条件到本地存储
        localStorageManager.saveQueryCondition(moduleKey, {
            query_text: queryText,
            timestamp: new Date().toISOString()
        });

        // 执行查询
        await this.executeQuery(moduleKey, {
            query_text: queryText,
            page: 1,
            page_size: 20
        });
    }

    /**
     * 检查ELECTRON访问权限
     */
    checkElectronAccess() {
        if (typeof authManager === 'undefined' || !authManager.userInfo) {
            authManager.showNotification('请先登录', 'warning');
            return false;
        }

        if (!authManager.userInfo.electron_access) {
            authManager.showNotification('需要电子超市平台权限才能进行数据采集', 'error');
            return false;
        }

        return true;
    }

    /**
     * 执行查询
     */
    async executeQuery(moduleKey, queryData) {
        const module = this.modules[moduleKey];
        const resultsContainer = document.getElementById(`${moduleKey}-results`);
        
        try {
            // 显示加载状态
            LoadingManager.show(resultsContainer, `正在查询${module.name}...`);
            
            const response = await fetch(module.endpoint, {
                method: 'POST',
                headers: authManager.getAuthHeaders(),
                body: JSON.stringify(queryData)
            });

            if (response.status === 401) {
                authManager.handleTokenExpiry();
                return;
            }

            const result = await response.json();
            
            // 渲染结果
            this.tableManagers[moduleKey].render(result, module.columns);

            // 更新记录数量显示
            this.updateRecordCount(moduleKey, result);

            if (result.success) {
                authManager.showNotification(`${module.name}查询完成，找到 ${result.pagination?.total || 0} 条记录`, 'success');
            } else {
                authManager.showNotification(`${module.name}查询失败：${result.message}`, 'error');
            }
            
        } catch (error) {
            console.error(`${module.name}查询失败:`, error);
            authManager.showNotification(`${module.name}查询失败，请稍后重试`, 'error');
            
            // 显示错误状态
            this.tableManagers[moduleKey].renderError('网络连接失败，请稍后重试');
        }
    }

    /**
     * 更新记录数量显示
     */
    updateRecordCount(moduleKey, result) {
        const countElement = document.getElementById(`${moduleKey}-count`);
        if (countElement) {
            if (result.success && result.pagination) {
                const total = result.pagination.total || 0;
                countElement.textContent = `${total} 条记录`;
                countElement.className = total > 0 ? 'badge bg-primary' : 'badge bg-secondary';
            } else {
                countElement.textContent = '查询失败';
                countElement.className = 'badge bg-danger';
            }
        }
    }

    /**
     * 获取模块信息
     */
    getModule(moduleKey) {
        return this.modules[moduleKey];
    }
}

// 扩展的数据表格管理器，支持分页和排序
class ModuleDataTableManager extends DataTableManager {
    constructor(containerId, moduleKey, dataCollectionManager) {
        super(containerId);
        this.moduleKey = moduleKey;
        this.dataCollectionManager = dataCollectionManager;
        this.currentQuery = null;
    }

    /**
     * 页面变化处理
     */
    onPageChange(page) {
        if (this.currentQuery) {
            this.currentQuery.page = page;
            this.dataCollectionManager.executeQuery(this.moduleKey, this.currentQuery);
        }
    }

    /**
     * 排序处理
     */
    sortBy(column) {
        super.sortBy(column);
        
        if (this.currentQuery) {
            this.currentQuery.sort_column = this.sortColumn;
            this.currentQuery.sort_direction = this.sortDirection;
            this.currentQuery.page = 1; // 排序后回到第一页
            this.dataCollectionManager.executeQuery(this.moduleKey, this.currentQuery);
        }
    }

    /**
     * 重写render方法以保存当前查询
     */
    render(response, columns, options = {}) {
        // 保存当前查询参数
        if (response.success && response.data) {
            this.currentQuery = {
                query_text: this.getCurrentQueryText(),
                page: response.pagination?.page || 1,
                page_size: response.pagination?.page_size || 20,
                sort_column: this.sortColumn,
                sort_direction: this.sortDirection
            };
        }
        
        super.render(response, columns, options);
    }

    /**
     * 获取当前查询文本
     */
    getCurrentQueryText() {
        const form = document.getElementById(`${this.moduleKey}-form`);
        if (form) {
            const textarea = form.querySelector('textarea[name="query_text"]');
            return textarea ? textarea.value : '';
        }
        return '';
    }
}

// 创建全局数据采集管理器实例
let dataCollectionManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    dataCollectionManager = new DataCollectionManager();
    console.log('数据采集模块已初始化');
});
